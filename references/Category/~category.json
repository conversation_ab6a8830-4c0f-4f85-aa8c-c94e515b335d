{"name": "Category", "plural": "Categories", "base": "Model", "idInjection": false, "strict": false, "mixins": {}, "options": {}, "properties": {"name": {"id": true, "type": "String", "max": 32}, "description": {"type": "String"}, "parent": {"type": "String", "description": "The last category name"}, "children": {"type": "array", "description": "An array of category names directly after the current category"}, "depth": {"type": "number", "description": "The depth of the category based on its parents."}}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": {}}