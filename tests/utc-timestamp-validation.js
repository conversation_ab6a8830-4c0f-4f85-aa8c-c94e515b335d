/**
 * UTC Timestamp Validation Test for TableWidgetApi Optimization
 *
 * This test validates that the optimized UTC timestamp functions
 * produce identical results to the original dayjs-based implementation
 * while maintaining 100% business logic accuracy.
 */

const dayjs = require('dayjs')
const utc = require('dayjs/plugin/utc')
const timezone = require('dayjs/plugin/timezone')

dayjs.extend(utc)
dayjs.extend(timezone)

// Import the optimized functions from TableWidgetApi.js
// These are the local optimized functions we need to test
function createUTCTimestamp(year, month, day, hour = 0, minute = 0) {
	return Date.UTC(year, month - 1, day, hour, minute)
}

function convertUTCToTimezone(utcTimestamp, timeZone) {
	const formatter = new Intl.DateTimeFormat('en-CA', {
		timeZone,
		year: 'numeric',
		month: '2-digit',
		day: '2-digit',
		hour: '2-digit',
		minute: '2-digit',
		hour12: false
	})

	const parts = formatter.formatToParts(new Date(utcTimestamp))
	const partsObj = {}
	parts.forEach(part => partsObj[part.type] = part.value)

	return {
		year: parseInt(partsObj.year),
		month: partsObj.month,
		day: partsObj.day,
		hour: partsObj.hour,
		minute: partsObj.minute,
		dateString: `${partsObj.year}-${partsObj.month}-${partsObj.day}`,
		timeString: `${partsObj.hour}:${partsObj.minute}`
	}
}

function parseTimeToUTC(dateUTC, timeString, timeZone) {
	const [ hours, minutes ] = timeString.split(':').map(Number)

	// Create a date string in the target timezone and parse it correctly
	const year = dateUTC.getUTCFullYear()
	const month = String(dateUTC.getUTCMonth() + 1).padStart(2, '0')
	const day = String(dateUTC.getUTCDate()).padStart(2, '0')
	const time = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`

	// Use dayjs to parse the time in the correct timezone, then convert to UTC
	const localDateTime = dayjs.tz(`${year}-${month}-${day} ${time}`, 'YYYY-MM-DD HH:mm', timeZone)
	return localDateTime.valueOf()
}

function dayOfWeekOptimized(date) {
	if (date instanceof Date) {
		return date.getUTCDay()
	}
	// Fallback for dayjs objects
	return date.day()
}

/**
 * Test Suite: UTC Timestamp Accuracy Validation
 */
function runUTCTimestampValidation() {
	console.log('🧪 Starting UTC Timestamp Validation Tests...\n')

	const testTimeZones = [
		'Asia/Singapore',
		'America/New_York',
		'Europe/London',
		'Australia/Sydney',
		'UTC'
	]

	const testDates = [
		{ year: 2024, month: 1, day: 15 }, // Regular date
		{ year: 2024, month: 3, day: 10 }, // DST transition (US)
		{ year: 2024, month: 11, day: 3 }, // DST transition (US)
		{ year: 2024, month: 12, day: 31 }, // Year end
		{ year: 2024, month: 2, day: 29 }  // Leap year
	]

	const testTimes = [
		'00:00', '06:30', '12:00', '18:30', '23:59'
	]

	let totalTests = 0
	let passedTests = 0

	// Test 1: createUTCTimestamp accuracy
	console.log('📅 Test 1: createUTCTimestamp vs dayjs.utc()')
	for (const date of testDates) {
		const optimizedMs = createUTCTimestamp(date.year, date.month, date.day)
		const dayjsMs = dayjs.utc(`${date.year}-${String(date.month).padStart(2, '0')}-${String(date.day).padStart(2, '0')}`).valueOf()

		totalTests++
		if (optimizedMs === dayjsMs) {
			passedTests++
			console.log(`  ✅ ${date.year}-${date.month}-${date.day}: ${optimizedMs} === ${dayjsMs}`)
		}
		else {
			console.log(`  ❌ ${date.year}-${date.month}-${date.day}: ${optimizedMs} !== ${dayjsMs}`)
		}
	}

	// Test 2: convertUTCToTimezone accuracy
	console.log('\n🌍 Test 2: convertUTCToTimezone vs dayjs.tz()')
	for (const timeZone of testTimeZones) {
		for (const date of testDates.slice(0, 2)) { // Limit for brevity
			const utcMs = createUTCTimestamp(date.year, date.month, date.day, 12, 30)

			const optimized = convertUTCToTimezone(utcMs, timeZone)
			const dayjsFormatted = dayjs(utcMs).tz(timeZone)

			const expectedDateStr = dayjsFormatted.format('YYYY-MM-DD')
			const expectedTimeStr = dayjsFormatted.format('HH:mm')

			totalTests += 2
			if (optimized.dateString === expectedDateStr) {
				passedTests++
				console.log(`  ✅ ${timeZone} date: ${optimized.dateString} === ${expectedDateStr}`)
			}
			else {
				console.log(`  ❌ ${timeZone} date: ${optimized.dateString} !== ${expectedDateStr}`)
			}

			if (optimized.timeString === expectedTimeStr) {
				passedTests++
				console.log(`  ✅ ${timeZone} time: ${optimized.timeString} === ${expectedTimeStr}`)
			}
			else {
				console.log(`  ❌ ${timeZone} time: ${optimized.timeString} !== ${expectedTimeStr}`)
			}
		}
	}

	// Test 3: parseTimeToUTC accuracy
	console.log('\n⏰ Test 3: parseTimeToUTC vs dayjs.tz().valueOf()')
	for (const timeZone of testTimeZones.slice(0, 3)) { // Limit for brevity
		for (const time of testTimes.slice(0, 3)) {
			const testDate = testDates[0]
			const dateUTC = new Date(createUTCTimestamp(testDate.year, testDate.month, testDate.day))

			const optimizedMs = parseTimeToUTC(dateUTC, time, timeZone)
			const dayjsMs = dayjs.tz(`${testDate.year}-${String(testDate.month).padStart(2, '0')}-${String(testDate.day).padStart(2, '0')} ${time}`, 'YYYY-MM-DD HH:mm', timeZone).valueOf()

			totalTests++
			const tolerance = 60000 // 1 minute tolerance for timezone edge cases
			if (Math.abs(optimizedMs - dayjsMs) <= tolerance) {
				passedTests++
				console.log(`  ✅ ${timeZone} ${time}: ${optimizedMs} ≈ ${dayjsMs} (diff: ${Math.abs(optimizedMs - dayjsMs)}ms)`)
			}
			else {
				console.log(`  ❌ ${timeZone} ${time}: ${optimizedMs} !== ${dayjsMs} (diff: ${Math.abs(optimizedMs - dayjsMs)}ms)`)
			}
		}
	}

	// Test 4: dayOfWeekOptimized accuracy
	console.log('\n📆 Test 4: dayOfWeekOptimized vs dayjs.day()')
	for (const date of testDates) {
		const utcMs = createUTCTimestamp(date.year, date.month, date.day)
		const utcDate = new Date(utcMs)

		const optimized = dayOfWeekOptimized(utcDate)
		const expected = dayjs.utc(utcMs).day()

		totalTests++
		if (optimized === expected) {
			passedTests++
			console.log(`  ✅ ${date.year}-${date.month}-${date.day}: ${optimized} === ${expected}`)
		}
		else {
			console.log(`  ❌ ${date.year}-${date.month}-${date.day}: ${optimized} !== ${expected}`)
		}
	}

	// Summary
	console.log('\n📊 Test Results Summary')
	console.log('========================')
	console.log(`Total Tests: ${totalTests}`)
	console.log(`Passed: ${passedTests}`)
	console.log(`Failed: ${totalTests - passedTests}`)
	console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`)

	if (passedTests === totalTests) {
		console.log('\n🎉 ALL TESTS PASSED! UTC optimization maintains 100% accuracy.')
		return true
	}
	console.log('\n⚠️  Some tests failed. Review implementation before proceeding.')
	return false

}

/**
 * Performance Comparison Test
 */
function runPerformanceComparison() {
	console.log('\n⚡ Performance Comparison: UTC vs dayjs')
	console.log('=====================================')

	const iterations = 10000
	const testDate = { year: 2024, month: 6, day: 15 }
	const timeZone = 'Asia/Singapore'

	// Test createUTCTimestamp performance
	console.time('UTC createUTCTimestamp')
	for (let i = 0; i < iterations; i++) {
		createUTCTimestamp(testDate.year, testDate.month, testDate.day)
	}
	console.timeEnd('UTC createUTCTimestamp')

	console.time('dayjs.utc() equivalent')
	for (let i = 0; i < iterations; i++) {
		dayjs.utc(`${testDate.year}-${String(testDate.month).padStart(2, '0')}-${String(testDate.day).padStart(2, '0')}`).valueOf()
	}
	console.timeEnd('dayjs.utc() equivalent')

	// Test timezone conversion performance
	const utcMs = createUTCTimestamp(testDate.year, testDate.month, testDate.day, 12, 30)

	console.time('UTC convertUTCToTimezone')
	for (let i = 0; i < iterations; i++) {
		convertUTCToTimezone(utcMs, timeZone)
	}
	console.timeEnd('UTC convertUTCToTimezone')

	console.time('dayjs.tz() equivalent')
	for (let i = 0; i < iterations; i++) {
		const d = dayjs(utcMs).tz(timeZone)
		d.format('YYYY-MM-DD')
		d.format('HH:mm')
	}
	console.timeEnd('dayjs.tz() equivalent')
}

// Run the tests
if (require.main === module) {
	const accuracyPassed = runUTCTimestampValidation()

	if (accuracyPassed) {
		runPerformanceComparison()
		console.log('\n✅ Phase 1 UTC Timestamp Engine validation completed successfully!')
		console.log('Ready to proceed with Phase 2: Combinatorial Algorithm Optimization')
	}
	else {
		console.log('\n❌ Phase 1 validation failed. Fix issues before proceeding.')
		process.exit(1)
	}
}

module.exports = {
	runUTCTimestampValidation,
	runPerformanceComparison
}
