const assert = require('assert'),
	{ openUntil } = require('@perkd/utils')

const AVAILABLE = [
	{
		id: 't01',
		hours: {
			periods: [
				{ open: { day: 7, time: '12:00' }, close: { day: 7, time: '23:59' } },
				{ open: { day: 6, time: '12:00' }, close: { day: 6, time: '23:59' } },
				{ open: { day: 5, time: '12:00' }, close: { day: 5, time: '23:59' } },
				{ open: { day: 4, time: '12:00' }, close: { day: 4, time: '23:59' } },
				{ open: { day: 3, time: '12:00' }, close: { day: 3, time: '23:59' } },
				{ open: { day: 2, time: '12:00' }, close: { day: 2, time: '23:59' } },
				{ open: { day: 1, time: '12:00' }, close: { day: 1, time: '23:59' } }
			]
		}
	},
	{
		id: 't02',
		hours: {
			periods: [
				{ open: { day: 7, time: '12:00' }, close: { day: 7, time: '23:59' } },
				{ open: { day: 6, time: '12:00' }, close: { day: 6, time: '23:59' } },
				{ open: { day: 5, time: '12:00' }, close: { day: 5, time: '23:59' } },
				{ open: { day: 4, time: '12:00' }, close: { day: 4, time: '23:59' } },
				{ open: { day: 3, time: '12:00' }, close: { day: 3, time: '23:59' } },
				{ open: { day: 2, time: '12:00' }, close: { day: 2, time: '23:59' } },
				{ open: { day: 1, time: '12:00' }, close: { day: 1, time: '23:59' } }
			]
		}
	}
]

describe('Prioritize', () => {
	it('Should sort available per proritized ordering', async () => {
		const from = new Date('2023-01-01T06:00:00.000Z'),
			to = new Date('2023-01-01T06:59:59.999Z'),
			occupiedAfter = [
				{ resourceId: 't01', startTime: new Date('2023-01-01T07:00:00.000Z'), endTime: new Date('2023-01-01T07:59:59.999Z') },
				{ resourceId: 't01', startTime: new Date('2023-01-01T12:00:00.000Z'), endTime: new Date('2023-01-01T12:59:59.999Z') },
				{ resourceId: 't02', startTime: new Date('2023-01-01T08:00:00.000Z'), endTime: new Date('2023-01-01T08:59:59.999Z') },
				{ resourceId: 't02', startTime: new Date('2023-01-01T11:00:00.000Z'), endTime: new Date('2023-01-01T11:59:59.999Z') }
			],
			res = prioritize(from, to, AVAILABLE, occupiedAfter),
			expected = [
				{
					id: 't02',
					hours: {
						periods: [
							{ open: { day: 7, time: '12:00' }, close: { day: 7, time: '23:59' } },
							{ open: { day: 6, time: '12:00' }, close: { day: 6, time: '23:59' } },
							{ open: { day: 5, time: '12:00' }, close: { day: 5, time: '23:59' } },
							{ open: { day: 4, time: '12:00' }, close: { day: 4, time: '23:59' } },
							{ open: { day: 3, time: '12:00' }, close: { day: 3, time: '23:59' } },
							{ open: { day: 2, time: '12:00' }, close: { day: 2, time: '23:59' } },
							{ open: { day: 1, time: '12:00' }, close: { day: 1, time: '23:59' } }
						]
					}
				},
				{
					id: 't01',
					hours: {
						periods: [
							{ open: { day: 7, time: '12:00' }, close: { day: 7, time: '23:59' } },
							{ open: { day: 6, time: '12:00' }, close: { day: 6, time: '23:59' } },
							{ open: { day: 5, time: '12:00' }, close: { day: 5, time: '23:59' } },
							{ open: { day: 4, time: '12:00' }, close: { day: 4, time: '23:59' } },
							{ open: { day: 3, time: '12:00' }, close: { day: 3, time: '23:59' } },
							{ open: { day: 2, time: '12:00' }, close: { day: 2, time: '23:59' } },
							{ open: { day: 1, time: '12:00' }, close: { day: 1, time: '23:59' } }
						]
					}
				}
			]

		assert.deepStrictEqual(res, expected)
	})

	it('Should prioritize non-occupied resources first', async () => {
		const from = new Date('2023-01-01T06:00:00.000Z'),
			to = new Date('2023-01-01T06:59:59.999Z'),
			occupiedAfter = [
				{ resourceId: 't02', startTime: new Date('2023-01-01T08:00:00.000Z'), endTime: new Date('2023-01-01T08:59:59.999Z') },
				{ resourceId: 't02', startTime: new Date('2023-01-01T11:00:00.000Z'), endTime: new Date('2023-01-01T11:59:59.999Z') },
				{ resourceId: 't03', startTime: new Date('2023-01-01T07:00:00.000Z'), endTime: new Date('2023-01-01T07:59:59.999Z') },
				{ resourceId: 't03', startTime: new Date('2023-01-01T12:00:00.000Z'), endTime: new Date('2023-01-01T12:59:59.999Z') }
			],
			res = prioritize(from, to, AVAILABLE, occupiedAfter),
			expected = AVAILABLE

		assert.deepStrictEqual(res, expected)
	})

	it('No change when occupiedAfter is empty', async () => {
		const from = new Date('2023-01-01T06:00:00.000Z'),
			to = new Date('2023-01-01T06:59:59.999Z'),
			occupiedAfter = [],
			res = prioritize(from, to, AVAILABLE, occupiedAfter),
			expected = AVAILABLE

		assert.deepStrictEqual(res, expected)
	})
})

function prioritize(from, to, available, occupiedAfter) {
	const ids = available.map(r => r.id),
		hours = available.reduce((res, resource) => {
			const { hours } = resource

			res.periods.push(...hours.periods)
			hours.specific && res.specific.push(...hours.specific)
			return res
		}, { periods: [], specific: [] }),
		endOfDay = openUntil(hours, to),
		prioritized = []

	if (!occupiedAfter.length) return available

	for (const id of ids) {
		const [ earliest = {} ] = occupiedAfter.filter(r => r.resourceId === id),
			{ startTime: next = endOfDay } = earliest

		prioritized.push({ id, next })
	}

	prioritized.sort((a, b) => b.next - a.next)		// latest first

	// sort available per proritized ordering
	return available.sort((a, b) => prioritized.findIndex(r => r.id === a.id) - prioritized.findIndex(r => r.id === b.id))
}
