# MCP Error Handling Migration Plan

## Overview
Migrate from deprecated `withErrorHandling()` to new specific error handling functions:
- `withToolErrorHandling()` for MCP tools
- `withResourceErrorHandling()` for MCP resources

## Migration Checklist

### Phase 1: Tool Files Migration (withToolErrorHandling)
- [x] `server/mcp/tools/products.js`
  - [x] Update import statement (line 8)
  - [x] Update usage in product_search tool (line 212)
  - [x] Update usage in product_category tool (line 147)
- [x] `server/mcp/tools/resources.js`
  - [x] Update import statement (line 8)
- [x] `server/mcp/tools/bundles.js`
  - [x] Update import statement (line 8)
- [x] `server/mcp/tools/variants.js`
  - [x] Update import statement (line 8)

### Phase 2: Tool Utilities Migration (withToolErrorHandling)
- [x] `server/mcp/tools/utils/productUtils.js`
  - [x] Update import statement (line 5)
  - [x] Update registerProductCreateTool function (line 99)
  - [x] Update registerProductListTool function (line 210)
  - [x] Update registerProductGetTool function (line 143)
  - [x] Update registerProductUpdateTool function (line 285)
  - [x] Update registerProductDeleteTool function (line 339)
- [x] `server/mcp/tools/utils/resourceUtils.js`
  - [x] Update import statement (line 5)
  - [x] Update registerResourceCreateTool function (line 72)
  - [x] Update registerResourceListTool function (line 136)
  - [x] Update registerResourceUpdateTool function (line 177)
  - [x] Update registerResourceGetTool function (line 98)
  - [x] Update registerResourceDeleteTool function (line 206)
- [x] `server/mcp/tools/utils/variantUtils.js`
  - [x] Update import statement (line 5)
  - [x] Update registerVariantCreateTool function (line 108)
  - [x] Update registerVariantGetTool function (line 158)
  - [x] Update registerVariantListTool function (line 224)
  - [x] Update registerVariantUpdateTool function (line 303)
  - [x] Update registerVariantDeleteTool function (line 361)
- [x] `server/mcp/tools/utils/bundleUtils.js`
  - [x] Update import statement (line 5)
  - [x] Update registerBundleCreateTool function (line 134)
  - [x] Update registerBundleGetTool function (line 164)
  - [x] Update registerBundleListTool function (line 202)
  - [x] Update registerBundleUpdateTool function (line 255)
  - [x] Update registerBundleDeleteTool function (line 296)

### Phase 3: Resource Files Migration (withResourceErrorHandling)
- [x] `server/mcp/resources/products.js`
  - [x] Update import statement (line 8)
  - [x] Update list method (line 20)
  - [x] Update read method (line 53)
- [x] `server/mcp/resources/resources.js`
  - [x] Update import statement (line 8)
  - [x] Update list method (line 20)
  - [x] Update read method (line 59)
- [x] `server/mcp/resources/variants.js`
  - [x] Update import statement (line 8)
  - [x] Update list method (line 20)
  - [x] Update read method (line 53)
- [x] `server/mcp/resources/bundles.js`
  - [x] Update import statement (line 8)
  - [x] Update list method (line 20)
  - [x] Update read method (line 53)

### Phase 4: Documentation Updates
- [x] `server/mcp/resources/README.md`
  - [x] Update example code (line 33)

### Phase 5: Validation
- [x] Run diagnostics to ensure no compilation errors
- [x] Verify error handling behavior is preserved
- [x] Check that all imports are correctly updated
- [x] Confirm no remaining withErrorHandling usages

## Implementation Notes

### API Compatibility
- Function signatures remain the same: `withXxxErrorHandling(async () => { ... })()`
- Return values and error handling behavior should be preserved
- Only the function name and import changes

### Context-Specific Usage
- **Tools**: Use `withToolErrorHandling()` - generates tool-appropriate error responses
- **Resources**: Use `withResourceErrorHandling()` - generates resource-appropriate error responses

### Safety Measures
- Preserve all existing business logic
- Maintain exact same error handling behavior
- No changes to function parameters or return types
- Only update function names and imports
