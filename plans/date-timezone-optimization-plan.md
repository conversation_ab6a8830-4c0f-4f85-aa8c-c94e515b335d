# Date/Timezone Optimization Plan for Table Availability System

## Executive Summary

This plan outlines an aggressive optimization strategy to eliminate timezone-related performance bottlenecks in the `Product.availabilityTables()` method. Since this is a new service, we'll implement a direct cutover approach with local optimized date functions that can later be promoted to @perkd/utils.

**Expected Performance Improvements:**
- 60-80% reduction in date object creation overhead
- 50-70% reduction in timezone conversion operations
- 40-60% improvement in overall method execution time
- 30-50% reduction in memory usage

**Implementation Strategy:**
- **Direct cutover** - No backward compatibility concerns
- **Local optimization** - Create optimized functions directly in TableWidgetApi.js
- **Fast iteration** - Immediate implementation and testing
- **Future promotion** - Move successful optimizations to @perkd/utils later

## Current Performance Issues

### Critical Bottlenecks Identified
1. **Excessive dayjs.tz() calls in nested loops** (Days × Slots × Party Sizes)
2. **Redundant timezone conversions** in `generateTimeSlots` and `buildAvailabilityByCalendarDate`
3. **Heavy dayjs object creation** for intermediate calculations
4. **Inefficient business hours aggregation** with timezone-aware operations

### Profiling Evidence
- `generateTimeSlots`: Major bottleneck in dayjs timezone operations
- `buildAvailabilityByCalendarDate`: Significant time in `m.isSame` operations
- Multiple timezone conversions of identical data throughout the pipeline

## Phase 1: Local UTC Optimization (Priority: HIGH)
**Timeline: Week 1**
**Risk: LOW**

### 1.1 Create Local Optimized Date Functions in TableWidgetApi.js

#### Local UTC-First Date Utilities (Add to TableWidgetApi.js)
```javascript
// ===== LOCAL OPTIMIZED DATE UTILITIES =====
// These will be moved to @perkd/utils after successful optimization

/**
 * High-performance UTC-based date operations for table availability
 * Located at the top of TableWidgetApi.js for easy access
 */

// Core UTC operations - replace dayjs timezone operations
function createUTCTimestamp(year, month, day, hour = 0, minute = 0) {
  return Date.UTC(year, month - 1, day, hour, minute);
}

function addMinutesUTC(timestampMs, minutes) {
  return timestampMs + (minutes * 60 * 1000);
}

function isSameUTCDay(timestamp1Ms, timestamp2Ms) {
  const date1 = new Date(timestamp1Ms);
  const date2 = new Date(timestamp2Ms);
  return date1.getUTCFullYear() === date2.getUTCFullYear() &&
         date1.getUTCMonth() === date2.getUTCMonth() &&
         date1.getUTCDate() === date2.getUTCDate();
}

// Efficient timezone conversion - replace dayjs.tz() calls
function convertUTCToTimezone(utcTimestamp, timeZone) {
  const formatter = new Intl.DateTimeFormat('en-CA', {
    timeZone,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });

  const parts = formatter.formatToParts(new Date(utcTimestamp));
  const partsObj = {};
  parts.forEach(part => partsObj[part.type] = part.value);

  return {
    year: parseInt(partsObj.year),
    month: partsObj.month,
    day: partsObj.day,
    hour: partsObj.hour,
    minute: partsObj.minute,
    dateString: `${partsObj.year}-${partsObj.month}-${partsObj.day}`,
    timeString: `${partsObj.hour}:${partsObj.minute}`
  };
}

// Parse time string to UTC milliseconds for a given date
function parseTimeToUTC(dateUTC, timeString, timeZone) {
  const [hours, minutes] = timeString.split(':').map(Number);

  // Create date in target timezone, then convert to UTC
  const localDate = new Date();
  localDate.setFullYear(dateUTC.getUTCFullYear(), dateUTC.getUTCMonth(), dateUTC.getUTCDate());
  localDate.setHours(hours, minutes, 0, 0);

  // Convert to UTC considering timezone
  const utcMs = localDate.getTime() - getTimezoneOffsetMs(timeZone, localDate);
  return utcMs;
}

// Get timezone offset in milliseconds (cached for performance)
const timezoneOffsetCache = new Map();
function getTimezoneOffsetMs(timeZone, date) {
  const cacheKey = `${timeZone}-${Math.floor(date.getTime() / (24 * 60 * 60 * 1000))}`;

  if (timezoneOffsetCache.has(cacheKey)) {
    return timezoneOffsetCache.get(cacheKey);
  }

  const utcDate = new Date(date.getTime());
  const localDate = new Date(date.toLocaleString('en-US', { timeZone }));
  const offset = utcDate.getTime() - localDate.getTime();

  timezoneOffsetCache.set(cacheKey, offset);
  return offset;
}

// Optimized business hours aggregation - replace aggregateHours()
function aggregateHoursOptimized(hoursArray, timeZone) {
  if (!hoursArray || hoursArray.length === 0) {
    return { periods: [], specific: [] };
  }

  // Use existing aggregateHours for now, optimize later if needed
  // Focus on time slot generation first as it's the bigger bottleneck
  return aggregateHours(hoursArray);
}

// Day of week calculation - replace dayOfWeek()
function dayOfWeekOptimized(date) {
  if (date instanceof Date) {
    return date.getUTCDay();
  }
  // Fallback for dayjs objects
  return dayOfWeek(date);
}
```

### 1.2 Simple Performance Benchmarking

#### Quick Benchmark (Add to TableWidgetApi.js)
```javascript
// Simple performance tracking - add at top of availabilityTables method
function benchmarkAvailability(placeId) {
  const startTime = performance.now();
  const startMemory = process.memoryUsage().heapUsed;

  return {
    end: () => {
      const endTime = performance.now();
      const endMemory = process.memoryUsage().heapUsed;

      console.log(`[PERF] availabilityTables(${placeId}):`, {
        duration: `${(endTime - startTime).toFixed(2)}ms`,
        memoryDelta: `${((endMemory - startMemory) / 1024 / 1024).toFixed(2)}MB`
      });
    }
  };
}
```

## Phase 2: Direct Function Optimization (Priority: HIGH)
**Timeline: Week 1-2**
**Risk: LOW**

### 2.1 Replace generateTimeSlots with UTC-Optimized Version

#### Current Implementation Issues
- Creates timezone-aware dayjs objects in tight loops
- Performs timezone conversions for every time slot
- Heavy memory allocation for intermediate calculations

#### Direct Replacement in createHelperFunctions (TableWidgetApi.js)
```javascript
// Replace the existing generateTimeSlots function in createHelperFunctions
function createHelperFunctions({ businessHours, now, timeZone, leadTime, timeSlotInterval, minDuration, minPartySize, maxPartySize, settings }) {

  // OPTIMIZED: generateTimeSlots using UTC calculations
  const generateTimeSlots = (date) => {
    const slots = [];

    // Convert dayjs date to UTC timestamp once
    const dayStartMs = createUTCTimestamp(
      date.year(),
      date.month() + 1,
      date.date()
    );

    const timeSlotIntervalMs = (timeSlotInterval || 30) * 60 * 1000;
    const minDurationMs = minDuration * 60 * 1000;
    const leadTimeMs = leadTime * 60 * 1000;
    const nowMs = now.valueOf(); // Get UTC timestamp from dayjs
    const minBookingTimeMs = nowMs + leadTimeMs;

    // Process each business period
    for (const period of businessHours.periods || []) {
      const { open, close } = period;

      // Parse business hours to UTC for this day
      const periodStartMs = parseTimeToUTC(new Date(dayStartMs), open, timeZone);
      const periodEndMs = parseTimeToUTC(new Date(dayStartMs), close, timeZone);

      let currentTimeMs = periodStartMs;

      // Round to next interval boundary
      const intervalOffset = currentTimeMs % timeSlotIntervalMs;
      if (intervalOffset > 0) {
        currentTimeMs += timeSlotIntervalMs - intervalOffset;
      }

      // Generate slots in UTC space
      while (currentTimeMs < periodEndMs) {
        const bookingEndMs = currentTimeMs + minDurationMs;

        // Check constraints in UTC
        if (currentTimeMs >= minBookingTimeMs && bookingEndMs <= periodEndMs) {
          slots.push(currentTimeMs); // Store UTC timestamp
        }

        currentTimeMs += timeSlotIntervalMs;
      }
    }

    // Handle specific date overrides if needed
    const specificHours = businessHours.specific?.find(s =>
      isSameUTCDay(dayStartMs, new Date(s.date).getTime())
    );

    if (specificHours) {
      // Process specific hours similar to regular periods
      // Implementation details...
    }

    return slots;
  };

  // Keep other helper functions unchanged for now
  const getPartySizeRanges = (minPartySize, maxPartySize) => {
    // Existing implementation
  };

  const findAvailableTables = (partySize, timeSlotTimestamp, tablesByCapacity, tableOccupancy, maxPartySize, maxCombined) => {
    // Existing implementation - timeSlotTimestamp is now UTC milliseconds
    // Convert to dayjs only when needed for existing logic
    const timeSlotDayjs = dayjs(timeSlotTimestamp).tz(timeZone);

    // Rest of existing implementation...
  };

  return { generateTimeSlots, getPartySizeRanges, findAvailableTables };
}
```

### 2.2 Replace buildAvailabilityByCalendarDate with UTC-Optimized Version

#### Current Implementation Issues
- Timezone-aware formatting in loops
- Redundant dayjs object creation
- Heavy `isSame` operations

#### Direct Replacement in availabilityTables Method
```javascript
// Replace the existing buildAvailabilityByCalendarDate call in availabilityTables
Product.availabilityTables = async function (placeId) {
  // ... existing validation and setup code ...

  // Generate time slots (now returns UTC timestamps)
  const allRawSlotTimestamps = [];
  for (const businessDay of currentBusinessDays) {
    const slotsForBusinessDay = generateTimeSlots(businessDay);
    allRawSlotTimestamps.push(...slotsForBusinessDay);
  }

  // Remove duplicates and sort
  const uniqueSlotTimestamps = Array.from(new Set(allRawSlotTimestamps)).sort((a, b) => a - b);

  // OPTIMIZED: buildAvailabilityByCalendarDate replacement
  const availabilityByCalendarDate = buildAvailabilityOptimized({
    nowMs: now.valueOf(), // UTC timestamp
    allRawSlotTimestamps: uniqueSlotTimestamps, // UTC timestamps
    partySizeRanges,
    representativeSizes,
    timeZone,
    findAvailableTables: (partySize, timeSlotTimestamp) =>
      findAvailableTables(partySize, timeSlotTimestamp, tablesByCapacity, tableOccupancy, maxPartySize, maxCombined),
    settings
  });

  // ... rest of method unchanged ...
};

// OPTIMIZED: buildAvailabilityByCalendarDate function
function buildAvailabilityOptimized({
  nowMs,
  allRawSlotTimestamps, // UTC timestamps instead of dayjs objects
  partySizeRanges,
  representativeSizes,
  timeZone,
  findAvailableTables,
  settings
}) {
  const availabilityMap = new Map();

  // Get today's date string in timezone for comparison
  const todayParts = convertUTCToTimezone(nowMs, timeZone);
  const todayDateStr = todayParts.dateString;

  for (const slotMs of allRawSlotTimestamps) {
    // Convert to timezone only for display formatting
    const localParts = convertUTCToTimezone(slotMs, timeZone);
    const calendarDateStr = localParts.dateString;
    const timeStr = localParts.timeString;

    if (!availabilityMap.has(calendarDateStr)) {
      // Get day of week name efficiently
      const dayOfWeekNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      const dayOfWeekIndex = new Date(slotMs).getUTCDay();

      availabilityMap.set(calendarDateStr, {
        date: calendarDateStr,
        dayOfWeek: dayOfWeekNames[dayOfWeekIndex],
        isToday: calendarDateStr === todayDateStr, // Simple string comparison
        timeSlots: []
      });
    }

    // Availability calculation using UTC timestamps
    const partySizeAvailability = {};
    const exactAvailability = {};

    for (let i = 0; i < partySizeRanges.length; i++) {
      const range = partySizeRanges[i];
      const representativeSize = representativeSizes[i];

      // Pass UTC timestamp to findAvailableTables
      const availableTables = findAvailableTables(representativeSize, slotMs);

      let availabilityLevel = NONE;
      if (availableTables.length > 5) availabilityLevel = HIGH;
      else if (availableTables.length > 2) availabilityLevel = MEDIUM;
      else if (availableTables.length > 0) availabilityLevel = LOW;

      partySizeAvailability[range.label] = availabilityLevel;
      exactAvailability[range.label] = availableTables.length;
    }

    availabilityMap.get(calendarDateStr).timeSlots.push({
      time: timeStr,
      timestamp: new Date(slotMs).toISOString(), // UTC ISO string
      partySizeAvailability,
      exactAvailability
    });
  }

  // Sort and return (same as before)
  const availabilityByDateArray = Array.from(availabilityMap.values());
  availabilityByDateArray.sort((a, b) => a.date.localeCompare(b.date));

  for (const dayData of availabilityByDateArray) {
    dayData.timeSlots.sort((a, b) => a.time.localeCompare(b.time));
  }

  return availabilityByDateArray;
}
```

## Phase 3: Testing & Validation (Priority: HIGH)
**Timeline: Week 2**
**Risk: LOW**

### 3.1 Simple Performance Testing

#### Before/After Performance Comparison
```javascript
// Add to TableWidgetApi.js for quick testing
async function testOptimizationPerformance(placeId, iterations = 10) {
  console.log(`\n=== Performance Test: ${placeId} (${iterations} iterations) ===`);

  const results = [];

  for (let i = 0; i < iterations; i++) {
    const benchmark = benchmarkAvailability(placeId);
    await Product.availabilityTables(placeId);
    benchmark.end();

    // Collect timing data for analysis
    const duration = performance.now() - benchmark.startTime;
    results.push(duration);
  }

  const avgDuration = results.reduce((a, b) => a + b) / results.length;
  const minDuration = Math.min(...results);
  const maxDuration = Math.max(...results);

  console.log(`Average: ${avgDuration.toFixed(2)}ms`);
  console.log(`Min: ${minDuration.toFixed(2)}ms`);
  console.log(`Max: ${maxDuration.toFixed(2)}ms`);

  return { avgDuration, minDuration, maxDuration };
}
```

### 3.2 Quick Accuracy Validation

#### Simple Result Verification
```javascript
// Add to TableWidgetApi.js for testing
async function validateOptimizationAccuracy(placeId) {
  console.log(`\n=== Accuracy Validation: ${placeId} ===`);

  const result = await Product.availabilityTables(placeId);

  // Basic structure validation
  console.log(`✓ Returned ${result.availability.length} days`);
  console.log(`✓ Date range: ${result.dateRange.start} to ${result.dateRange.end}`);

  let totalSlots = 0;
  for (const day of result.availability) {
    totalSlots += day.timeSlots.length;

    // Validate each time slot has required properties
    for (const slot of day.timeSlots) {
      if (!slot.time || !slot.timestamp || !slot.partySizeAvailability) {
        console.error(`❌ Invalid slot structure in ${day.date}`);
        return false;
      }
    }
  }

  console.log(`✓ Total time slots: ${totalSlots}`);
  console.log(`✓ All slots have required properties`);

  return true;
}

## Phase 4: Future Enhancements (Priority: LOW)
**Timeline: Week 3-4**
**Risk: LOW**

### 4.1 Promote Optimizations to @perkd/utils

#### Extract Successful Optimizations
```javascript
// File: @perkd/utils/src/date-optimized.js
// Move successful local functions to the utils package

/**
 * High-performance UTC-based date operations
 * Extracted from TableWidgetApi.js after successful optimization
 */

export function createUTCTimestamp(year, month, day, hour = 0, minute = 0) {
  return Date.UTC(year, month - 1, day, hour, minute);
}

export function addMinutesUTC(timestampMs, minutes) {
  return timestampMs + (minutes * 60 * 1000);
}

export function isSameUTCDay(timestamp1Ms, timestamp2Ms) {
  const date1 = new Date(timestamp1Ms);
  const date2 = new Date(timestamp2Ms);
  return date1.getUTCFullYear() === date2.getUTCFullYear() &&
         date1.getUTCMonth() === date2.getUTCMonth() &&
         date1.getUTCDate() === date2.getUTCDate();
}

export function convertUTCToTimezone(utcTimestamp, timeZone) {
  // Implementation from successful local optimization
}

// Update existing functions to use optimized versions
export function dayjs(input) {
  // Enhanced dayjs with UTC-first optimizations
}

export function aggregateHours(hoursArray, timeZone) {
  // Enhanced aggregateHours with UTC optimizations
}
```

### 4.2 Additional Performance Optimizations

#### Business Hours Caching
```javascript
// Add to TableWidgetApi.js if business hours aggregation becomes a bottleneck
const businessHoursCache = new Map();

function getCachedBusinessHours(placeId, resourceIds) {
  const cacheKey = `${placeId}:${resourceIds.sort().join(',')}`;
  const cached = businessHoursCache.get(cacheKey);

  if (cached && (Date.now() - cached.timestamp) < 3600000) { // 1 hour TTL
    return cached.data;
  }

  return null;
}

function setCachedBusinessHours(placeId, resourceIds, businessHours) {
  const cacheKey = `${placeId}:${resourceIds.sort().join(',')}`;
  businessHoursCache.set(cacheKey, {
    data: businessHours,
    timestamp: Date.now()
  });
}
```

## Phase 5: Advanced Optimizations (Priority: LOW)
**Timeline: Week 9-12**
**Risk: MEDIUM**

### 5.1 Caching Layer Implementation

#### Business Hours Caching
```javascript
// File: server/lib/cache/business-hours-cache.js

class BusinessHoursCache {
  constructor(app) {
    this.cache = new Map();
    this.ttl = 24 * 60 * 60 * 1000; // 24 hours
  }

  async getAggregatedHours(placeId, resourceIds) {
    const cacheKey = `${placeId}:${resourceIds.sort().join(',')}`;
    const cached = this.cache.get(cacheKey);

    if (cached && (Date.now() - cached.timestamp) < this.ttl) {
      return cached.data;
    }

    // Compute and cache
    const resources = await Resource.find({
      where: { id: { inq: resourceIds } }
    });

    const aggregated = aggregateHoursOptimized(
      resources.map(r => r.hours),
      app.getSettings(LOCALE).timeZone
    );

    this.cache.set(cacheKey, {
      data: aggregated,
      timestamp: Date.now()
    });

    return aggregated;
  }
}
```

#### Availability Matrix Caching
```javascript
// File: server/lib/cache/availability-cache.js

class AvailabilityCache {
  constructor(app) {
    this.cache = new Map();
    this.ttl = 5 * 60 * 1000; // 5 minutes
  }

  async getAvailability(placeId) {
    const cached = this.cache.get(placeId);

    if (cached && (Date.now() - cached.timestamp) < this.ttl) {
      // Update lastUpdated timestamp for freshness
      cached.data.lastUpdated = new Date().toISOString();
      return cached.data;
    }

    // Compute fresh availability
    const availability = await Product.availabilityTablesOptimized(placeId);

    this.cache.set(placeId, {
      data: availability,
      timestamp: Date.now()
    });

    return availability;
  }

  invalidate(placeId) {
    this.cache.delete(placeId);
  }
}
```

### 5.2 Parallel Processing

#### Concurrent Time Slot Generation
```javascript
// File: server/lib/parallel/time-slot-processor.js

const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');

class ParallelTimeSlotProcessor {
  static async generateSlotsParallel(businessDays, businessHours, settings) {
    const numWorkers = Math.min(businessDays.length, require('os').cpus().length);
    const chunkSize = Math.ceil(businessDays.length / numWorkers);

    const workers = [];
    const promises = [];

    for (let i = 0; i < numWorkers; i++) {
      const chunk = businessDays.slice(i * chunkSize, (i + 1) * chunkSize);

      if (chunk.length > 0) {
        const promise = this.createWorker({
          businessDays: chunk,
          businessHours,
          settings
        });

        promises.push(promise);
      }
    }

    const results = await Promise.all(promises);
    return results.flat();
  }

  static createWorker(data) {
    return new Promise((resolve, reject) => {
      const worker = new Worker(__filename, { workerData: data });

      worker.on('message', resolve);
      worker.on('error', reject);
      worker.on('exit', (code) => {
        if (code !== 0) {
          reject(new Error(`Worker stopped with exit code ${code}`));
        }
      });
    });
  }
}

// Worker thread code
if (!isMainThread) {
  const { businessDays, businessHours, settings } = workerData;
  const slots = [];

  for (const day of businessDays) {
    const daySlots = generateTimeSlotsOptimized(day, businessHours, settings);
    slots.push(...daySlots);
  }

  parentPort.postMessage(slots);
}
```

### 5.3 Memory Optimization

#### Object Pooling for Date Operations
```javascript
// File: @perkd/utils/src/date-pool.js

class DateObjectPool {
  constructor(initialSize = 100) {
    this.pool = [];
    this.inUse = new Set();

    // Pre-allocate Date objects
    for (let i = 0; i < initialSize; i++) {
      this.pool.push(new Date());
    }
  }

  acquire() {
    let date = this.pool.pop();

    if (!date) {
      date = new Date();
    }

    this.inUse.add(date);
    return date;
  }

  release(date) {
    if (this.inUse.has(date)) {
      this.inUse.delete(date);
      this.pool.push(date);
    }
  }

  releaseAll() {
    for (const date of this.inUse) {
      this.pool.push(date);
    }
    this.inUse.clear();
  }
}

// Global pool instance
export const datePool = new DateObjectPool();
```

## Implementation Timeline & Milestones

### Week 1: Core Optimization (HIGH Priority)
- [ ] Add local UTC date utility functions to TableWidgetApi.js
- [ ] Replace generateTimeSlots with UTC-optimized version
- [ ] Replace buildAvailabilityByCalendarDate with UTC-optimized version
- [ ] Add simple performance benchmarking
- [ ] **Milestone**: 40-60% improvement in time slot generation

### Week 2: Testing & Validation (HIGH Priority)
- [ ] Performance testing with before/after comparisons
- [ ] Accuracy validation to ensure identical results
- [ ] Edge case testing (DST, midnight crossings, etc.)
- [ ] Load testing with multiple concurrent requests
- [ ] **Milestone**: Production-ready optimized implementation

### Week 3-4: Future Enhancements (LOW Priority)
- [ ] Extract successful optimizations to @perkd/utils
- [ ] Implement business hours caching if needed
- [ ] Additional performance optimizations based on profiling
- [ ] Documentation and knowledge transfer
- [ ] **Milestone**: 60-80% overall performance improvement

## Risk Mitigation

### Technical Risks
1. **Timezone Accuracy**: Simple validation tests to ensure identical results
2. **Performance Regression**: Built-in benchmarking and monitoring
3. **Edge Cases**: Focused testing on DST transitions and boundary conditions

### Business Risks
1. **Service Disruption**: Direct cutover approach suitable for new service
2. **Data Inconsistency**: Validation testing before deployment
3. **Maintenance Overhead**: Local functions are easy to understand and modify

## Success Metrics

### Performance Targets
- **Response Time**: 40-60% reduction in average response time
- **Memory Usage**: 30-50% reduction in peak memory consumption
- **CPU Utilization**: 25-40% reduction in CPU usage
- **Throughput**: 2-3x increase in concurrent request handling

### Quality Targets
- **Accuracy**: 100% identical results compared to original implementation
- **Reliability**: 99.9% uptime during optimization rollout
- **Maintainability**: Clear separation of concerns and comprehensive documentation

## Conclusion

This streamlined optimization plan provides an aggressive but safe approach to eliminating timezone-related performance bottlenecks in the table availability system. By implementing local optimized functions directly in TableWidgetApi.js, we can:

**Immediate Benefits:**
- **Fast Implementation**: Direct cutover without complex infrastructure
- **Easy Testing**: Local functions are simple to test and validate
- **Quick Iteration**: Immediate feedback on performance improvements
- **Low Risk**: New service allows aggressive optimization without backward compatibility concerns

**Future Benefits:**
- **Foundation for Growth**: Successful optimizations can be promoted to @perkd/utils
- **Performance Culture**: Establishes UTC-first optimization patterns
- **Scalability**: Optimized functions will handle increased load efficiently

The optimization represents a fundamental shift from timezone-aware intermediate calculations to UTC-first processing, which aligns with industry best practices and provides significant performance benefits for high-frequency date operations. The local implementation approach allows for rapid iteration and validation before committing to broader architectural changes.
```
