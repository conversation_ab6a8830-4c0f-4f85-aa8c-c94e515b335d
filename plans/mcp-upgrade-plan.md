# MCP Extension Upgrade Plan: v2.1.0 → v3.1.0

> **Objective**: Upgrade Product service MCP implementation from mcp-core v2.1.0 to v3.1.0 with shared factory libraries

## 📊 Current State Analysis

### **Current Implementation (v2.1.0)**
- **Package Version**: `@perkd/mcp-core@^2.1.0`
- **Architecture**: Legacy manual tool registration pattern
- **Files**: 
  - `server/lib/common/modules/mcp.js` (expects mcp-extension.js but file missing)
  - `server/mcp/tools/` (manual tool implementations)
  - `server/mcp/resources/` (manual resource implementations)
  - `server/config/mcp.json` (configuration)

### **Current Tools & Resources**
- **Models**: Product, Variant, Resource, Bundle
- **Tools**: 27 total (manual CRUD + specialized)
  - Product: 7 tools (create, get, list, update, delete, findByCategory, search)
  - Variant: 5 tools (standard CRUD + specialized)
  - Resource: 5 tools (standard CRUD + specialized)  
  - Bundle: 5 tools (standard CRUD + specialized)
  - Prompts: 5 tools
- **Resources**: Schema and examples for each model
- **Schema Format**: JSON Schema (legacy)

### **Technical Debt Identified**
1. **Missing Extension File**: `mcp-extension.js` expected but doesn't exist
2. **Legacy Schema Format**: Using JSON Schema instead of Zod
3. **Manual Tool Registration**: Repetitive CRUD implementations
4. **Inconsistent Error Handling**: Mixed error handling patterns
5. **No Factory Libraries**: Missing shared factory benefits
6. **Protocol Version Mismatch**: Config uses 2025-06-18, code uses 2025-03-26

## 🎯 Target State (v3.1.0)

### **Modern Architecture Benefits**
- **Configuration-Driven**: Single config file generates all tools
- **Shared Factory Libraries**: Automatic CRUD, query, and specialized tools
- **Zod Schemas**: Type-safe validation with better client support
- **Consistent Error Handling**: Shared utilities for all operations
- **Auto-Generated Documentation**: Complete schema and examples
- **91 Tools from 11 Models**: Image service reference pattern

## 📋 Upgrade Plan

### **Phase 1: Foundation Setup** ⚡ **CRITICAL PATH**
**Priority**: P0 | **Risk**: Low | **Effort**: 2 hours

#### **Task 1.1: Update Package Dependency**
```bash
# Update package.json
yarn add @perkd/mcp-core@^3.1.0
```

#### **Task 1.2: Create Service Configuration**
- Create `server/mcp/config/service-config.js`
- Define service-level settings (defaultLimit: 20, maxLimit: 100)
- Configure model mappings for Product, Variant, Resource, Bundle

#### **Task 1.3: Create Base Extension File**
- Create `server/mcp/mcp-extension.js` with `McpExtension` class
- Implement basic factory initialization
- Add minimal error handling

**Validation**: Extension loads without errors, basic tools register

---

### **Phase 2: Model Configuration Migration** ⚡ **CRITICAL PATH**
**Priority**: P0 | **Risk**: Medium | **Effort**: 4 hours

#### **Task 2.1: Product Model Configuration**
- Convert Product tools to factory configuration
- Map existing specialized tools (findByCategory, search)
- Define Zod schemas for all Product fields
- Configure custom operations if needed

#### **Task 2.2: Variant Model Configuration**
- Convert Variant tools to factory configuration
- Define Zod schemas for Variant-specific fields
- Map specialized tools

#### **Task 2.3: Resource Model Configuration**
- Convert Resource tools to factory configuration
- Define Zod schemas for Resource-specific fields
- Map specialized tools

#### **Task 2.4: Bundle Model Configuration**
- Convert Bundle tools to factory configuration
- Define Zod schemas for Bundle-specific fields
- Map specialized tools

**Validation**: All models generate expected tools, schemas validate correctly

---

### **Phase 3: Advanced Features** 
**Priority**: P1 | **Risk**: Low | **Effort**: 3 hours

#### **Task 3.1: Specialized Tools Migration**
- Convert `findByCategory` to specialized tool pattern
- Convert `search` to specialized tool pattern
- Add any missing domain-specific tools

#### **Task 3.2: Documentation Resources**
- Implement auto-generated schema resources
- Implement auto-generated examples
- Add service overview resource

#### **Task 3.3: Interactive Prompts**
- Convert existing prompts to new format
- Add service management guide prompt

**Validation**: All specialized tools work, documentation generates correctly

---

### **Phase 4: Testing & Validation**
**Priority**: P1 | **Risk**: Medium | **Effort**: 2 hours

#### **Task 4.1: Tool Generation Validation**
- Verify expected tool count matches actual
- Test all CRUD operations
- Test specialized tools
- Test error scenarios

#### **Task 4.2: Schema Format Validation**
- Verify Zod schemas render input fields in clients
- Test validation error messages
- Confirm no JSON Schema remnants

#### **Task 4.3: Performance Testing**
- Test tool registration time
- Test memory usage
- Validate pagination limits

**Validation**: All tests pass, performance meets requirements

---

### **Phase 5: Cleanup & Optimization**
**Priority**: P2 | **Risk**: Low | **Effort**: 1 hour

#### **Task 5.1: Legacy Code Removal**
- Remove `server/mcp/tools/` directory
- Remove `server/mcp/resources/` directory  
- Remove `server/mcp/utils/` directory
- Clean up unused imports

#### **Task 5.2: Configuration Optimization**
- Optimize tool generation settings
- Fine-tune pagination defaults
- Review and optimize specialized tools

**Validation**: Clean codebase, no unused files, optimal performance

## 🔧 Implementation Details

### **Expected Tool Generation**
```
Product Service (4 models) → 28+ tools:
- 16 CRUD tools (4 models × 4 operations)
- 8 Query tools (4 models × 2 operations) 
- 4+ Specialized tools (domain-specific)
- Interactive prompts and documentation
```

### **Configuration Structure**
```javascript
const ServiceConfig = {
  serviceConfig: {
    serviceName: 'Product',
    defaultLimit: 20,
    maxLimit: 100
  },
  modelConfigs: {
    Product: {
      toolPrefix: 'product',
      baseSchema: { /* Zod schemas */ },
      specializedTools: [{ /* findByCategory, search */ }]
    }
    // ... other models
  }
};
```

## ⚠️ Risk Assessment

### **High Risk Items**
1. **Schema Migration**: JSON Schema → Zod conversion
   - **Mitigation**: Incremental testing, validation scripts
2. **Tool Compatibility**: Existing clients expect current tool signatures
   - **Mitigation**: Maintain backward compatibility in tool names/params

### **Medium Risk Items**
1. **Configuration Complexity**: Large config files
   - **Mitigation**: Modular config structure, validation
2. **Performance Impact**: Factory overhead
   - **Mitigation**: Performance testing, optimization

### **Low Risk Items**
1. **Documentation Changes**: Auto-generated vs manual
   - **Mitigation**: Review generated docs, adjust as needed

## 📅 Timeline Estimate

- **Phase 1**: 2 hours (Foundation)
- **Phase 2**: 4 hours (Model Migration) 
- **Phase 3**: 3 hours (Advanced Features)
- **Phase 4**: 2 hours (Testing)
- **Phase 5**: 1 hour (Cleanup)

**Total Estimated Time**: 12 hours over 2-3 days

## ✅ Success Criteria

1. **Functional**: All existing tools work with new implementation
2. **Performance**: Tool registration < 5 seconds, memory usage stable
3. **Compatibility**: Existing MCP clients continue to work
4. **Documentation**: Auto-generated docs are complete and accurate
5. **Maintainability**: Configuration-driven, no manual tool code
6. **Testing**: All tests pass, error scenarios handled correctly

## 🚀 Next Steps

1. **Start with Phase 1**: Update dependency and create base files
2. **Incremental Migration**: One model at a time in Phase 2
3. **Continuous Testing**: Validate each phase before proceeding
4. **Reference Implementation**: Use Image service patterns as guide

## 📋 Implementation Commands

### **Phase 1: Foundation Setup**
```bash
# 1. Update package dependency
yarn add @perkd/mcp-core@^3.1.0

# 2. Create directory structure
mkdir -p server/mcp/config

# 3. Verify current MCP module expects mcp-extension.js
# (Already confirmed in server/lib/common/modules/mcp.js line 10)
```

### **Phase 2: Create Configuration Files**
```bash
# Create service configuration
touch server/mcp/config/service-config.js

# Create main extension file
touch server/mcp/mcp-extension.js
```

### **Phase 3: Test and Validate**
```bash
# Start server and test MCP endpoint
NODE_ENV=development node --require dotenv/config .

# Test basic connectivity
curl http://localhost:8081/mcp/status

# Generate test token and initialize session
TOKEN=$(node --require dotenv/config -e "console.log(require('jsonwebtoken').sign({ tenant: { code: 'TEST' }, aud: 'http://localhost:8081' }, process.env.PERKD_SECRET_KEY || 'test-secret-key'))")

# Test tool listing
curl -X POST http://localhost:8081/mcp \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"jsonrpc":"2.0","id":"1","method":"initialize","params":{"clientInfo":{"name":"test","version":"1.0.0"},"supportedProtocolVersions":["2025-06-18"],"capabilities":{},"protocolVersion":"2025-06-18"}}'
```

## 🎯 Critical Success Factors

1. **Maintain Backward Compatibility**: Existing tool names and parameters must remain the same
2. **Incremental Migration**: Test each model configuration before proceeding to the next
3. **Schema Validation**: Ensure Zod schemas render properly in MCP clients
4. **Performance Monitoring**: Track tool registration time and memory usage
5. **Error Handling**: Validate all error scenarios work correctly

## 📊 Expected Outcomes

### **Before (v2.1.0)**
- **Tools**: 27 manually implemented
- **Code**: ~1,500 lines of repetitive CRUD code
- **Schema**: JSON Schema format
- **Maintenance**: High (manual updates for each model)

### **After (v3.1.0)**
- **Tools**: 28+ automatically generated
- **Code**: ~200 lines of configuration
- **Schema**: Zod format with better client support
- **Maintenance**: Low (configuration-driven)

### **Improvement Metrics**
- **Code Reduction**: 85% less manual code
- **Tool Generation**: 100% automated
- **Schema Quality**: Type-safe with validation
- **Development Speed**: 5x faster for new models
