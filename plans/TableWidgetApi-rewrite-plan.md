# TableWidgetApi Complete Rewrite - Progressive Optimization Plan

## Overview
Complete rewrite of TableWidgetApi.js for optimal performance using cutover strategy with no backward compatibility concerns.

## Performance Goals
- Eliminate 89.3% "Unaccounted" time from V8 profiler
- Reduce dayjs object creation by 95%+ in hot paths
- Optimize combinatorial algorithms for better time complexity
- Minimize object property access overhead (LoadIC issues)
- Achieve single-pass processing where possible

## Phase 1: Core Date Engine Rewrite 🔥 **CRITICAL PATH**
**Priority: HIGHEST** - Foundation for all other optimizations

### 1.1 UTC Timestamp Engine
- [ ] Create optimized UTC timestamp utilities
- [ ] Replace all dayjs operations in `generateTimeSlots`
- [ ] Implement timezone conversion only at boundaries
- [ ] Add time slot interval calculations in pure UTC

### 1.2 Business Hours Processing
- [ ] Optimize business hours parsing to UTC ranges
- [ ] Eliminate dayjs from period calculations
- [ ] Cache computed time ranges per day
- [ ] Handle overnight periods efficiently

### 1.3 Date Validation & Constraints
- [ ] Rewrite lead time validation using UTC math
- [ ] Optimize advance booking limit checks
- [ ] Streamline "now" comparisons

**Success Criteria:**
- Zero dayjs objects in `generateTimeSlots`
- 70%+ reduction in date-related CPU time
- All time slots as UTC timestamps

## Phase 2: Combinatorial Algorithm Optimization 🚀
**Priority: HIGH** - Major performance bottleneck

### 2.1 Table Combination Engine
- [ ] Replace recursive `generateCombos` with iterative approach
- [ ] Implement intelligent pruning strategies
- [ ] Add early termination for impossible combinations
- [ ] Optimize adjacency checking algorithm

### 2.2 Adjacency Graph Optimization
- [ ] Pre-compute adjacency maps
- [ ] Use bit manipulation for adjacency checks
- [ ] Cache adjacency validation results
- [ ] Optimize BFS traversal in `areTablesAdjacent`

### 2.3 Capacity Matching
- [ ] Sort tables by optimal capacity matching
- [ ] Implement greedy selection with backtracking
- [ ] Add combination scoring for better selection

**Success Criteria:**
- 80%+ reduction in combinatorial computation time
- Eliminate recursive stack overhead
- Faster adjacency validation

## Phase 3: Availability Processing Engine 🎯
**Priority: HIGH** - Core business logic optimization

### 3.1 Single-Pass Availability Builder
- [ ] Replace `buildAvailabilityByCalendarDate` with optimized version
- [ ] Batch process all time slots simultaneously
- [ ] Eliminate redundant table availability checks
- [ ] Cache availability calculations per party size

### 3.2 Table Occupancy Optimization
- [ ] Pre-process occupancy data into efficient lookup structures
- [ ] Use interval trees for overlap detection
- [ ] Batch occupancy checks across time slots
- [ ] Optimize `isTableAvailable` function

### 3.3 Party Size Range Processing
- [ ] Optimize representative size calculations
- [ ] Cache party size availability results
- [ ] Batch process multiple party sizes

**Success Criteria:**
- Single-pass processing for all availability data
- 60%+ reduction in table lookup operations
- Optimized memory usage patterns

## Phase 4: Data Structure & Memory Optimization 💾
**Priority: MEDIUM** - Performance fine-tuning

### 4.1 Object Shape Optimization
- [ ] Standardize object structures for V8 optimization
- [ ] Eliminate dynamic property additions
- [ ] Use consistent property ordering
- [ ] Optimize for inline caching (LoadIC)

### 4.2 Memory Allocation Reduction
- [ ] Reuse objects where possible
- [ ] Eliminate temporary array allocations
- [ ] Use object pooling for frequent allocations
- [ ] Optimize garbage collection pressure

### 4.3 Data Access Patterns
- [ ] Optimize hot path property access
- [ ] Use Maps/Sets for O(1) lookups where beneficial
- [ ] Minimize object traversal depth

**Success Criteria:**
- Stable object shapes in hot paths
- Reduced memory allocations
- Improved V8 inline caching

## Phase 5: Integration & Validation 🔍
**Priority: MEDIUM** - Ensure correctness and performance

### 5.1 Performance Validation
- [ ] Implement comprehensive benchmarking
- [ ] Compare before/after V8 profiler results
- [ ] Validate memory usage improvements
- [ ] Test with realistic data volumes

### 5.2 Functional Validation
- [ ] Ensure identical output to original implementation
- [ ] Test edge cases (overnight periods, timezone boundaries)
- [ ] Validate complex table combinations
- [ ] Test various party sizes and date ranges

### 5.3 Integration Testing
- [ ] Test with existing Product.availabilityTables API
- [ ] Validate with real booking scenarios
- [ ] Performance test under load
- [ ] Monitor for regressions

**Success Criteria:**
- 100% functional equivalence
- Measurable performance improvements
- No regressions in edge cases

## Implementation Strategy

### Cutover Approach
1. **Parallel Development**: Build new optimized functions alongside existing ones
2. **Feature Flag**: Use environment variable to switch between implementations
3. **A/B Testing**: Compare performance and correctness
4. **Complete Cutover**: Replace old implementation entirely

### Risk Mitigation
- Comprehensive test suite before cutover
- Performance monitoring during transition
- Rollback plan if issues discovered
- Gradual rollout to production

## Success Metrics

### Performance Targets
- [ ] 80%+ reduction in total execution time
- [ ] 95%+ reduction in dayjs object creation
- [ ] 70%+ reduction in memory allocations
- [ ] Eliminate "Unaccounted" time from profiler

### Code Quality Targets
- [ ] 50%+ reduction in function complexity
- [ ] Improved maintainability scores
- [ ] Better test coverage
- [ ] Cleaner separation of concerns

## Dependencies & Blockers
- None identified (new feature, no backward compatibility)
- Requires comprehensive testing infrastructure
- Need performance monitoring tools

## Timeline Estimate
- **Phase 1**: 2-3 days (Critical path)
- **Phase 2**: 2-3 days (Parallel with Phase 3)
- **Phase 3**: 2-3 days (Parallel with Phase 2)
- **Phase 4**: 1-2 days (Fine-tuning)
- **Phase 5**: 1-2 days (Validation)
- **Total**: 8-13 days

## Next Steps
1. Begin Phase 1: Core Date Engine Rewrite
2. Set up performance benchmarking infrastructure
3. Create comprehensive test suite for validation
4. Implement feature flag for safe cutover
