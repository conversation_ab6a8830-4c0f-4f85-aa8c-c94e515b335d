# MCP Upgrade Checklist: v2.1.0 → v3.1.0

## Phase 1: Foundation Setup ⚡ CRITICAL PATH
- [ ] Update `@perkd/mcp-core` to v3.1.0 in package.json
- [ ] Create `server/mcp/config/service-config.js`
- [ ] Create `server/mcp/mcp-extension.js` with McpExtension class
- [ ] Test basic extension loading
- [ ] Verify no startup errors

## Phase 2: Model Configuration Migration ⚡ CRITICAL PATH
- [ ] **Product Model**
  - [ ] Convert to factory configuration
  - [ ] Define Zod schemas for all fields
  - [ ] Map specialized tools (findByCategory, search)
  - [ ] Test tool generation
- [ ] **Variant Model**
  - [ ] Convert to factory configuration  
  - [ ] Define Zod schemas
  - [ ] Map specialized tools
  - [ ] Test tool generation
- [ ] **Resource Model**
  - [ ] Convert to factory configuration
  - [ ] Define Zod schemas
  - [ ] Map specialized tools
  - [ ] Test tool generation
- [ ] **Bundle Model**
  - [ ] Convert to factory configuration
  - [ ] Define Zod schemas
  - [ ] Map specialized tools
  - [ ] Test tool generation

## Phase 3: Advanced Features
- [ ] **Specialized Tools**
  - [ ] Convert findByCategory pattern
  - [ ] Convert search pattern
  - [ ] Add any missing domain-specific tools
- [ ] **Documentation Resources**
  - [ ] Implement auto-generated schema resources
  - [ ] Implement auto-generated examples
  - [ ] Add service overview resource
- [ ] **Interactive Prompts**
  - [ ] Convert existing prompts
  - [ ] Add service management guide

## Phase 4: Testing & Validation
- [ ] **Tool Generation**
  - [ ] Verify expected tool count (28+ tools)
  - [ ] Test all CRUD operations
  - [ ] Test specialized tools
  - [ ] Test error scenarios
- [ ] **Schema Format**
  - [ ] Verify Zod schemas render input fields
  - [ ] Test validation error messages
  - [ ] Confirm no JSON Schema remnants
- [ ] **Performance**
  - [ ] Test tool registration time
  - [ ] Test memory usage
  - [ ] Validate pagination limits

## Phase 5: Cleanup & Optimization
- [ ] **Legacy Code Removal**
  - [ ] Remove `server/mcp/tools/` directory
  - [ ] Remove `server/mcp/resources/` directory
  - [ ] Remove `server/mcp/utils/` directory
  - [ ] Clean up unused imports
- [ ] **Configuration Optimization**
  - [ ] Optimize tool generation settings
  - [ ] Fine-tune pagination defaults
  - [ ] Review specialized tools

## Final Validation
- [ ] All existing tools work with new implementation
- [ ] Tool registration < 5 seconds
- [ ] Memory usage stable
- [ ] Existing MCP clients continue to work
- [ ] Auto-generated docs complete and accurate
- [ ] Configuration-driven, no manual tool code
- [ ] All tests pass
- [ ] Error scenarios handled correctly

## Success Metrics
- **Tools Generated**: 28+ (vs current 27)
- **Registration Time**: < 5 seconds
- **Memory Usage**: Stable or improved
- **Client Compatibility**: 100% backward compatible
- **Code Reduction**: ~80% less manual tool code
