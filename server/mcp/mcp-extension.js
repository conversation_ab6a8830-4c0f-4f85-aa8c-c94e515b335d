/**
 * MCP Extension for Product Service
 *
 * Modern factory-based MCP implementation using shared libraries
 * from @perkd/mcp-core v3.1.0
 *
 * This extension automatically generates tools for:
 * - Product: 8 tools (6 standard + 2 specialized)
 * - Variant: 7 tools (6 standard + 1 specialized)
 * - Resource: 7 tools (6 standard + 1 specialized)
 * - Bundle: 7 tools (6 standard + 1 specialized)
 *
 * Total: 29 tools + documentation resources + interactive prompts
 */

const { BaseExtension } = require('@perkd/mcp-core')
const { ServiceConfig } = require('./config')

/**
 * Product Service MCP Extension
 *
 * IMPORTANT: This class MUST be named 'McpExtension' and the file MUST be
 * named 'mcp-extension.js' as the CRM MCP module is hardcoded to load this.
 */
class McpExtension extends BaseExtension {
	/**
   * Initialize the MCP extension with factory libraries
   * This method is called by the parent MCPModule during startup
   */
	async initialize() {
		try {
			// 1. Initialize shared factory libraries with configuration
			await this.initializeFactories(ServiceConfig, {
				generateSchema: true,      // Generate schema documentation
				generateExamples: true,    // Generate usage examples
				generateUploadDocs: false  // Product service doesn't handle file uploads
			})

			// 2. Auto-register all factory-generated tools
			await this.registerAllFactoryTools()

			// 3. Register documentation resources
			await this.registerDocumentationResources()

			// 4. Register interactive prompts
			await this.registerServicePrompts()

			// 5. Log successful initialization
			const toolCount = this.getRegisteredToolCount()
			console.log(`[mcp-extension] ✅ Successfully initialized Product Service MCP Extension with ${toolCount} tools`)
		}
		catch (error) {
			console.error('[mcp-extension] ❌ Failed to initialize MCP extension:', error)
			throw error
		}
	}

	/**
   * Register documentation resources for all models
   * Provides schema definitions and usage examples
   */
	async registerDocumentationResources() {
		try {
			// Register resources for each model
			for (const modelType of Object.keys(ServiceConfig.modelConfigs)) {
				const modelConfig = ServiceConfig.modelConfigs[modelType]
				const toolPrefix = modelConfig.toolPrefix

				// Schema documentation resource
				this.registerStaticResource(
					`${toolPrefix}-schema`,
					`${toolPrefix}://schema`,
					{
						title: `${modelType} Schema`,
						description: `Complete schema definition for ${modelType} model`,
						mimeType: 'application/json'
					},
					async () => {
						const Model = this.app.models[modelType]
						if (!Model) {
							throw new Error(`Model ${modelType} not found`)
						}

						// Use shared documentation provider to generate complete schema
						const { SharedDocumentationProvider } = require('@perkd/mcp-core')
						return SharedDocumentationProvider.generateCompleteDocumentation(Model, modelConfig)
					}
				)

				// Usage examples resource
				this.registerStaticResource(
					`${toolPrefix}-examples`,
					`${toolPrefix}://examples`,
					{
						title: `${modelType} Examples`,
						description: `Usage examples for ${modelType} tools`,
						mimeType: 'application/json'
					},
					async () => {
						const Model = this.app.models[modelType]
						if (!Model) {
							throw new Error(`Model ${modelType} not found`)
						}

						// Generate examples using shared provider
						const { SharedDocumentationProvider } = require('@perkd/mcp-core')
						const docs = SharedDocumentationProvider.generateCompleteDocumentation(Model, modelConfig)
						return docs.examples
					}
				)
			}

			// Service overview resource
			this.registerStaticResource(
				'service-overview',
				'service://overview',
				{
					title: 'Product Service Overview',
					description: 'Complete overview of Product Service MCP capabilities',
					mimeType: 'application/json'
				},
				() => this.generateServiceOverview()
			)

			// Service types reference
			this.registerStaticResource(
				'service-types',
				'service://types',
				{
					title: 'Product Service Types',
					description: 'Reference guide for all model types and their schemas',
					mimeType: 'application/json'
				},
				() => this.generateTypesReference()
			)

		}
		catch (error) {
			console.error('[mcp-extension] Failed to register documentation resources:', error)
			throw error
		}
	}

	/**
   * Register interactive prompts for service management
   */
	async registerServicePrompts() {
		try {
			// Service management guide prompt
			this.server.registerPrompt(
				'product-service-guide',
				{
					title: 'Product Service Management Guide',
					description: 'Interactive guide for using Product Service MCP tools'
				},
				async () => ({
					messages: [
						{
							role: 'user',
							content: {
								type: 'text',
								text: 'What tools are available in the Product Service?'
							}
						},
						{
							role: 'assistant',
							content: {
								type: 'text',
								text: this.generateToolsList()
							}
						}
					]
				})
			)

		}
		catch (error) {
			console.error('[mcp-extension] Failed to register service prompts:', error)
			throw error
		}
	}

	/**
   * Generate service overview with statistics
   */
	generateServiceOverview() {
		const models = Object.keys(ServiceConfig.modelConfigs)
		const toolCount = this.getRegisteredToolCount()

		return {
			service: 'Product',
			version: '3.1.0',
			description: 'Product Service MCP Extension with factory-generated tools',
			models: models.length,
			modelTypes: models,
			tools: toolCount,
			capabilities: [
				'CRUD operations for all models',
				'Advanced querying and filtering',
				'Specialized domain-specific tools',
				'Auto-generated documentation',
				'Type-safe Zod validation'
			],
			generatedAt: new Date().toISOString()
		}
	}

	/**
   * Generate types reference for all models
   */
	generateTypesReference() {
		const types = {}

		for (const [ modelType, config ] of Object.entries(ServiceConfig.modelConfigs)) {
			types[modelType] = {
				toolPrefix: config.toolPrefix,
				responseKey: config.responseKey,
				idField: config.idField,
				specializedTools: config.specializedTools?.map(tool => tool.name) || [],
				description: `${modelType} model with ${config.specializedTools?.length || 0} specialized tools`
			}
		}

		return {
			service: 'Product',
			types,
			totalModels: Object.keys(types).length,
			generatedAt: new Date().toISOString()
		}
	}

	/**
   * Generate tools list for prompts
   */
	generateToolsList() {
		const models = Object.keys(ServiceConfig.modelConfigs)
		let toolsList = 'Product Service MCP Tools:\n\n'

		for (const modelType of models) {
			const config = ServiceConfig.modelConfigs[modelType]
			const prefix = config.toolPrefix

			toolsList += `**${modelType} Tools (${prefix}_*):**\n`
			toolsList += `- ${prefix}_create - Create new ${modelType}\n`
			toolsList += `- ${prefix}_get - Get ${modelType} by ID\n`
			toolsList += `- ${prefix}_list - List ${modelType}s with filtering\n`
			toolsList += `- ${prefix}_delete - Delete ${modelType}\n`
			toolsList += `- ${prefix}_query - Advanced ${modelType} queries\n`
			toolsList += `- ${prefix}_count - Count ${modelType}s\n`

			if (config.specializedTools) {
				for (const tool of config.specializedTools) {
					toolsList += `- ${prefix}_${tool.name} - ${tool.description}\n`
				}
			}
			toolsList += '\n'
		}

		return toolsList
	}

	/**
   * Get count of registered tools (for logging)
   */
	getRegisteredToolCount() {
		// Each model gets 6 standard tools + specialized tools
		let count = 0
		for (const config of Object.values(ServiceConfig.modelConfigs)) {
			count += 6 // Standard CRUD + query + count
			count += config.specializedTools?.length || 0
		}
		return count
	}
}

module.exports = { McpExtension }
