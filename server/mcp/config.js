/**
 * MCP Service Configuration for Product Service
 *
 * This configuration defines the modern factory-based MCP implementation
 * using shared libraries from @perkd/mcp-core v3.1.0
 */

const { z } = require('zod')

/**
 * Product Service MCP Configuration
 * Defines 4 models: Product, Variant, Resource, Bundle
 * Generates 28+ tools automatically using shared factory libraries
 */
const ServiceConfig = {
	serviceConfig: {
		serviceName: 'Product',
		defaultLimit: 20,
		maxLimit: 100,
		customOperations: {
			// Custom operations can be added here if needed
			// Example: activate, clone, etc.
		}
	},

	modelConfigs: {
		/**
     * Product Model Configuration
     * Main product entity with title, description, brand, availability
     */
		Product: {
			toolPrefix: 'product',
			responseKey: 'product',
			idField: 'id',
			baseSchema: {
				// Core product fields
				title: z.string().min(1).max(128).describe('Product name (required)'),
				description: z.string().optional().describe('Product description'),
				brand: z.string().max(64).optional().describe('Brand name of the product'),

				// Availability and status
				availability: z.enum([ 'active', 'soldout', 'hide', 'preorder' ])
					.optional()
					.describe('Availability status'),
				availabilityDate: z.string().optional().describe('Date when pre-ordered product becomes available (ISO format)'),

				// Price information
				priceRange: z.object({
					min: z.number().optional().describe('Minimum price'),
					max: z.number().optional().describe('Maximum price'),
					currency: z.string().optional().describe('Currency code')
				}).optional().describe('Price range for the product'),

				// Status flags
				isLowQuantity: z.boolean().default(false).describe('Indicates low inventory across variants'),
				isSoldOut: z.boolean().default(false).describe('Indicates all variants are sold out'),
				isBackOrder: z.boolean().default(false).describe('Allows ordering when out of stock'),

				// Additional data
				attributes: z.record(z.any()).optional().describe('Custom attributes'),
				external: z.object({
					shopify: z.object({
						productId: z.string().optional(),
						shop: z.string().optional().describe('Shopify shop name')
					}).optional()
				}).optional().describe('External system references'),

				// Relations (for reference, actual relations handled by LoopBack)
				businessId: z.string().optional().describe('Vendor/Business ID')
			},
			specializedTools: [
				{
					name: 'findByCategory',
					title: 'Find Products by Category',
					description: 'Find products in a specific category',
					inputSchema: {
						category: z.string().describe('Category to filter by'),
						limit: z.number().min(1).max(100).optional().default(20).describe('Maximum number of products to return'),
						skip: z.number().min(0).optional().default(0).describe('Number of products to skip (for pagination)')
					},
					filter: args => ({
						categories: { inq: [ args.category ] }
					}),
					order: 'title ASC'
				},
				{
					name: 'search',
					title: 'Search Products',
					description: 'Search for products by name or description',
					inputSchema: {
						query: z.string().min(1).describe('Search query string'),
						limit: z.number().min(1).max(100).optional().default(20).describe('Maximum number of products to return'),
						skip: z.number().min(0).optional().default(0).describe('Number of products to skip (for pagination)')
					},
					filter: args => ({
						or: [
							{ title: { like: `%${args.query}%`, options: 'i' } },
							{ description: { like: `%${args.query}%`, options: 'i' } }
						]
					}),
					order: 'title ASC'
				}
			]
		},

		/**
     * Variant Model Configuration
     * Product variations with SKU, pricing, inventory
     */
		Variant: {
			toolPrefix: 'variant',
			responseKey: 'variant',
			idField: 'id',
			baseSchema: {
				// Core variant fields
				kind: z.enum([ 'product', 'membership', 'giftcard', 'storedvalue', 'booking', 'ticket', 'voucher', 'widget', 'nft' ])
					.default('product')
					.describe('Type of variant'),
				title: z.string().max(128).optional().describe('Variant title displayed in checkout'),
				position: z.number().default(1).describe('Order position in variant list'),

				// Identifiers
				sku: z.string().optional().describe('Stock Keeping Unit - unique identifier'),
				gtin: z.object({
					code: z.string().optional().describe('Barcode type (GTIN-8, GTIN-12, etc.)'),
					value: z.string().optional().describe('Barcode value')
				}).optional().describe('Global Trade Item Number'),

				// Pricing and tax
				taxable: z.boolean().default(true).describe('Whether variant is taxable'),
				taxCode: z.string().optional().describe('Tax code for variant'),

				// Inventory management
				inventory: z.object({
					management: z.enum([ 'shopify', 'perkd', 'resource' ]).optional().describe('Inventory tracking system'),
					policy: z.enum([ 'deny', 'continue' ]).default('deny').describe('Out of stock policy'),
					lowQuantityWarningThreshold: z.number().default(0).describe('Low stock warning threshold'),
					quantity: z.number().default(0).describe('Current inventory quantity')
				}).optional().describe('Inventory settings'),

				// Channels and attributes
				channels: z.array(z.enum([ 'store', 'website', 'perkd', 'shopify', 'grabfood', 'grabmart', 'ubereats', 'foodpanda' ]))
					.default([])
					.describe('Sales channels where variant is available'),
				attributes: z.record(z.any()).optional().describe('Custom attributes'),

				// Relations
				productId: z.string().optional().describe('Parent product ID'),
				resourceId: z.string().optional().describe('Associated resource ID')
			},
			specializedTools: [
				{
					name: 'findBySku',
					title: 'Find Variant by SKU',
					description: 'Find a variant by its SKU',
					inputSchema: {
						sku: z.string().describe('SKU to search for')
					},
					filter: args => ({ sku: args.sku }),
					findOne: true
				}
			]
		},

		/**
     * Resource Model Configuration
     * Bookable resources like tables, venues, events
     */
		Resource: {
			toolPrefix: 'resource',
			responseKey: 'resource',
			idField: 'id',
			baseSchema: {
				// Core resource fields
				kind: z.enum([ 'event', 'venue', 'table', 'person' ])
					.default('event')
					.describe('Type of resource'),
				name: z.string().max(32).optional().describe('Resource name (used as calendar summary)'),
				description: z.string().max(120).optional().describe('Resource description'),

				// Location and timing
				timeZone: z.string().optional().describe('IANA time zone name (e.g., Asia/Singapore)'),

				// Relations
				productId: z.string().optional().describe('Associated product ID'),
				placeId: z.string().optional().describe('Location/place ID')
			},
			specializedTools: [
				{
					name: 'findByKind',
					title: 'Find Resources by Kind',
					description: 'Find resources by their type',
					inputSchema: {
						kind: z.enum([ 'event', 'venue', 'table', 'person' ]).describe('Resource type to filter by'),
						limit: z.number().min(1).max(100).optional().default(20).describe('Maximum number of resources to return'),
						skip: z.number().min(0).optional().default(0).describe('Number of resources to skip')
					},
					filter: args => ({ kind: args.kind }),
					order: 'name ASC'
				}
			]
		},

		/**
     * Bundle Model Configuration
     * Product bundles and combinations
     */
		Bundle: {
			toolPrefix: 'bundle',
			responseKey: 'bundle',
			idField: 'key',
			baseSchema: {
				// Core bundle fields
				title: z.string().max(24).optional().describe('Display name in bag (e.g., Drinks)'),
				required: z.boolean().default(true).describe('Is bundle mandatory?'),
				unique: z.boolean().optional().describe('Single selection per option'),

				// Selection constraints
				min: z.number().optional().describe('Minimum number of selections'),
				max: z.number().optional().describe('Maximum number of selections'),

				// Bundle values (simplified for schema - actual structure is complex)
				value: z.array(z.string()).optional().describe('Bundle value identifiers')
			},
			specializedTools: [
				{
					name: 'findRequired',
					title: 'Find Required Bundles',
					description: 'Find bundles that are marked as required',
					inputSchema: {
						limit: z.number().min(1).max(100).optional().default(20).describe('Maximum number of bundles to return'),
						skip: z.number().min(0).optional().default(0).describe('Number of bundles to skip')
					},
					filter: () => ({ required: true }),
					order: 'title ASC'
				}
			]
		}
	}
}

module.exports = { ServiceConfig }
