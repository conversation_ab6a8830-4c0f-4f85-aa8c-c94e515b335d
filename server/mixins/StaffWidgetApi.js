/**
 *  @module Model:StaffWidgetApi - used by staff widgets
 */
const { Bookings, Queuings, Products, Settings } = require('@crm/types'),
	{ resourceToSpot, resourceToProduct } = require('@perkd/fulfillments'),
	{ dayjs, isLastStep, openUntil, dayOfWeek } = require('@perkd/utils')

const { SUCCESS, RESERVED, ENDED } = Bookings.Status,
	{ STAFF } = Bookings.Source,
	{ REGULAR } = Queuings.Tier,
	{ TABLE } = Products.ResourceKind,
	{ BOOKING, LOCALE } = Settings.Name,
	MAINTENANCE = 'maintenance',
	MIN_DURATION = 15 // Minimum duration in minutes for table availability

module.exports = function(Product) {

	/**
	 * Open a table for use, idempotent operation (Staff Widgets API)
	 * 	1. End open orders in the last 24 hours
	 * 	2. End all active bookings
	 * @param	{Spot} table { resourceId, ... }
	 * @returns {Promise<Object>} { resource: { id, name, position } }
	 */
	Product.staffOpenTable = async function(table = {}) {
		const { app } = Product,
			{ Event, models } = app,
			{ Resource, Booking, Order } = models,
			{ timeZone: TIMEZONE } = app.getSettings(LOCALE),
			{ resourceId } = table

		// Input validation
		if (!resourceId) throw 'invalid_resource'

		// 1. Verify table exists
		const resource = await Resource.findById(resourceId),
			{ name, position, timeZone = TIMEZONE, placeId } = resource || {},
			now = dayjs().tz(timeZone).toDate()

		try {
			// 2. Get open orders and active bookings (not ended) for the table
			const [ openOrders, activeBookings ] = await Promise.all([
				Order.find({
					where: {
						resourceId,
						'when.paid': { neq: null },
						'when.cancelled': null,
						'when.fulfilled': null,
						and: [
							{ createdAt: { gt: dayjs(now).subtract(1, 'day').toDate() } },
							{ createdAt: { lte: now } }
						]
					},
					order: 'createdAt ASC'
				}),
				Booking.find({
					where: {
						resourceId,
						or: [
							{	// booked by customer and checked in
								status: SUCCESS,
								and: [
									{ startTime: { lt: now } },
									{ arrivedAt: { exists: true } }
								]
							},
							{	// closed by staff and started
								status: RESERVED,
								startTime: { lte: now }
							}
						],
						departedAt: { exists: false }
					},
					order: 'createdAt ASC'
				})
			])

			// 3. Verify all open orders are fulfilled
			const fulfilledOrders = openOrders.filter(order => isLastStep(order.flow))

			if (fulfilledOrders.length !== openOrders.length) throw 'UNFULFILLED_ORDERS'

			// 4. End all active bookings of the same session (same start time)
			if (activeBookings.length > 0) {
				const [ first ] = activeBookings,
					startTime = first.startTime.getTime(),
					sameTimeBookings = activeBookings
						.filter(b => b.startTime.getTime() === startTime)

				await Promise.all([
					...sameTimeBookings.map(booking => booking.updateAttributes({ status: ENDED, departedAt: now })),
					...fulfilledOrders.map(order => order.updateAttributes({ when: { ended: now } }))
				])
			}

			// 5. End all open fulfilled orders
			await Promise.all(
				fulfilledOrders.map(order => {
					const { when } = order.toJSON()
					when.ended = now
					return order.updateAttributes({ when })
				})
			)

			// 6. Emit person.visit.leave event for reservation
			const { personId, reservationId, partySize, startTime, endTime } = activeBookings[0] || {}
			if (personId) {
				const visitData = {
					id: personId,
					context: {
						spot: { placeId },
						booking: {
							reservationId,
							kind: TABLE,
							partySize,
							placeId,
							startTime,
							endTime
						}
					},
					occurredAt: now
				}
				appEmit(Event.person.visit.leave, visitData)
			}

			return { resource: { id: resourceId, name, position } }
		}
		catch (error) {
			console.error(`[openTable] table: ${resourceId}`, error)
			throw error
		}
	}

	/**
	 * Queue for Immediately available table  (Staff Widgets API)
	 * @param	{String} placeId
	 * @param	{Number} partySize - number of people
	 * @param	{Boolean} [allowCombined] - accept combined tables, default true
	 * @param	{Boolean} [adjacentOnly] - adjacent tables only, default true
	 * @param	{Boolean} [immediate] - immediate available tables only, default true
	 * @returns {Promise<Object>} { kind, status, partySize, resources? }	// TODO waitTime
	 */
	Product.staffQueueTable = async function (placeId, partySize, allowCombined, adjacentOnly, immediate = true) {
		const customer = { tier: REGULAR },
			preferences = { allowCombined, adjacentOnly, immediate }

		return Product.requestTableQueue(customer, placeId, partySize, preferences)
	}

	/**
	 * Take a Table offline for a period of time  (Staff Widgets API)
	 * @param	{Spot} table { resourceId, ... }
	 * @param	{Date} from
	 * @param	{Date} to
	 * @param	{String} [reason] - default: maintenance
	 * @returns {Promise<void>}
	 */
	Product.staffCloseTable = async function (table = {}, from, to, reason = MAINTENANCE) {
		const { app } = Product,
			{ Resource, Booking, Place } = app.models,
			{ timeZone: TIMEZONE } = app.getSettings(LOCALE),
			{ resourceId } = table

		if (!resourceId) throw 'invalid_resource'

		try {
			// 1. Verify table exists
			const resource = await Resource.findById(resourceId)
			if (!resource) throw 'invalid_resource'

			// 2. Extract resource properties
			const { placeId, timeZone = TIMEZONE, capacity } = resource

			// 3. Fetch the place to get business hours
			const place = await Place.findById(placeId)
			if (!place) throw 'place_not_found'
			const products = [ resourceToProduct(resource) ]
			const businessHours = place.dinein?.hours || place.openingHours

			// 4. Create booking segments that align with business hours
			await createBookingsForPeriod(
				businessHours,
				from,
				to,
				timeZone,
				products,
				reason,
				capacity,
				Resource,
				Booking,
				resourceId
			)
		}
		catch (error) {
			console.error(`[closeTable] table: ${resourceId}`, error)

			// Ensure the error has a proper statusCode for HTTP response
			if (!error.statusCode) {
				error.statusCode = error.code === 'tables_unavailable' ? 400 : 500
			}

			throw error
		}
	}

	/**
	 * Express intent to relocate table(s) and retrieve available destination tables
	 * @param {Spot} from - source table
	 *			{String} type
	 *			{String} name
	 *			{String} placeId
	 *			{String} resourceId
	 *			{Object[]} position - of table [{ key, value }]
	 * @returns {Object} Source tables and available destination tables, with reservation details if booking is enabled
	 *			{Object} reservation - Reservation details (when booking is enabled)
	 *			{Object[]} sourceTables - List of source tables with order information
	 *			{Object[]} availableTables - List of available destination tables
	 *			{Object[]} suggestedCombinations - Suggested table combinations (when booking is enabled)
	 */
	Product.staffRelocateTableIntent = async function (from = {}) {
		const { app } = Product
		const { Resource, Booking, Fulfillment } = app.models
		const { enabled } = app.getSettings(BOOKING)[TABLE]
		const { placeId, resourceId = null } = from

		// Find the source table resource
		const firstTableResource = await Resource.findById(resourceId)
		if (!firstTableResource) {
			throw {
				statusCode: 404,
				code: 'table_not_found',
				message: 'Source table not found'
			}
		}

		// Handle based on booking feature status
		if (enabled) {
			// ===== BOOKING ENABLED FLOW =====

			// Get active reservation for this table
			const reservation = await Booking.activeReservationOfResource(resourceId)
			if (!reservation) {
				throw {
					statusCode: 404,
					code: 'reservation_not_found',
					message: 'No active reservation'
				}
			}

			const { id: reservationId, partySize, startTime, endTime, note, deposit, bookings } = reservation

			// Get all source table IDs
			const sourceTableIds = bookings.map(b => b.resourceId)

			// Find all source table resources
			const sourceTableResources = await Resource.find({
				where: {
					id: { inq: sourceTableIds }
				}
			})

			// Find all open fulfillments associated with these tables
			const sources = sourceTableResources.map(resourceToSpot)
			const fulfillments = await Fulfillment.findOpenForTables(sources)
			// Find the minimum capacity of the source tables
			const minSourceCapacity = Math.min(...sourceTableResources.map(table => table.capacity || 0))

			// Create a map of order IDs by table ID
			const orderIdsByTable = {}

			// Process fulfillments to group by table
			fulfillments.forEach(fulfillment => {
				const { destination = {}, orderId } = fulfillment
				const { resourceId: tableId } = destination

				if (tableId) {
					orderIdsByTable[tableId] = orderIdsByTable[tableId] || new Set()
					orderIdsByTable[tableId].add(String(orderId))
				}
			})

			// Prepare source tables with order information
			const sourceTables = sourceTableResources.map(table => {
				const orderIds = orderIdsByTable[table.id] || new Set()

				return {
					resourceId: table.id,
					name: table.name,
					position: table.position,
					capacity: table.capacity,
					orders: orderIds.size,
					hasActiveOrders: orderIds.size > 0
				}
			})

			// Find available tables that can accommodate the party
			const { timeZone: TIMEZONE } = app.getSettings(LOCALE)
			const { timeZone = TIMEZONE } = firstTableResource || {}
			const now = dayjs().tz(timeZone).toDate()
			const endTimeDate = dayjs(endTime).tz(timeZone).toDate()
			// Calculate remaining minutes, using minimum duration if negative or zero
			const duration = Math.max(Math.ceil((endTimeDate - now) / (60 * 1000)), MIN_DURATION)

			// Find available tables
			const { available: availableTables } = await Resource.available(
				placeId,
				TABLE,
				minSourceCapacity,
				now,
				duration,
				true, // allow combined tables
				true
			)

			// Filter out tables that are already part of this reservation
			const filteredAvailableTables = availableTables.filter(
				table => !sourceTableIds.find(id => String(id) === String(table.id))
			)

			// Get candidates with suitability scores
			const candidates = Resource.candidates(
				filteredAvailableTables,
				minSourceCapacity,
				{ allowCombined: true, adjacentOnly: false },
				true // include scores
			)

			// Prepare available tables with additional information, filtering out duplicates
			// Create a map to track unique tables by ID, keeping only the highest-scoring entry
			const uniqueTables = new Map()

			candidates.forEach(candidate => {
				const { resources, totalCapacity, isAdjacent, score } = candidate

				// Process all tables in the combination
				resources.forEach(table => {
					if (!uniqueTables.has(table.id) || uniqueTables.get(table.id).suitabilityScore < (score || 0)) {
						uniqueTables.set(table.id, {
							resourceId: table.id,
							name: table.name,
							position: table.position,
							capacity: table.capacity,
							suitabilityScore: score || 0,
							isAdjacent,
							isCombined: resources.length > 1,
							totalCapacity
						})
					}
				})
			})

			const availableTablesWithInfo = Array.from(uniqueTables.values())

			// Generate suggested combinations directly from candidates
			const suggestedCombinations = candidates
				.filter(candidate => candidate.totalCapacity >= minSourceCapacity)
				.map(candidate => ({
					tables: candidate.resources.map(r => r.id),
					totalCapacity: candidate.totalCapacity,
					suitabilityScore: candidate.score,
					isAdjacent: candidate.isAdjacent
				}))
				.sort((a, b) => b.suitabilityScore - a.suitabilityScore)
				// .slice(0, 5) // Top 5 options

			return {
				reservation: {
					id: reservationId,
					partySize,
					startTime,
					endTime,
					note,
					deposit
				},
				sourceTables,
				availableTables: availableTablesWithInfo,
				suggestedCombinations
			}
		}
		// ===== BOOKING DISABLED FLOW =====

		// When booking is disabled, we only have the single source table
		// Find all orders associated with this table
		const fulfillments = await Fulfillment.findOpenForTables([ from ])

		// Check if there are any active orders for this table
		if (fulfillments.length === 0) {
			throw {
				statusCode: 404,
				code: 'no_active_orders',
				message: 'No active orders found for this table'
			}
		}

		// Prepare source table with order information
		const orderIds = new Set(fulfillments.map(f => String(f.orderId)))

		const sourceTables = [ {
			resourceId: firstTableResource.id,
			name: firstTableResource.name,
			position: firstTableResource.position,
			capacity: firstTableResource.capacity,
			orders: orderIds.size,
			hasActiveOrders: orderIds.size > 0
		} ]

		// Find all tables in the same place
		const allTables = await Resource.find({
			where: { kind: TABLE, placeId }
		})

		// Get all table IDs except the source table
		const otherTables = allTables.filter(table => table.id !== resourceId)

		// Find all fulfillments for other tables in a single query
		const otherTableFulfillments = await Fulfillment.findOpenForTables(otherTables)

		// Create a set of table IDs that have active orders
		const tablesWithOrders = new Set(
			otherTableFulfillments
				.map(fulfillment => {
					const { destination = {} } = fulfillment
					const { resourceId: tableId } = destination
					return tableId
				})
				.filter(Boolean)
		)

		// Filter to tables with no active orders
		const availableTables = allTables.filter(table =>
			table.id !== resourceId && !tablesWithOrders.has(table.id)
		)

		// Prepare available tables with basic information
		const availableTablesWithInfo = availableTables.map(table => ({
			resourceId: table.id,
			name: table.name,
			position: table.position,
			capacity: table.capacity
		}))

		// Estimate party size from source table orders
		const estimatedPartySize = Math.min(
			fulfillments.reduce((max, f) => Math.max(max, f.partySize || 1), 1),
			firstTableResource.capacity
		)

		return {
			sourceTables,
			estimatedPartySize,
			availableTables: availableTablesWithInfo
		}
	}

	/**
	 * Execute table relocation with selected source and destination tables
	 * @param {String[]} sourceIds - IDs of source tables
	 * @param {String[]} destinationIds - IDs of destination tables
	 * @param {String} [reservationId] - ID of the reservation (required when booking is enabled)
	 * @returns {Object}
	 *		{Spot[]} originalTables
	 *		{Spot[]} newTables
	 *		{Object[]} relocatedOrders
	 */
	Product.staffRelocateTableCommit = async function (sourceIds = [], destinationIds = [], reservationId) {
		const { app } = Product
		const { Resource, Booking, Order, Fulfillment } = app.models
		const { enabled } = app.getSettings(BOOKING)[TABLE]

		// Validate common inputs
		if (!sourceIds.length || !destinationIds.length) {
			throw {
				statusCode: 400,
				code: 'invalid_table_ids',
				message: 'Source and destination ids are required'
			}
		}

		// Find all source and destination table resources
		const [ sourceTables, destinationTables ] = await Promise.all([
				Resource.find({ where: { id: { inq: sourceIds } } }),
				Resource.find({ where: { id: { inq: destinationIds } } })
			]),
			// Prepare source and destination tables for order relocation
			from = sourceTables.map(resourceToSpot),
			to = destinationTables.map(resourceToSpot),
			{ placeId = '' } = sourceTables[0] ?? {}

		// Validate that all tables exist
		if (sourceTables.length !== sourceIds.length) {
			throw {
				statusCode: 404,
				code: 'source_tables_not_found',
				message: 'One or more source tables not found'
			}
		}
		if (destinationTables.length !== destinationIds.length) {
			throw {
				statusCode: 404,
				code: 'destination_tables_not_found',
				message: 'One or more destination tables not found'
			}
		}

		// Validate that source and destination tables are in the same place
		const allSamePlaceId = [ ...sourceTables, ...destinationTables ]
			.every(table => String(table.placeId || '') === String(placeId))

		if (!allSamePlaceId) {
			throw {
				statusCode: 400,
				code: 'different_places',
				message: 'Source and destination tables must be in the same place'
			}
		}

		if (enabled) {
			// ===== BOOKING ENABLED FLOW =====

			// Validate reservation ID
			if (!reservationId) {
				throw {
					statusCode: 400,
					code: 'invalid_reservationid',
					message: 'Reservation ID is required'
				}
			}

			// Get the first source table ID to find the reservation
			const firstSourceId = String(sourceIds[0])
			if (!firstSourceId) {
				throw {
					statusCode: 400,
					code: 'invalid_source_id',
					message: 'First source table ID is required'
				}
			}

			// Get active reservation for this table using the same method as in intent
			const reservation = await Booking.activeReservationOfResource(firstSourceId)
			if (!reservation) {
				throw {
					statusCode: 404,
					code: 'reservation_not_found',
					message: 'No active reservation found for tables'
				}
			}

			// Validate that the provided reservationId matches the one found
			if (reservation.id !== reservationId) {
				throw {
					statusCode: 400,
					code: 'reservation_mismatch',
					message: 'Provided reservation ID does not match the active reservation for these tables'
				}
			}

			// Get the bookings from the reservation
			const { bookings, partySize, startTime, endTime, note, deposit } = reservation,
				[ firstBooking ] = bookings,
				{ timeZone: TIMEZONE } = app.getSettings(LOCALE),
				{ timeZone = TIMEZONE } = sourceTables[0] || {},
				now = dayjs().tz(timeZone).toDate(),
				{ source, preferences, personId, membershipId, orderId } = firstBooking,
				products = destinationTables.map(resourceToProduct),
				price = undefined,
				options = { note, source, deposit, orderId }

			try {
				const endTimeDate = dayjs(endTime).tz(timeZone).toDate(),
					duration = Math.max(Math.ceil((endTimeDate - now) / (60 * 1000)), MIN_DURATION)

				// Find available tables using the same method as in staffRelocateTableIntent
				const { available: availableTables } = await Resource.available(
					placeId,
					TABLE,
					partySize,
					now,
					duration,
					true // allow combined tables
				)

				// Get IDs of available tables
				const availableTableIds = availableTables.map(table => table.id)

				// Check if all destination tables are available
				const unavailableTables = destinationIds.filter(
					id => !availableTableIds.find(tableId => String(tableId) === String(id))
				)

				if (unavailableTables.length > 0) {
					throw {
						statusCode: 400,
						code: 'tables_unavailable',
						message: 'One or more destination tables are not available',
						details: {
							unavailableTables
						}
					}
				}

				// 1. Create new bookings for destination table and set to SUCCESS
				const newBookings = await Booking.requestAndConfirm(
					products,
					new Date(startTime),
					new Date(endTime),
					1,
					price,
					partySize,
					preferences,
					options,
					personId,
					membershipId
				)

				try {
					// 2. Set new bookings to SUCCESS and relocate orders in parallel
					const setBookingsPromise = Promise.all(
						newBookings.map(booking =>
							booking.updateAttributes({ status: SUCCESS, arrivedAt: now })
						)
					)

					const [ relocateResult ] = await Promise.all([
						Order.relocateTableFulfillments(from, to),
						setBookingsPromise
					])
					const { relocatedOrders } = relocateResult

					// 3. End only the bookings for the selected source tables (async)
					const sourceIdStrings = sourceIds.map(id => String(id))
					Promise.all(
						bookings
							.filter(data => sourceIdStrings.includes(String(data.resourceId)))
							.map(data => {
								const booking = new Booking(data)
								booking.updateAttributes({ status: ENDED, departedAt: now })
							})
					).catch(err => {
						console.error('[staffRelocateTableCommit] Error ending original bookings:', err)
					})

					return {
						originalTables: from,
						newTables: to,
						relocatedOrders
					}
				}
				catch (error) {
					// Attempt rollback if needed
					try {
						// In a real implementation, we would use proper database transactions
						// and rollback the new bookings if they were created

						// Standardize error response
						const statusCode = error.statusCode || 500
						const code = error.code || 'TRANSACTION_FAILED'
						const message = error.message || 'Table relocation failed'
						const details = error.details || { error: error.message || 'Unknown error' }

						throw { statusCode, code, message, details }
					}
					catch (rollbackError) {
						throw {
							statusCode: 500,
							code: 'ROLLBACK_FAILED',
							message: 'Table relocation failed and rollback was unsuccessful',
							details: {
								error: error.message || 'Unknown error',
								rollbackError: rollbackError.message || 'Unknown rollback error'
							}
						}
					}
				}
			}
			catch (error) {
				// Log detailed error information for debugging
				console.log('[staffRelocateTableCommit] Error:', {
					errorMessage: error.message,
					errorCode: error.code,
					sourceIds,
					destinationIds,
					reservationId,
					placeId
				})

				// Handle specific error types with more informative messages
				if (error.code === 'BOOKING_UNAVAILABLE') {
					throw {
						statusCode: 400,
						code: 'tables_unavailable',
						message: 'One or more destination tables are not available',
						details: {
							error: error.message || 'Unknown error',
							sourceIds,
							destinationIds
						}
					}
				}

				const statusCode = error.statusCode || 500
				const code = error.code || 'TRANSACTION_FAILED'
				const message = error.message || 'Table relocation failed'
				const details = error.details || { error: error.message || 'Unknown error' }

				throw { statusCode, code, message, details }
			}
		}
		else {
			// ===== BOOKING DISABLED FLOW =====

			// When booking is disabled, we only support single table to single table relocation
			if (sourceIds.length !== 1 || destinationIds.length !== 1) {
				throw {
					statusCode: 400,
					code: 'single_table_only',
					message: 'Only single table to single table relocation is supported when booking is disabled'
				}
			}

			// Find all orders associated with the source table
			const fulfillments = await Fulfillment.findOpenForTables(from)
			if (fulfillments.length === 0) {
				throw {
					statusCode: 404,
					code: 'no_active_orders',
					message: 'No active orders found for the source table'
				}
			}

			try {
				// Relocate outstanding fulfillments to the destination table
				const { relocatedOrders } = await Order.relocateTableFulfillments(from, to)

				return {
					originalTables: from,
					newTables: to,
					relocatedOrders
				}
			}
			catch (error) {
				const statusCode = error.statusCode || 500
				const code = error.code || 'TRANSACTION_FAILED'
				const message = error.message || 'Table relocation failed'
				const details = error.details || { error: error.message || 'Unknown error' }

				throw { statusCode, code, message, details }
			}
		}
	}

	/**
	 * Get today's table resource timings for staff (Staff Widgets API)
	 * @param {String} placeId - ID of the place
	 * @return {Object[]} List of resources with timing information
	 */
	Product.staffGetTableTimings = async function(placeId) {
		if (!placeId) {
			throw {
				statusCode: 400,
				code: 'invalid_place_id',
				message: 'Place ID is required'
			}
		}

		const { app } = Product,
			{ Resource, Place } = app.models,
			{ timeZone } = app.getSettings(LOCALE)

		try {
			const place = await Place.findById(placeId)
			if (!place) {
				throw {
					statusCode: 404,
					code: 'place_not_found',
					message: 'Place not found'
				}
			}

			// Get dine-in hours or fall back to opening hours
			const { dinein = {}, openingHours = {} } = place
			const { hours: dineinHours } = dinein
			const businessHours = dineinHours || openingHours

			// Calculate today's operating hours end time
			const now = dayjs().tz(timeZone).toDate()
			const closingTime = openUntil(businessHours, now, timeZone)

			return await Resource.getTimings(placeId, TABLE, closingTime)
		}
		catch (error) {
			console.error('[staffGetTableTimings] Error:', error)
			throw {
				statusCode: 500,
				code: 'get_timings_failed',
				message: 'Failed to get table timings',
				details: { error: error.message || 'Unknown error' }
			}
		}
	}

	// -----  Remote Methods  -----

	Product.remoteMethod('staffOpenTable', {
		description: 'Open a table for use (Staff Widgets API)',
		http: { path: '/staff/tables/open', verb: 'post' },
		accepts: [
			{ arg: 'table', type: 'object', required: true, description: 'spot' },
		],
		returns: { type: 'object', root: true },
	})

	Product.remoteMethod('staffQueueTable', {
		description: 'Queue for Immediately available table (Staff Widgets API)',
		http: { path: '/staff/tables/queuing', verb: 'post' },
		accepts: [
			{ arg: 'placeId', type: 'string', required: true },
			{ arg: 'partySize', type: 'number', required: true, description: 'number of people' },
			{ arg: 'allowCombined', type: 'boolean', default: true, description: 'accept combined tables' },
			{ arg: 'adjacentOnly', type: 'boolean', default: true, description: 'adjacent tables only' },
			{ arg: 'immediate', type: 'boolean', default: true, description: 'immediate available tables only' },
		],
		returns: {
			type: 'object', root: true,
			description: '{ kind, status, partySize, resources? }'
		},
	})

	Product.remoteMethod('staffCloseTable', {
		description: 'Take a Table offline for a period of time  (Staff Widgets API)',
		http: { path: '/staff/tables/close', verb: 'post' },
		accepts: [
			{ arg: 'table', type: 'object', required: true, description: 'spot' },
			{ arg: 'from', type: 'Date', required: true, description: 'start time' },
			{ arg: 'to', type: 'Date', required: true, description: 'end time' },
			{ arg: 'reason', type: 'string', default: MAINTENANCE },
		],
		returns: { type: 'object', root: true },
	})

	Product.remoteMethod('staffRelocateTableIntent', {
		description: 'Express intent to relocate table(s) and retrieve available destination tables (Staff Widgets API)',
		http: { path: '/staff/tables/relocate/intent', verb: 'post' },
		accepts: [
			{ arg: 'from', type: 'object', required: true, description: 'Source table (Spot) with resourceId' },
		],
		returns: {
			type: 'object',
			root: true,
			description: 'Source tables and available destination tables, with reservation details if booking is enabled'
		}
	})

	Product.remoteMethod('staffRelocateTableCommit', {
		description: 'Execute table relocation with selected source and destination tables (Staff Widgets API)',
		http: { path: '/staff/tables/relocate/commit', verb: 'post' },
		accepts: [
			{ arg: 'sourceIds', type: [ 'string' ], required: true, description: 'Source table resource ids' },
			{ arg: 'destinationIds', type: [ 'string' ], required: true, description: 'Destination tables resource Ids' },
			{ arg: 'reservationId', type: 'string', description: 'ID of the reservation (required when booking is enabled)' }
		],
		returns: {
			type: 'object',
			root: true,
			description: 'Result of the relocation operation'
		}
	})

	// Register the remote method for staffGetTableTimings
	Product.remoteMethod('staffGetTableTimings', {
		description: 'Get table resource timings for staff (Staff Widgets API)',
		http: { path: '/staff/tables/timings', verb: 'get' },
		accepts: [
			{ arg: 'placeId', type: 'string', required: true, http: { source: 'query' } }
		],
		returns: {
			type: 'array',
			root: true,
			description: 'List of resources with timing information'
		}
	})
}

/**
 * Helper function to create bookings for a time period respecting business hours
 * @param {Object} businessHours - The business hours configuration
 * @param {Date} from - Start time of the closure period
 * @param {Date} to - End time of the closure period
 * @param {String} timeZone - Timezone to use for calculations
 * @param {Array} products - Array of products (tables) to book
 * @param {String} reason - Reason for the closure
 * @param {Number} capacity - Capacity of the resource
 * @param {Object} Resource - Resource model
 * @param {Object} Booking - Booking model
 * @param {String} resourceId - ID of the resource to book
 */
async function createBookingsForPeriod(businessHours, from, to, timeZone, products, reason, capacity, Resource, Booking, resourceId) {
	// Skip processing if no business hours defined
	if (!businessHours?.periods?.length) return

	// Initialize date objects with proper timezone
	// Ensure we're using the correct timezone for all date operations
	const requestedFrom = dayjs.tz(from, timeZone)
	const requestedTo = dayjs.tz(to, timeZone)

	// Standard booking parameters
	const bookingParams = {
		quantity: 1,
		price: 0,
		partySize: capacity,
		options: { note: reason, source: STAFF }
	}

	// Array to store unavailable periods
	const unavailablePeriods = []

	// Process all days including the previous day for overnight periods
	await processAllDays(
		businessHours, requestedFrom, requestedTo, timeZone,
		products, bookingParams, Resource, Booking, resourceId, unavailablePeriods
	)

	// After processing all periods, if there are any unavailable periods, throw a single error
	if (unavailablePeriods.length > 0) {
		// Extract all occupied periods from the unavailable periods
		const occupiedPeriods = unavailablePeriods.flatMap(period => period.occupiedPeriods || [])

		// Extract error messages from unavailable periods
		const errorMessages = unavailablePeriods
			.filter(period => period.error)
			.map(period => period.error)
			.filter(Boolean)

		// Determine appropriate message based on what we have
		let message = 'The table is unavailable during the requested time'
		if (occupiedPeriods.length > 0) {
			message = 'The table is already booked during these times'
		}
		else if (errorMessages.length > 0) {
			message = errorMessages[0] // Use the first error message
		}

		// Throw a properly formatted error with statusCode to ensure it's handled correctly
		throw {
			statusCode: 400, // Add statusCode to ensure proper HTTP response
			code: 'tables_unavailable',
			message,
			details: {
				occupiedPeriods,
				...(errorMessages.length > 0 && { errors: errorMessages })
			}
		}
	}
}

/**
 * Process all days in the requested period, including the previous day for overnight periods
 */
async function processAllDays(
	businessHours, requestedFrom, requestedTo, timeZone,
	products, bookingParams, Resource, Booking, resourceId, unavailablePeriods
) {
	// First, process the previous day for overnight periods
	const previousDate = requestedFrom.startOf('day').subtract(1, 'day')
	const previousDayOfWeek = dayOfWeek(previousDate.toDate(), timeZone)

	// Find and process overnight periods from previous day
	const previousDayPeriods = businessHours.periods.filter(
		period => period.open.day === previousDayOfWeek && period.close.time < period.open.time
	)

	for (const period of previousDayPeriods) {
		await processBusinessPeriod(
			period, previousDate.toDate(), requestedFrom, requestedTo, timeZone,
			products, bookingParams, Resource, Booking, resourceId, unavailablePeriods
		)
	}

	// Now process each day in the requested period
	let currentDate = requestedFrom.startOf('day')
	const endDate = requestedTo.endOf('day')

	// Process each day until we reach the end date (using dayjs for comparison)
	while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'day')) {
		const currentDayOfWeek = dayOfWeek(currentDate.toDate(), timeZone)

		// Find business periods for the current day
		const periodsForDay = businessHours.periods.filter(
			period => period.open.day === currentDayOfWeek
		)

		// Process each business period for the current day
		for (const period of periodsForDay) {
			await processBusinessPeriod(
				period, currentDate.toDate(), requestedFrom, requestedTo, timeZone,
				products, bookingParams, Resource, Booking, resourceId, unavailablePeriods
			)
		}

		// Only process the next day's overnight periods if the next day is NOT included in the requested period
		// This avoids creating overlapping bookings
		const nextDay = currentDate.clone().add(1, 'day')
		const nextDayNotIncluded = nextDay.isAfter(endDate)

		if (nextDayNotIncluded) {
			// Calculate the next day of week, handling the wrap-around from 7 to 1
			const nextDayOfWeek = currentDayOfWeek === 7 ? 1 : currentDayOfWeek + 1

			// Find overnight periods from the next day that might overlap with the current day
			const overnightPeriodsFromNextDay = businessHours.periods.filter(
				period => period.open.day === nextDayOfWeek && period.close.time < period.open.time
			)

			// Process each overnight period from the next day
			for (const period of overnightPeriodsFromNextDay) {
				// Create a modified period that represents just the daytime part (11:00 to 23:59:59)
				const daytimePeriod = {
					open: { day: period.open.day, time: period.open.time },
					close: { day: period.open.day, time: '23:59' } // End at midnight
				}

				// Process the daytime part of the overnight period
				const nextDayDate = currentDate.clone().add(1, 'day')
				await processBusinessPeriod(
					daytimePeriod, nextDayDate.toDate(), requestedFrom, requestedTo, timeZone,
					products, bookingParams, Resource, Booking, resourceId, unavailablePeriods
				)
			}
		}

		// Move to the next day (maintaining dayjs object with timezone)
		currentDate = currentDate.add(1, 'day')
	}
}

/**
 * Process a single business period and create bookings if needed
 */
async function processBusinessPeriod(
	period, baseDate, requestedFrom, requestedTo, timeZone,
	products, bookingParams, Resource, Booking, resourceId, unavailablePeriods
) {
	// Calculate business hours and booking period in one flow
	const { businessStart, businessEnd } = calculateBusinessHours(period, baseDate, timeZone)
	const { startDate, endDate } = calculateBookingPeriod(
		businessStart, businessEnd, requestedFrom, requestedTo
	)

	// Only create booking if there's a valid time segment
	if (startDate && endDate) {
		await createBookingForPeriod(
			startDate, endDate, products, bookingParams,
			Resource, Booking, resourceId, unavailablePeriods
		)
	}
}

/**
 * Calculate business hours start and end times for a period
 */
function calculateBusinessHours(period, baseDate, timeZone) {
	const baseDateStr = dayjs(baseDate).format('YYYY-MM-DD')
	const isOvernight = period.close.time < period.open.time

	// Calculate business start time
	const businessStart = dayjs.tz(
		`${baseDateStr} ${period.open.time}`,
		'YYYY-MM-DD HH:mm',
		timeZone
	)

	// Calculate business end time
	const endDateStr = isOvernight
		? dayjs(baseDate).add(1, 'day').format('YYYY-MM-DD')
		: baseDateStr

	const businessEnd = dayjs.tz(
		`${endDateStr} ${period.close.time}`,
		'YYYY-MM-DD HH:mm',
		timeZone
	)

	return { businessStart, businessEnd }
}

/**
 * Calculate the booking period based on business hours and requested time
 */
function calculateBookingPeriod(businessStart, businessEnd, requestedFrom, requestedTo) {
	// Find the overlap between requested time and business hours
	const bookingStart = requestedFrom.isAfter(businessStart) ? requestedFrom : businessStart

	// Determine the end time, handling midnight specially to avoid gaps
	const isMidnight = businessEnd.hour() === 0
		&& businessEnd.minute() === 0
		&& businessEnd.second() === 0
		&& businessEnd.millisecond() === 0

	// Use the earlier of requestedTo and businessEnd
	const rawBookingEnd = requestedTo.isBefore(businessEnd) ? requestedTo : businessEnd

	// Only subtract 1ms if not exactly midnight to avoid overlapping with next booking
	const bookingEnd = isMidnight ? rawBookingEnd : rawBookingEnd.subtract(1, 'millisecond')

	// Only return dates if there's a valid time segment
	if (bookingStart.isBefore(bookingEnd)) {
		return { startDate: bookingStart, endDate: bookingEnd }
	}

	return { startDate: null, endDate: null }
}

/**
 * Create a booking for a specific period
 * @param {Object} startDate - dayjs object with timezone information
 * @param {Object} endDate - dayjs object with timezone information
 */
async function createBookingForPeriod(
	startDate, endDate, products, bookingParams,
	Resource, Booking, resourceId, unavailablePeriods
) {
	// Convert dayjs objects to Date objects only at the last moment before database calls
	const startDateObj = startDate.toDate()
	const endDateObj = endDate.toDate()

	try {
		// Check if the resource is available for this specific period
		const { available } = await Resource.availableBy([ resourceId ], startDateObj, endDateObj)

		if (!available.length) {
			// Get occupied periods that conflict with the requested booking
			const occupiedPeriods = await Booking.occupied([ resourceId ], startDateObj, endDateObj) || []

			// Add occupied periods to the unavailable periods array
			unavailablePeriods.push({ occupiedPeriods })
			return
		}

		// Create the booking with consistent timezone
		const bookings = await Booking.requestAndConfirm(
			products,
			startDateObj,
			endDateObj,
			bookingParams.quantity,
			bookingParams.price,
			bookingParams.partySize,
			undefined, 	// preferences
			undefined, 	// customer
			bookingParams.options
		)

		// Set all bookings to RESERVED status
		await Promise.all(
			bookings.map(booking => booking.updateAttributes({
				status: RESERVED
			}))
		)
	}
	catch (error) {
		// Try to get occupied periods even in error case
		try {
			const occupiedPeriods = await Booking.occupied([ resourceId ], startDateObj, endDateObj) || []
			unavailablePeriods.push({
				occupiedPeriods,
				error: error.message || 'Unknown error'
			})
		}
		catch (innerError) {
			// If we can't get occupied periods, add error without periods
			unavailablePeriods.push({
				occupiedPeriods: [],
				error: error.message || 'Unknown error'
			})
		}
	}
}
