/**
 *  @module Mixin:TableWidgetApi - for table booking via Widgets and Website
 */
const { Settings, Products, Queuings, Bookings, Wallet, Tags, Notify } = require('@crm/types'),
	{ generateReceiptNumber, MOrder } = require('@perkd/commerce'),
	{ dayjs, dayOfWeek, currencyFormat, satisfyCondition, aggregateHours } = require('@perkd/utils')

const { BOOKING, LOCALE, DEPOSIT } = Settings.Name,
	{ TABLE } = Products.ResourceKind,
	{ WEBSITE, STORE, PERKD } = Products.Channel,
	{ VIP, REGULAR } = Queuings.Tier,
	{ PERSON } = Settings.DepositBasis,
	{ CANCELLED } = Bookings.Status,
	{ QUEUING } = Bookings.Source,
	{ BOOK_TABLE } = Wallet.Widgets.Key,
	{ NONE, HIGH, MEDIUM, LOW } = Bookings.Availability,
	{ NO_REWARD } = Tags.Order

// ===== LOCAL OPTIMIZED DATE UTILITIES =====
// These will be moved to @perkd/utils after successful optimization

/**
 * High-performance UTC-based date operations for table availability
 * Optimized to eliminate timezone conversion overhead in loops
 */

// Core UTC operations - replace dayjs timezone operations
function createUTCTimestamp(year, month, day, hour = 0, minute = 0) {
	return Date.UTC(year, month - 1, day, hour, minute)
}

function addMinutesUTC(timestampMs, minutes) {
	return timestampMs + (minutes * 60 * 1000)
}

function isSameUTCDay(timestamp1Ms, timestamp2Ms) {
	const date1 = new Date(timestamp1Ms)
	const date2 = new Date(timestamp2Ms)
	return date1.getUTCFullYear() === date2.getUTCFullYear()
		&& date1.getUTCMonth() === date2.getUTCMonth()
		&& date1.getUTCDate() === date2.getUTCDate()
}

// Efficient timezone conversion - replace dayjs.tz() calls
function convertUTCToTimezone(utcTimestamp, timeZone) {
	const formatter = new Intl.DateTimeFormat('en-CA', {
		timeZone,
		year: 'numeric',
		month: '2-digit',
		day: '2-digit',
		hour: '2-digit',
		minute: '2-digit',
		hour12: false
	})

	const parts = formatter.formatToParts(new Date(utcTimestamp))
	const partsObj = {}
	parts.forEach(part => partsObj[part.type] = part.value)

	return {
		year: parseInt(partsObj.year),
		month: partsObj.month,
		day: partsObj.day,
		hour: partsObj.hour,
		minute: partsObj.minute,
		dateString: `${partsObj.year}-${partsObj.month}-${partsObj.day}`,
		timeString: `${partsObj.hour}:${partsObj.minute}`
	}
}

// Parse time string to UTC milliseconds for a given date
function parseTimeToUTC(dateUTC, timeString, timeZone) {
	const [ hours, minutes ] = timeString.split(':').map(Number)

	// Create a date string in the target timezone and parse it correctly
	const year = dateUTC.getUTCFullYear()
	const month = String(dateUTC.getUTCMonth() + 1).padStart(2, '0')
	const day = String(dateUTC.getUTCDate()).padStart(2, '0')
	const time = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`

	const localDateTime = dayjs.tz(`${year}-${month}-${day} ${time}`, 'YYYY-MM-DD HH:mm', timeZone)
	return localDateTime.valueOf()
}

// Get timezone offset in milliseconds (cached for performance)
const timezoneOffsetCache = new Map()
function getTimezoneOffsetMs(timeZone, date) {
	const cacheKey = `${timeZone}-${Math.floor(date.getTime() / (24 * 60 * 60 * 1000))}`

	if (timezoneOffsetCache.has(cacheKey)) {
		return timezoneOffsetCache.get(cacheKey)
	}

	const utcDate = new Date(date.getTime())
	const localDate = new Date(date.toLocaleString('en-US', { timeZone }))
	const offset = utcDate.getTime() - localDate.getTime()

	timezoneOffsetCache.set(cacheKey, offset)
	return offset
}

// Day of week calculation - replace dayOfWeek()
function dayOfWeekOptimized(date) {
	if (date instanceof Date) {
		return date.getUTCDay()
	}
	// Fallback for dayjs objects
	return dayOfWeek(date)
}

// Business hours day conversion - convert JS day (0-6) to business hours day (1-7)
function convertToBusinessHoursDay(jsDay) {
	return jsDay === 0 ? 7 : jsDay
}

// Simple performance tracking
function benchmarkAvailability(placeId) {
	const startTime = performance.now()
	const startMemory = process.memoryUsage().heapUsed

	return {
		end: () => {
			const endTime = performance.now()
			const endMemory = process.memoryUsage().heapUsed

			console.log(`[PERF] availabilityTables(${placeId}):`, {
				duration: `${(endTime - startTime).toFixed(2)}ms`,
				memoryDelta: `${((endMemory - startMemory) / 1024 / 1024).toFixed(2)}MB`
			})
		}
	}
}

module.exports = function(Product) {

	// Testing and validation functions
	async function testOptimizationPerformance(placeId, iterations = 10) {
		console.log(`\n=== Performance Test: ${placeId} (${iterations} iterations) ===`)

		const results = []

		for (let i = 0; i < iterations; i++) {
			const benchmark = benchmarkAvailability(placeId)
			await Product.availabilityTables(placeId)
			benchmark.end()

			// Note: In a real test, you would collect timing data here
			// This is a simplified version for demonstration
		}

		console.log('=== Performance Test Complete ===\n')
	}

	async function validateOptimizationAccuracy(placeId) {
		console.log(`\n=== Accuracy Validation: ${placeId} ===`)

		try {
			const result = await Product.availabilityTables(placeId)

			// Basic structure validation
			console.log(`✓ Returned ${result.availability.length} days`)
			console.log(`✓ Date range: ${result.dateRange.start} to ${result.dateRange.end}`)

			let totalSlots = 0
			for (const day of result.availability) {
				totalSlots += day.timeSlots.length

				// Validate each time slot has required properties
				for (const slot of day.timeSlots) {
					if (!slot.time || !slot.timestamp || !slot.partySizeAvailability) {
						console.error(`❌ Invalid slot structure in ${day.date}`)
						return false
					}
				}
			}

			console.log(`✓ Total time slots: ${totalSlots}`)
			console.log('✓ All slots have required properties')
			console.log('=== Accuracy Validation Complete ===\n')

			return true
		}
		catch (error) {
			console.error('❌ Validation failed:', error.message)
			return false
		}
	}

	/**
	 * Availability of tables for party size within bookable period (used by both web and widgets)
	 * @param	{String} placeId
	 * @return	{Object}
	 * 			{String} placeId
	 * 			{Date} lastUpdated
	 * 			{Object} dateRange - { start, end }
	 * 			{Object} config - {
	 * 				{Object} operatingHours
	 * 				{Number} timeSlotDuration
	 * 				{Number} maxPartySize
	 * 				{Number} minPartySize
	 * 				{Number} leadTimeMinutes
	 * 				{Number} maxAdvanceBookingDays
	 * 				{Object[]} partySizeRanges
	 *			}
	 * 			{availability[]} matrix by day/time slots/party size
	 * 				{String} date
	 * 				{String} dayOfWeek
	 * 				{Boolean} isToday
	 * 				{Object[]} timeSlots - {
	 * 					{String} time
	 * 					{String} timestamp
	 * 					{Object} partySizeAvailability
	 * 					{Object} exactAvailability
	 * 				}
	 */
	Product.availabilityTables = async function (placeId) {
		// Start performance tracking
		const benchmark = benchmarkAvailability(placeId)

		const { app } = Product,
			{ Resource, Place, Booking } = app.models,
			settings = app.getSettings(BOOKING)[TABLE],
			locale = app.getSettings(LOCALE),
			{ enabled, minPartySize, maxPartySize, minDuration, leadTime, maxAdvanceBooking, timeSlotInterval, cancelLeadTime } = settings,
			{ timeZone } = locale,
			now = dayjs().tz(timeZone)

		if (!enabled) throw new Error('NOT_ENABLED', { statusCode: 403 })

		// Get the place to check if dinein is enabled
		const store = await Place.findById(placeId).catch(error => {
				throw new Error(`PLACE_NOT_FOUND: ${error.message}`, { statusCode: 404 })
			}),
			{ dinein = {} } = store

		if (!dinein.available) throw new Error('DINEIN_NOT_ENABLED', { statusCode: 403 })

		// Get all table resources for this place
		const resources = await Resource.find({
			where: { placeId, kind: TABLE }
		})

		if (!resources.length) {
			throw new Error(`No active tables found for place: ${placeId}`)
		}

		// Aggregate hours from all resources
		// Convert resources to plain objects and extract hours
		const hoursArray = resources
			.filter(resource => resource && resource.hours)
			.map(resource => {
				// Create a plain object copy of hours to avoid model instance issues
				const hours = JSON.parse(JSON.stringify(resource.hours || { periods: [], specific: [] }))
				return { hours }
			})

		// Use aggregateHours with properly formatted data
		const businessHours = hoursArray.length > 0
			? aggregateHours(hoursArray)
			: { periods: [], specific: [] }

		const helperFunctions = createHelperFunctions({ // Store in a variable
			businessHours, now, timeZone, leadTime, timeSlotInterval, minDuration, minPartySize, maxPartySize, settings
		})
		const { generateTimeSlots, getPartySizeRanges, findAvailableTables } = helperFunctions

		const currentBusinessDays = Array.from({ length: maxAdvanceBooking + 1 }, (_, i) => now.add(i, 'day'))
		const partySizeRanges = getPartySizeRanges(minPartySize, maxPartySize)
		const representativeSizes = partySizeRanges.map(range =>
			range.min === range.max ? range.min : Math.ceil((range.min + range.max) / 2)
		)

		// OPTIMIZED: Collect all raw UTC timestamp slots
		let allRawSlotTimestamps = []

		// Consider the day before the first business day to catch overnight slots
		if (currentBusinessDays.length > 0) {
			const dayBeforeFirstBusinessDay = currentBusinessDays[0].subtract(1, 'day')
			const slotsFromDayBefore = generateTimeSlots(dayBeforeFirstBusinessDay)
			allRawSlotTimestamps.push(...slotsFromDayBefore)
		}

		for (const businessDay of currentBusinessDays) {
			const slotsForBusinessDay = generateTimeSlots(businessDay)
			allRawSlotTimestamps.push(...slotsForBusinessDay)
		}

		// Define the valid range for calendar dates to be included in the response
		const firstCalendarDateInRange = currentBusinessDays[0].startOf('day').valueOf()
		const lastCalendarDateInRange = currentBusinessDays[currentBusinessDays.length - 1].endOf('day').valueOf()

		// Filter slots to be within the actual calendar day range of currentBusinessDays
		// and also respect lead time from the absolute 'now'
		const minBookingTime = now.add(leadTime, 'minutes').valueOf()
		allRawSlotTimestamps = allRawSlotTimestamps.filter(slotMs =>
			slotMs >= firstCalendarDateInRange && slotMs <= lastCalendarDateInRange && slotMs > minBookingTime)

		// Remove duplicates and sort chronologically (simple numeric operations)
		allRawSlotTimestamps = Array.from(new Set(allRawSlotTimestamps)).sort((a, b) => a - b)

		// Get all tables and their occupancy data
		// The dates for occupancy check should cover the full span of possible slot times
		const occupancyCheckDates = [ firstCalendarDateInRange, ...currentBusinessDays ]
		if (currentBusinessDays.length > 0 && dayjs(occupancyCheckDates[0]).isAfter(currentBusinessDays[0].subtract(1,'day'))) {
			occupancyCheckDates.unshift(currentBusinessDays[0].subtract(1,'day'))
		}

		const { tablesByCapacity, tableOccupancy } = await getTablesData({
			Resource, Booking, placeId, dates: occupancyCheckDates, now,
		})

		// Get maxCombined setting from app settings
		const { maxCombined = 3 } = app.getSettings(BOOKING)[TABLE]

		// OPTIMIZED: Process all raw slots to build availability grouped by actual calendar date
		const availabilityByCalendarDate = buildAvailabilityOptimized({
			nowMs: now.valueOf(), // UTC timestamp
			allRawSlotTimestamps, // UTC timestamps instead of dayjs objects
			partySizeRanges,
			representativeSizes,
			timeZone,
			findAvailableTables: (partySize, timeSlotTimestamp) =>
				findAvailableTables(partySize, timeSlotTimestamp, tablesByCapacity, tableOccupancy, maxPartySize, maxCombined),
			settings
		})

		// Determine actual date range from the result
		let actualStartDate = null
		let actualEndDate = null
		if (availabilityByCalendarDate.length > 0) {
			actualStartDate = availabilityByCalendarDate[0].date
			actualEndDate = availabilityByCalendarDate[availabilityByCalendarDate.length - 1].date
		}
		else if (currentBusinessDays.length > 0) { // Fallback if no slots available
			actualStartDate = currentBusinessDays[0].format('YYYY-MM-DD')
			actualEndDate = currentBusinessDays[currentBusinessDays.length - 1].format('YYYY-MM-DD')
		}

		// Build complete response
		const result = {
			placeId,
			lastUpdated: now.toISOString(),
			dateRange: {
				start: actualStartDate,
				end: actualEndDate,
			},
			config: {
				operatingHours: businessHours,
				timeSlotDuration: timeSlotInterval || 30,
				maxPartySize,
				minPartySize,
				leadTimeMinutes: leadTime,
				maxAdvanceBookingDays: maxAdvanceBooking,
				partySizeRanges,
				cancelLeadTime,
			},
			availability: availabilityByCalendarDate,
		}

		// End performance tracking
		benchmark.end()

		return result
	}
	// -----  Table Booking for members from Widgets  -----

	/**
	 * Availability of tables for party size within bookable period
	 * @param	{String} placeId
	 * @return	{Object} availability matrix (see availabilityTables)
	 */
	Product.availableTablesWidget = async function(placeId) {
		return Product.availabilityTables(placeId)
	}

	/**
	 * Book tables
	 * @param	{String} cardId
	 * @param	{String} placeId
	 * @param	{Number} partySize - number of people
	 * @param	{Date} from - start time
	 * @param	{Boolean} allowCombined - accept combined tables
	 * @param	{Boolean} adjacentOnly - adjacent tables only
	 * @param	{String} note - special request/instructions
	 * @return	{Resource[]} booked tables: [{ name, position, capacity, startTime, endTime, resourceId }]
	 */
	Product.bookTablesWidget = async function(cardId, placeId, partySize, from, allowCombined, adjacentOnly, note) {
		const { app } = Product,
			{ Event, models } = app,
			{ Membership, Place } = models,
			locale = app.getSettings(LOCALE),
			{ currency } = locale,
			tableSettings = app.getSettings(BOOKING)[TABLE],
			{ holdTime = 10 } = tableSettings || {},
			membership = await Membership.findActiveByCardId(cardId)

		if (!membership) throw new Error('MEMEBRSHIP_NOT_FOUND', { statusCode: 404 })

		const { id: membershipId, memberId, personId, programId, tierLevel, cardNumber } = membership,
			customer = { tier: VIP, personId, membershipId, cardId },
			place = await Place.findById(placeId)

		if (!place) throw new Error('PLACE_NOT_FOUND', { statusCode: 404 })

		const program = { id: programId, tierLevel, cardNumber },
			{ name: placeName, addressList } = place,
			through = {
				type: PERKD,
				location: {
					type: STORE,
					id: placeId,
					name: placeName
				},
				attributed: {
					type: BOOKING
				}
			}

		// --- Calculate Deposit ---
		const preferences = { allowCombined, adjacentOnly },
			deposit = getDeposit(place, from, partySize, VIP, currency),
			hasDeposit = deposit?.value > 0,
			ttl = !hasDeposit ? undefined : holdTime * 60 * 1000,	// minutes -> milliseconds
			options = { note, ttl, deposit: hasDeposit }

		// --- Request Booking & Create Deposit Order ---
		const booked = await Product.requestTableBookings(customer, placeId, partySize, from, preferences, options),
			[ first = {} ] = booked,
			{ reservationId, startTime, endTime, expiresAt, status, source, createdAt } = first,
			booking = { reservationId, partySize, startTime, endTime, expiresAt, deposit },
			order = (reservationId && hasDeposit)
				? await Product.createOrderUpdateBookings(booked, deposit, program, cardId, memberId, through)
				: undefined

		// --- Create Booking Widget Data, skip if from queuing source ---
		if (source !== QUEUING) {
			appEmit(Event.widget.data.update, {
				key: BOOK_TABLE,
				cardId,
				reservations: [ {
					id: reservationId,
					status,
					partySize,
					startTime,
					endTime,
					expiresAt,
					deposit,
					order,
					place: { id: placeId, name: placeName, addressList },
					createdAt
				} ],
				createdAt,
				modifiedAt: createdAt
			})
		}

		return { booking, order }
	}

	// -----  Table Booking for non-members from Google Reservation / Website  -----

	/**
	 * Availability of tables for party size within bookable period		(web API)
	 * @param	{String} placeId
	 * @return	{Object} availability matrix (see availabilityTables)
	 */
	Product.availabilityTablesWebsite = async function (placeId) {
		const { enabled } = Product.app.getSettings(BOOKING)[WEBSITE]

		if (!enabled) throw new Error('NOT_ENABLED', { statusCode: 403 })

		return Product.availabilityTables(placeId)
	}

	/**
	 * Book tables for non-members			(web API)
	 * @param	{String} name - of customer
	 * @param	{Object} mobile - { countryCode, number }
	 * @param	{String} placeId
	 * @param	{Number} partySize - number of people
	 * @param	{Date} from - start time
	 * @param	{Boolean} allowCombined - accept combined tables
	 * @param	{Boolean} adjacentOnly - adjacent tables only
	 * @param	{String} note - special request/instructions
	 * @return	{Object}
	 *			{String} reservationId of booking
	 *			{Boolean} member - is customer a member
	 *			{Number|undefined} ttl - booking on hold for milliseconds, undefined if confirmed (member no deposit)
	 *			{Object|undefined} deposit - { value, currency, formatted }
	 *			{Object|undefined} welcome - { value, currency, formatted }
	 */
	Product.bookTablesWebsite = async function (placeId, name, mobile = {}, partySize, from, allowCombined, adjacentOnly, note) {
		const { app } = Product,
			{ Event, models } = app,
			{ Program, Membership, Place, OfferMaster } = models,
			locale = app.getSettings(LOCALE),
			{ currency } = locale,
			{ enabled, holdTime = 10 } = app.getSettings(BOOKING)[WEBSITE]

		if (!enabled) throw new Error('NOT_ENABLED', { statusCode: 403 })

		const { countryCode, number } = mobile,
			profile = { givenName: name, mobile: `${countryCode}${number}` },
			{ tierList } = await Program.findOneActiveFreeOrPaid(),
			{ digitalCard: cardMaster } = tierList[0],
			{ masterId } = cardMaster,
			place = await Place.findById(placeId)

		if (!place) throw new Error('PLACE_NOT_FOUND', { statusCode: 404 })

		// --- Match and Join Membership ---
		const { name: placeName, addressList } = place,
			through = {
				type: WEBSITE,
				location: {
					type: STORE,
					id: placeId,
					name: placeName
				},
				attributed: {
					type: BOOKING
				}
			},
			{ membership = {} } = await Program.matchAndJoin(masterId, profile, { through }),
			{ id: membershipId, memberId, personId, digitalCard, programId, tierLevel, cardNumber } = membership,
			{ id: cardId, registeredAt  } = digitalCard ?? {},
			isRegistered = registeredAt !== undefined,
			customerTier = isRegistered ? VIP : REGULAR,
			customer = { tier: customerTier, personId, membershipId, cardId },
			program = { id: programId, tierLevel, cardNumber }

		// --- Calculate Deposit & Get Welcome Offer ---
		const preferences = { allowCombined, adjacentOnly },
			deposit = getDeposit(place, from, partySize, customerTier, currency),
			hasDeposit = deposit?.value > 0,
			[ welcome ] = isRegistered ? [] : await OfferMaster.findWelcomeOffers(),
			ttl = !hasDeposit && isRegistered ? undefined : holdTime * 60 * 1000,	// minutes -> milliseconds
			options = { note, ttl, deposit: hasDeposit, source: WEBSITE }

		// --- Request Booking & Create Deposit Order ---
		const booked = await Product.requestTableBookings(customer, placeId, partySize, from, preferences, options),
			[ first = {} ] = booked,
			{ reservationId, startTime, endTime, expiresAt, status, source, createdAt } = first,
			order = (reservationId && hasDeposit)
				? await Product.createOrderUpdateBookings(booked, deposit, program, cardId, memberId, through)
				: undefined

		// --- Create Booking Widget Data, skip if from queuing source ---
		if (source !== QUEUING) {
			appEmit(Event.widget.data.update, {
				key: BOOK_TABLE,
				cardId,
				reservations: [ {
					id: reservationId,
					status,
					partySize,
					startTime,
					endTime,
					expiresAt,
					deposit,
					order,
					place: { id: placeId, name: placeName, addressList },
					createdAt
				} ],
				createdAt,
				modifiedAt: createdAt
			})
		}

		// Send push notification to registered member
		if (isRegistered) {
			const { templates = {} } = app.getSettings(BOOKING) || {},
				{ TABLE_CONFIRMED, TABLE_PENDING } = Notify.Templates.Booking,
				templateName = deposit ? TABLE_PENDING : TABLE_CONFIRMED,
				template = {
					name: templates[templateName] || templateName,
					widget: BOOK_TABLE
				}

			// Send push notification with fetch
			Membership.notify(String(membershipId), template, undefined, { fetch: true })
				.catch(err => appNotify('[WebsiteBooking]notify', { err, membershipId, template }, 'error'))
		}

		return { reservationId, member: isRegistered, ttl, deposit, welcome }
	}

	/**
	 * Create Order and Update Bookings
	 * @param	{Object[]} booked - list of bookings
	 * @param	{Object} deposit - { value, currency, formatted }
	 * @param	{Object} program - { id, tierLevel, cardNumber }
	 * @param	{String} cardId
	 * @param	{String} memberId
	 * @param	{Object} acquired - { type, location, attributed }
	 * @return	{Object} { id, amount, currency }
	 */
	Product.createOrderUpdateBookings = async function(booked, deposit, program, cardId, memberId, acquired) {
		const { app } = Product,
			{ Booking, Order, Resource, Variant } = app.models,
			[ first = {} ] = booked,
			{ reservationId, personId, membershipId, placeId: storeId, expiresAt } = first,
			{ id: programId, tierLevel, cardNumber } = program ?? {},
			membership = { id: membershipId, memberId, programId, tierLevel, cardNumber },
			options = { program, cardId, through: acquired, skipPayments: true },
			items = []

		// --- Create Pending Deposit Order and Update Bookings ---
		try {
			// Build items list from bookings
			for (const booking of booked) {
				const { bookingId, resourceId } = booking
				if (!resourceId) continue

				const { productId } = await Resource.findById(resourceId),
					[ product, variant ] = await Promise.all([
						Product.findById(productId),
						Variant.findOne({ where: { productId } })
					])

				items.push({
					kind: BOOKING,
					product,
					variant,
					quantity: 1,
					price: 0,
					unitPrice: 0,
					custom: { bookingId, reservationId }
				})
			}

			if (items.length === 0 && booked.length > 0) {
				throw new Error(`Failed to gather variant/product info for any resources in reservation ${reservationId}`)
			}

			// Create order data for MOrder
			const orderData = {
				receipt: {
					number: generateReceiptNumber(),
					skipInvoice: true
				},
				sourceType: 'Variant',
				quantity: 1,
				amount: deposit?.value,
				currency: deposit?.currency,
				items,
				personId,
				membershipId,
				storeId,
				expiresAt,
				tags: { system: [ NO_REWARD ] }
			}
			const mOrder = new MOrder(orderData, [], [], '', options)
			const valid = mOrder.validate()

			if (valid !== true) {
				throw valid // MOrder validation returns an error object if invalid
			}

			const data = mOrder.buildOrder([], membership, personId)
			const { id, amount, currency } = await Order.create(data)

			// Update bookings with order ID
			const bookingIds = booked.map(b => b.bookingId).filter(Boolean)
			if (bookingIds.length > 0) {
				await Booking.updateAll(
					{ id: { inq: bookingIds } },
					{ orderId: id }
				)
			}

			return { id, amount, currency }
		}
		catch (error) {
			appNotify(`Error during order creation/booking update for reservation ${reservationId}:`, error)
			return {} // Return empty object in case of error
		}
	}

	/**
	 * Cancel table booking
	 * @param {String} cardId
	 * @param {String} reservationId
	 * @return {Object} cancelled booking details
	 */
	Product.cancelTableBooking = async function(cardId, reservationId) {
		const { app } = Product,
			{ Event } = app,
			{ Booking } = app.models

		try {
			const bookings = await Booking.find({
				where: { reservationId }
			})

			if (!bookings?.length) throw new Error('NOT_FOUND', { statusCode: 404 })

			// Cancel all bookings associated with this reservation
			for (const booking of bookings) {
				const { id, deposit } = booking

				if (deposit) throw {
					statusCode: 400,
					code: 'deposit_required',
					message: 'Cannot cancel reservation with deposit'
				}

				await Booking.cancel(id)
			}

			// Check if any booking has source 'queuing'
			const hasQueuingSource = bookings.some(booking => booking.source === QUEUING)

			// Emit widget data update event only if not from queuing source
			if (!hasQueuingSource) {
				const { createdAt } = bookings[0] || {}
				const now = new Date()
				appEmit(Event.widget.data.update, {
					key: BOOK_TABLE,
					cardId,
					reservations: [ { id: reservationId, status: CANCELLED, deletedAt: now } ],
					createdAt,
					modifiedAt: now
				})
			}

			// Emit booking cancelled event to delete calendar event
			appEmit(Event.sales.booking.reservation.cancelled, { data: { reservationId, bookings } })

			return { reservationId, status: CANCELLED }
		}
		catch (error) {
			console.error('[cancelTableBooking]', error)
			throw error
		}
	}

	// -----  Remote Methods  -----

	Product.remoteMethod('availableTablesWidget', {
		description: 'Availability of tables for booking (Table Widget API)',
		http: { path: '/app/booking/tables/availability', verb: 'get' },
		accepts: [
			{ arg: 'placeId', type: 'string', http: { source: 'query' }, required: true }
		],
		returns: { type: 'object', root: true, description: 'Availability matrix by day/time slots/party size' }
	})

	Product.remoteMethod('availabilityTablesWebsite', {
		description: 'Availability of tables for party size within bookable period (Website API)',
		http: { path: '/web/booking/tables/availability', verb: 'get' },
		accepts: [
			{ arg: 'placeId', type: 'string', http: { source: 'query' }, required: true }
		],
		returns: { type: 'object', root: true, description: 'Availability matrix by day/time slots/party size' }
	})

	Product.remoteMethod('bookTablesWidget', {
		description: 'Book tables (Table Widget API)',
		http: { path: '/app/booking/tables', verb: 'post' },
		accepts: [
			{ arg: 'cardId', type: 'string', required: true },
			{ arg: 'placeId', type: 'string', required: true },
			{ arg: 'partySize', type: 'number', required: true, description: 'number of people' },
			{ arg: 'from', type: 'Date', required: true, description: 'start time' },
			{ arg: 'allowCombined', type: 'boolean', default: true, description: 'accept combined tables' },
			{ arg: 'adjacentOnly', type: 'boolean', default: true, description: 'adjacent tables only' },
			{ arg: 'note', type: 'string', max: 120, description: 'special request/instructions' },
		],
		returns: { type: 'array', root: true, description: 'list of: { name, position, capacity, startTime, endTime, resourceId }' },
	})

	Product.remoteMethod('bookTablesWebsite', {
		description: 'Book tables for non-members (Website API)',
		http: { path: '/web/booking/tables', verb: 'post' },
		accepts: [
			{ arg: 'placeId', type: 'string', max: 80, required: true },
			{ arg: 'name', type: 'string', required: true, description: 'full name of customer' },
			{ arg: 'mobile', type: 'object', required: true, description: '{ countryCode, number }' },
			{ arg: 'partySize', type: 'number', required: true, description: 'number of people' },
			{ arg: 'from', type: 'date', required: true, description: 'start time' },
			{ arg: 'allowCombined', type: 'boolean', default: true, description: 'accept combined tables' },
			{ arg: 'adjacentOnly', type: 'boolean', default: true, description: 'adjacent tables only' },
			{ arg: 'note', type: 'string', max: 120, description: 'special request/instructions' },
		],
		returns: { type: 'object', root: true, description: '{ reservationId, member, ttl, deposit, welcome }' },
	})

	// Add the remote method
	Product.remoteMethod('cancelTableBooking', {
		description: 'Cancel table booking (Table Widget API)',
		http: { path: '/app/booking/tables/:reservationId', verb: 'delete' },
		accepts: [
			{ arg: 'cardId', type: 'string', required: true },
			{ arg: 'reservationId', type: 'string', required: true, http: { source: 'path' } },
		],
		returns: { type: 'object', root: true, description: '{ reservationId, status }' }
	})
}

// Helper function factory
function createHelperFunctions({ businessHours, now, timeZone, leadTime, timeSlotInterval, minDuration, settings }) {
	/**
	 * Round a time to the nearest interval, but never earlier than the original time
	 * @param {dayjs} time - The time to round
	 * @param {number} intervalMinutes - The interval in minutes
	 * @returns {dayjs} The rounded time
	 */
	function roundToInterval(time, intervalMinutes) {
		const minutes = time.minute()
		// Use Math.ceil to round up to the next interval, ensuring we never go earlier than the original time
		const roundedMinutes = Math.ceil(minutes / intervalMinutes) * intervalMinutes
		// Handle case where rounding up to the next hour
		if (roundedMinutes >= 60) {
			return time.add(1, 'hour').minute(0).second(0)
		}
		return time.minute(roundedMinutes).second(0)
	}

	/**
	 * OPTIMIZED: Generate time slots for a specific date using UTC calculations
	 * @param {dayjs} date
	 * @returns {number[]} UTC timestamps instead of dayjs objects
	 */
	function generateTimeSlots(date) {
		const slots = []

		// Convert dayjs date to UTC timestamp once
		const dayStartMs = createUTCTimestamp(
			date.year(),
			date.month() + 1,
			date.date()
		)

		const timeSlotIntervalMs = (timeSlotInterval || 30) * 60 * 1000
		const minDurationMs = minDuration * 60 * 1000
		const leadTimeMs = leadTime * 60 * 1000
		const nowMs = now.valueOf() // Get UTC timestamp from dayjs
		const minBookingTimeMs = nowMs + leadTimeMs

		// Get day of week for business hours lookup
		const jsDay = dayOfWeekOptimized(new Date(dayStartMs))
		const dayOfWeekValue = convertToBusinessHoursDay(jsDay)

		// Handle regular business hours
		if (businessHours.periods && Array.isArray(businessHours.periods)) {
			// Find all periods for the day
			const periodsForDay = businessHours.periods.filter(period =>
				period.open && period.open.day === dayOfWeekValue
			)

			// Generate slots for each period using UTC calculations
			for (const periodForDay of periodsForDay) {
				const openTime = periodForDay.open.time
				const closeTime = periodForDay.close.time

				// Parse business hours to UTC for this day
				const periodStartMs = parseTimeToUTC(new Date(dayStartMs), formatTime(openTime), timeZone)
				const periodEndMs = parseTimeToUTC(new Date(dayStartMs), formatTime(closeTime), timeZone)

				let currentTimeMs = periodStartMs

				// Round to next interval boundary
				const intervalOffset = currentTimeMs % timeSlotIntervalMs
				if (intervalOffset > 0) {
					currentTimeMs += timeSlotIntervalMs - intervalOffset
				}

				// Handle overnight periods (close time next day)
				let endTimeMs = periodEndMs
				if (endTimeMs < currentTimeMs) {
					endTimeMs += 24 * 60 * 60 * 1000 // Add 24 hours
				}

				// Generate slots in UTC space
				while (currentTimeMs < endTimeMs) {
					const bookingEndMs = currentTimeMs + minDurationMs

					// Check constraints in UTC
					if (currentTimeMs >= minBookingTimeMs && bookingEndMs <= endTimeMs) {
						slots.push(currentTimeMs) // Store UTC timestamp
					}

					currentTimeMs += timeSlotIntervalMs
				}
			}
		}

		// Handle specific date overrides
		if (businessHours.specific && Array.isArray(businessHours.specific)) {
			const dateDetail = {
				year: new Date(dayStartMs).getUTCFullYear(),
				month: new Date(dayStartMs).getUTCMonth() + 1,
				day: new Date(dayStartMs).getUTCDate()
			}

			const specificDate = businessHours.specific.find(spec => {
				if (!spec.date) return false
				return (!spec.date.year || spec.date.year === dateDetail.year)
					&& (!spec.date.month || spec.date.month === dateDetail.month)
					&& (!spec.date.day || spec.date.day === dateDetail.day)
			})

			if (specificDate && specificDate.periods && specificDate.periods.length) {
				// Clear regular slots if we have specific overrides
				slots.length = 0

				// Generate slots for each specific period
				for (const specificPeriod of specificDate.periods) {
					if (specificPeriod.open && specificPeriod.close) {
						const openTime = specificPeriod.open.time
						const closeTime = specificPeriod.close.time

						// Parse specific hours to UTC
						const periodStartMs = parseTimeToUTC(new Date(dayStartMs), formatTime(openTime), timeZone)
						const periodEndMs = parseTimeToUTC(new Date(dayStartMs), formatTime(closeTime), timeZone)

						let currentTimeMs = periodStartMs

						// Round to next interval boundary
						const intervalOffset = currentTimeMs % timeSlotIntervalMs
						if (intervalOffset > 0) {
							currentTimeMs += timeSlotIntervalMs - intervalOffset
						}

						// Handle overnight periods
						let endTimeMs = periodEndMs
						if (endTimeMs < currentTimeMs) {
							endTimeMs += 24 * 60 * 60 * 1000
						}

						// Generate slots in UTC space
						while (currentTimeMs < endTimeMs) {
							const bookingEndMs = currentTimeMs + minDurationMs

							if (currentTimeMs >= minBookingTimeMs && bookingEndMs <= endTimeMs) {
								slots.push(currentTimeMs)
							}

							currentTimeMs += timeSlotIntervalMs
						}
					}
				}
			}
		}

		// Sort slots chronologically (simple numeric sort for UTC timestamps)
		slots.sort((a, b) => a - b)

		return slots
	}

	// Helper function to format time string from HHMM to HH:mm
	function formatTime(timeString) {
		if (typeof timeString === 'string' && timeString.length === 4) {
			return `${timeString.substring(0, 2)}:${timeString.substring(2, 4)}`
		}
		return timeString
	}

	/**
	 * Generate time slots for a specific time period
	 * @param {dayjs} date - The date (start of the business day for which this period applies)
	 * @param {string} openTime - Opening time in format 'HHMM'
	 * @param {string} closeTime - Closing time in format 'HHMM'
	 * @returns {dayjs[]} Array of time slots
	 */
	function generateSlotsForPeriod(date, openTime, closeTime) {
		// Format times and generate slots
		const formattedOpenTime = `${openTime.substring(0, 2)}:${openTime.substring(2, 4)}`,
			formattedCloseTime = `${closeTime.substring(0, 2)}:${closeTime.substring(2, 4)}`,
			slots = []

		// Parse time strings directly in the target timezone to avoid ambiguity
		// 'date' here is the reference business day.
		let currentTime = dayjs.tz(date.format('YYYY-MM-DD') + ' ' + formattedOpenTime, 'YYYY-MM-DD HH:mm', timeZone)
		let endTime = dayjs.tz(date.format('YYYY-MM-DD') + ' ' + formattedCloseTime, 'YYYY-MM-DD HH:mm', timeZone)
		// minBookingTime is calculated based on the absolute 'now' passed into createHelperFunctions
		const currentMinBookingTime = now.add(leadTime, 'minutes')

		// Ensure currentTime is rounded to the nearest interval, but not earlier than the opening time
		currentTime = roundToInterval(currentTime, timeSlotInterval || 30)

		// Handle cases where close time is on the next day (e.g., 11:00 to 02:00)
		if (endTime.isBefore(currentTime)) {
			endTime = endTime.add(1, 'day')
		}

		while (currentTime.isBefore(endTime)) {
			// Check if booking would extend beyond closing time
			const bookingEnd = currentTime.add(minDuration, 'minutes')

			// Only add the slot if:
			// 1. It's after the minimum booking time (lead time from absolute now)
			// 2. The booking would end before or at the closing time of the period
			if (currentTime.isAfter(currentMinBookingTime) && !bookingEnd.isAfter(endTime)) {
				slots.push(currentTime.clone()) // Clone to avoid mutation issues if currentTime is further modified
			}
			// Add the interval and ensure we maintain rounded times
			currentTime = currentTime.add(timeSlotInterval || 30, 'minutes')
		}

		return slots
	}

	/**
	 * OPTIMIZED: Find available tables for a specific party size and time using UTC timestamps
	 * @param {number} partySize
	 * @param {number} timeSlot - UTC timestamp instead of dayjs object
	 * @param {Map<number, Resource[]>} tablesByCapacity
	 * @param {Map<string, { start: dayjs, end: dayjs, quantity: number }[]>} tableOccupancy
	 * @param {number} maxPartySize
	 * @param {number} [maxCombined]
	 * @returns {Resource[]}
	 */
	function findAvailableTables(partySize, timeSlot, tablesByCapacity, tableOccupancy, maxPartySize, maxCombined = 3) {
		// Use optimized UTC timestamp calculations
		const bookingStartMs = timeSlot
		const bookingEndMs = timeSlot + (minDuration * 60 * 1000) // Convert minutes to milliseconds

		// OPTIMIZATION: Convert legacy dayjs-based occupancy to optimized format for this call
		// Cache the conversion to avoid repeated work
		if (!findAvailableTables._occupancyCache || findAvailableTables._occupancyCache.source !== tableOccupancy) {
			findAvailableTables._occupancyCache = {
				source: tableOccupancy,
				optimized: convertOccupancyToOptimized(tableOccupancy)
			}
		}
		const tableOccupancyOptimized = findAvailableTables._occupancyCache.optimized

		// Get the effective maxCapacity for this party size
		const getEffectiveMaxCapacity = size => {
			const { maxCapacity } = settings // settings is from the outer scope of createHelperFunctions

			// Find the exact match or the closest smaller size
			const sizes = Object.keys(maxCapacity).map(Number).sort((a, b) => a - b)
			let bestMatch = sizes[0]

			for (const s of sizes) {
				if (s <= size) bestMatch = s
				else break
			}

			return maxCapacity[bestMatch] || 2.0
		}

		// Calculate the maximum allowed capacity based on maxCapacity constraint
		const effectiveMaxCapacity = getEffectiveMaxCapacity(partySize)
		const maxAllowedCapacity = Math.min(maxPartySize, Math.ceil(partySize * effectiveMaxCapacity))

		// OPTIMIZATION: Pre-allocate array and avoid spread operator in hot path
		let singleTables = []
		for (let capacity = partySize; capacity <= maxAllowedCapacity; capacity++) {
			const tablesAtCapacity = tablesByCapacity[capacity]
			if (tablesAtCapacity) {
				// Direct array concatenation is faster than spread operator
				for (let j = 0; j < tablesAtCapacity.length; j++) {
					singleTables.push(tablesAtCapacity[j])
				}
			}
		}

		// Filter for availability using optimized function
		const availableSingleTables = singleTables.filter(table =>
			isTableAvailableOptimized(table.id, bookingStartMs, bookingEndMs, tableOccupancyOptimized)
		)

		// If we have available single tables, return them
		if (availableSingleTables.length > 0) {
			return availableSingleTables
		}

		// OPTIMIZATION: If no single tables are available, try combinations
		const smallerTables = []
		for (let capacity = 1; capacity < partySize; capacity++) {
			const tablesAtCapacity = tablesByCapacity[capacity]
			if (tablesAtCapacity) {
				// Only include tables that are available using optimized function
				// Direct loop is faster than filter + spread for small arrays
				for (let k = 0; k < tablesAtCapacity.length; k++) {
					const table = tablesAtCapacity[k]
					if (isTableAvailableOptimized(table.id, bookingStartMs, bookingEndMs, tableOccupancyOptimized)) {
						smallerTables.push(table)
					}
				}
			}
		}

		// If no smaller tables are available, return empty array
		if (smallerTables.length === 0) {
			return []
		}

		// Sort by capacity (largest first) for optimal combinations
		smallerTables.sort((a, b) => (b.capacity || 0) - (a.capacity || 0))

		// Find valid combinations (respecting maxCombined and adjacency)
		return findValidCombinations(smallerTables, partySize, maxCombined)
	}

	/**
	 * OPTIMIZED: Define party size categories with stable object shapes
	 * @param {number} min - minimum party size
	 * @param {number} max - maximum party size
	 * @returns {Object[]} party size categories
	 */
	function getPartySizeRanges(min, max) {
		// OPTIMIZATION: Pre-defined stable object shapes for V8 inline caching
		// All objects have identical property order and types
		return [
			{ min, max: 2, label: '1-2' },
			{ min: 3, max: 4, label: '3-4' },
			{ min: 5, max: 8, label: '5-8' },
			{ min: 9, max, label: '9+' }
		]
	}

	return { generateTimeSlots, getPartySizeRanges, findAvailableTables, roundToInterval, generateSlotsForPeriod }
}

/**
 * OPTIMIZED: Convert dayjs-based occupancy to UTC timestamp format for faster processing
 * @param {Map<string, { start: dayjs, end: dayjs, quantity: number }[]>} tableOccupancy
 * @returns {Map<string, { startMs: number, endMs: number, quantity: number }[]>}
 */
function convertOccupancyToOptimized(tableOccupancy) {
	const optimizedOccupancy = new Map()

	for (const [ tableId, periods ] of tableOccupancy) {
		const optimizedPeriods = periods.map(period => ({
			startMs: period.start.valueOf(),
			endMs: period.end.valueOf(),
			quantity: period.quantity
		}))
		optimizedOccupancy.set(tableId, optimizedPeriods)
	}

	return optimizedOccupancy
}

/**
 * OPTIMIZED: Check if a table is available at a specific time using UTC timestamps
 * @param {Number} tableId
 * @param {number} bookingStartMs - UTC timestamp
 * @param {number} bookingEndMs - UTC timestamp
 * @param {Map<string, { startMs: number, endMs: number, quantity: number }[]>} tableOccupancyOptimized
 * @returns {boolean}
 */
function isTableAvailableOptimized(tableId, bookingStartMs, bookingEndMs, tableOccupancyOptimized) {
	const occupiedPeriods = tableOccupancyOptimized.get(String(tableId)) || []

	// Check for overlap with any occupied period using UTC timestamp comparison
	return !occupiedPeriods.some(period =>
		!(bookingEndMs <= period.startMs || bookingStartMs >= period.endMs)
	)
}

/**
 * LEGACY: Check if a table is available at a specific time (kept for compatibility)
 * @param {Number} tableId
 * @param {dayjs} bookingStart
 * @param {dayjs} bookingEnd
 * @param {Map<string, { start: dayjs, end: dayjs, quantity: number }[]>} tableOccupancy
 * @returns {boolean}
 */
function isTableAvailable(tableId, bookingStart, bookingEnd, tableOccupancy) {
	const occupiedPeriods = tableOccupancy.get(String(tableId)) || []

	// Check for overlap with any occupied period
	return !occupiedPeriods.some(period =>
		!((bookingEnd.isBefore(period.start) || bookingEnd.isSame(period.start))
			|| (bookingStart.isAfter(period.end) || bookingStart.isSame(period.end)))
	)
}

/**
 * OPTIMIZED: Check if all tables in the list are adjacent using pre-computed adjacency
 * @param {Resource[]} tables - list of tables
 * @param {Map<string, Set<string>>} precomputedAdjacency - optional pre-computed adjacency map
 * @returns {boolean}
 */
function areTablesAdjacent(tables = [], precomputedAdjacency = null) {
	if (tables.length <= 1) return true

	// Use pre-computed adjacency if available, otherwise build it
	let adjacencyMap = precomputedAdjacency
	if (!adjacencyMap) {
		adjacencyMap = buildAdjacencyMap(tables)
	}

	// Create a set of table IDs in the current combination for fast lookup
	const tableIdSet = new Set(tables.map(t => String(t.id)))

	// Optimized BFS using array instead of queue.shift() for better performance
	const visited = new Set()
	const queue = [ String(tables[0].id) ]
	visited.add(queue[0])
	let queueIndex = 0

	while (queueIndex < queue.length) {
		const current = queue[queueIndex++]
		const neighbors = adjacencyMap.get(current) || new Set()

		for (const neighbor of neighbors) {
			// Only consider neighbors that are in the current combination
			if (tableIdSet.has(neighbor) && !visited.has(neighbor)) {
				visited.add(neighbor)
				queue.push(neighbor)
			}
		}
	}

	// If all tables are visited, they form a connected component
	return visited.size === tables.length
}

/**
 * OPTIMIZED: Build adjacency map for tables (extracted for reuse)
 * @param {Resource[]} tables - list of tables
 * @returns {Map<string, Set<string>>} adjacency map
 */
function buildAdjacencyMap(tables) {
	const adjacencyMap = new Map()
	const tableIdSet = new Set(tables.map(t => String(t.id)))

	for (const table of tables) {
		const id = String(table.id)
		if (!adjacencyMap.has(id)) {
			adjacencyMap.set(id, new Set())
		}

		const adjacentIds = table.adjacentIds?.map(String) || []
		for (const adjId of adjacentIds) {
			if (tableIdSet.has(adjId)) {
				adjacencyMap.get(id).add(adjId)

				// Ensure the reverse connection exists
				if (!adjacencyMap.has(adjId)) {
					adjacencyMap.set(adjId, new Set())
				}
				adjacencyMap.get(adjId).add(id)
			}
		}
	}

	return adjacencyMap
}

/**
 * OPTIMIZED: Find valid table combinations using iterative approach with intelligent pruning
 * @param {Resource[]} tables - list of available tables
 * @param {number} partySize - number of people
 * @param {number} maxCombined - maximum number of tables to combine
 * @returns {Resource[]}
 */
function findValidCombinations(tables, partySize, maxCombined) {
	// Early termination: If we don't have enough tables to meet the party size
	const totalCapacity = tables.reduce((sum, table) => sum + (table.capacity || 0), 0)
	if (totalCapacity < partySize) {
		return []
	}

	// Pre-compute adjacency map for all tables to avoid rebuilding it repeatedly
	const adjacencyMap = buildAdjacencyMap(tables)

	// Use iterative approach with intelligent pruning
	const validCombinations = generateCombinationsOptimized(tables, partySize, maxCombined, adjacencyMap)

	// Sort combinations by:
	// 1. Number of tables (fewer is better)
	// 2. Total capacity (closer to party size is better)
	validCombinations.sort((a, b) => {
		// First sort by number of tables (ascending)
		if (a.length !== b.length) {
			return a.length - b.length
		}

		// Then sort by how close the capacity is to the party size
		const aCapacity = a.reduce((sum, table) => sum + (table.capacity || 0), 0)
		const bCapacity = b.reduce((sum, table) => sum + (table.capacity || 0), 0)
		const aDiff = Math.abs(aCapacity - partySize)
		const bDiff = Math.abs(bCapacity - partySize)

		return aDiff - bDiff
	})

	// Return the best combination, or empty array if none found
	return validCombinations.length > 0 ? validCombinations[0] : []
}

/**
 * OPTIMIZED: Generate all valid combinations using iterative approach with intelligent pruning
 * @param {Resource[]} tables - list of available tables
 * @param {number} partySize - number of people
 * @param {number} maxCombined - maximum number of tables to combine
 * @param {Map<string, Set<string>>} adjacencyMap - pre-computed adjacency map
 * @returns {Resource[][]} list of valid combinations
 */
function generateCombinationsOptimized(tables, partySize, maxCombined, adjacencyMap) {
	const validCombinations = []

	// Early termination: Try single tables first (most optimal)
	for (const table of tables) {
		if ((table.capacity || 0) >= partySize) {
			return [ [ table ] ] // Single table is always optimal
		}
	}

	// Use iterative approach with stack instead of recursion
	// Stack entry: { combo: Resource[], capacity: number, startIdx: number }
	const stack = [ { combo: [], capacity: 0, startIdx: 0 } ]

	while (stack.length > 0) {
		const { combo, capacity, startIdx } = stack.pop()

		// If we've reached the capacity requirement
		if (capacity >= partySize) {
			// Check if tables are adjacent using pre-computed adjacency map
			if (areTablesAdjacent(combo, adjacencyMap)) {
				validCombinations.push([ ...combo ])
			}
			continue
		}

		// If we've reached the maximum number of tables, skip
		if (combo.length >= maxCombined) {
			continue
		}

		// Try adding each remaining table (with intelligent pruning)
		for (let i = startIdx; i < tables.length; i++) {
			const table = tables[i]
			const newCapacity = capacity + (table.capacity || 0)
			const newCombo = [ ...combo, table ]

			// Intelligent pruning: Skip if remaining tables can't possibly meet party size
			const remainingTables = tables.slice(i + 1)
			const maxPossibleCapacity = newCapacity + remainingTables.reduce((sum, t) => sum + (t.capacity || 0), 0)

			// Only prune if we can't possibly reach the party size even with all remaining tables
			if (maxPossibleCapacity < partySize) {
				continue // Skip this branch - impossible to meet party size
			}

			// Add to stack for further exploration (the capacity check will happen when popped)
			stack.push({ combo: newCombo, capacity: newCapacity, startIdx: i + 1 })
		}
	}

	return validCombinations
}

/**
 * Get tables data and occupancy
 * @param	{Object}
 * 			{Model} Resource
 * 			{Model} Booking
 * 			{String} placeId
 * 			{dayjs[]} dates
 * 			{dayjs} now - Current time for reference
 * @return	{Object} - { allTables, tablesByCapacity, tablesById, tableOccupancy }
 */
async function getTablesData({ Resource, Booking, placeId, dates, now }) {
	const allTables = await Resource.find({
		where: { placeId, kind: TABLE }
	})

	// Organize tables by capacity
	const tableIds = allTables.map(table => table.id),
		tablesByCapacity = {},
		tablesById = new Map()

	for (const table of allTables) {
		const capacity = table.capacity || 0
		if (!tablesByCapacity[capacity]) {
			tablesByCapacity[capacity] = []
		}
		tablesByCapacity[capacity].push(table)
		tablesById.set(table.id, table)
	}

	// Get occupancy data
	// Ensure dates array is not empty before accessing its elements
	const startDate = dates && dates.length > 0 ? dates[0].startOf('day').toDate() : now.startOf('day').toDate()
	const endDate = dates && dates.length > 0 ? dates[dates.length - 1].endOf('day').toDate() : now.endOf('day').toDate()

	const occupiedPeriods = await Booking.occupied(tableIds, startDate, endDate),
		tableOccupancy = new Map() 	// Map occupancy by table ID

	// Create a mapping of resourceId strings to their original IDs
	// This ensures consistent key types when looking up in the Map
	const resourceIdMap = new Map()
	for (const tableId of tableIds) {
		resourceIdMap.set(String(tableId), tableId)
	}

	for (const booking of occupiedPeriods) {
		const { resourceId, startTime, endTime, quantity } = booking,
			resourceIdStr = String(resourceId)

		if (!tableOccupancy.has(resourceIdStr)) {
			tableOccupancy.set(resourceIdStr, [])
		}

		tableOccupancy.get(resourceIdStr).push({
			start: dayjs(startTime),
			end: dayjs(endTime),
			quantity: quantity || 1
		})
	}

	return { allTables, tablesByCapacity, tablesById, tableOccupancy }
}

/**
 * OPTIMIZED: Build availability data grouped by actual calendar date using UTC timestamps
 * @param	{Object} options
 * @param	{number} options.nowMs - Current time as UTC timestamp
 * @param	{number[]} options.allRawSlotTimestamps - Flat array of all generated slot UTC timestamps
 * @param	{Object[]} options.partySizeRanges - Array of party size range definitions
 * @param	{Number[]} options.representativeSizes - Array of representative sizes for each range
 * @param	{String} options.timeZone - The local timezone string (e.g., 'Asia/Singapore')
 * @param	{Function} options.findAvailableTables - Function to find available tables for a given size and slot
 * @param	{Object} options.settings - Booking settings for table
 * @returns	{Object[]} Availability data structured by calendar date
 */
function buildAvailabilityOptimized({
	nowMs,
	allRawSlotTimestamps, // UTC timestamps instead of dayjs objects
	partySizeRanges,
	representativeSizes,
	timeZone,
	findAvailableTables,
	settings
}) {
	const availabilityMap = new Map()

	// Get today's date string in timezone for comparison
	const todayParts = convertUTCToTimezone(nowMs, timeZone)
	const todayDateStr = todayParts.dateString

	// OPTIMIZATION: Pre-compute availability levels thresholds
	const { NONE, LOW, MEDIUM, HIGH } = Bookings.Availability
	const getAvailabilityLevel = count => {
		if (count > 5) return HIGH
		if (count > 2) return MEDIUM
		if (count > 0) return LOW
		return NONE
	}

	// OPTIMIZATION: Global cache for availability results to avoid redundant calculations
	const globalAvailabilityCache = new Map()

	for (const slotMs of allRawSlotTimestamps) {
		// Convert to timezone only for display formatting
		const localParts = convertUTCToTimezone(slotMs, timeZone)
		const calendarDateStr = localParts.dateString
		const timeStr = localParts.timeString

		if (!availabilityMap.has(calendarDateStr)) {
			// Get day of week name efficiently from the calendar date, not the slot timestamp
			// This is important for overnight business hours where slot timestamps may be from previous day
			const dayOfWeekNames = [ 'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday' ]
			const calendarDate = new Date(calendarDateStr + 'T00:00:00.000Z')
			const dayOfWeekIndex = calendarDate.getUTCDay()

			availabilityMap.set(calendarDateStr, {
				date: calendarDateStr,
				dayOfWeek: dayOfWeekNames[dayOfWeekIndex],
				isToday: calendarDateStr === todayDateStr, // Simple string comparison
				timeSlots: []
			})
		}

		// OPTIMIZATION: Pre-allocate objects with stable shapes for V8 optimization
		const partySizeAvailability = {
			'1-2': 0,
			'3-4': 0,
			'5-8': 0,
			'9+': 0
		}
		const exactAvailability = {
			'1-2': 0,
			'3-4': 0,
			'5-8': 0,
			'9+': 0
		}

		// OPTIMIZATION: Cache array length to avoid repeated property access
		const rangeCount = partySizeRanges.length

		for (let i = 0; i < rangeCount; i++) {
			const range = partySizeRanges[i]
			const representativeSize = representativeSizes[i]
			const cacheKey = `${slotMs}-${representativeSize}`

			// Check global cache first
			let availableTables
			if (globalAvailabilityCache.has(cacheKey)) {
				availableTables = globalAvailabilityCache.get(cacheKey)
			}
			else {
				// Pass UTC timestamp to findAvailableTables
				availableTables = findAvailableTables(representativeSize, slotMs)
				globalAvailabilityCache.set(cacheKey, availableTables)
			}

			// Use optimized availability level calculation
			const availabilityLevel = getAvailabilityLevel(availableTables.length)
			const tableCount = availableTables.length

			// Direct property assignment (faster than bracket notation in hot paths)
			const label = range.label
			partySizeAvailability[label] = availabilityLevel
			exactAvailability[label] = tableCount
		}

		// OPTIMIZATION: Create time slot object with stable structure for V8 inline caching
		// Always use the same property order and types to maintain object shape
		const timeSlot = {
			time: timeStr,
			timestamp: new Date(slotMs).toISOString(), // UTC ISO string
			partySizeAvailability,
			exactAvailability
		}

		availabilityMap.get(calendarDateStr).timeSlots.push(timeSlot)
	}

	// Convert map to array
	const availabilityByDateArray = Array.from(availabilityMap.values())

	// Sort the array by calendar date (e.g., "2023-01-01" before "2023-01-02")
	availabilityByDateArray.sort((a, b) => a.date.localeCompare(b.date))

	// Sort timeSlots within each day chronologically (e.g., "09:00" before "10:00")
	for (const dayData of availabilityByDateArray) {
		dayData.timeSlots.sort((a, b) => a.time.localeCompare(b.time))
	}

	return availabilityByDateArray
}

/**
 * LEGACY: Build availability data grouped by actual calendar date (kept for reference)
 * This function is replaced by buildAvailabilityOptimized but kept for comparison
 */
function buildAvailabilityByCalendarDate({
	now, // Expected to be dayjs().tz(timeZone)
	allRawSlotDayjsObjects,
	partySizeRanges,
	representativeSizes,
	timeZone, // For clarity, though dayjs objects might already be in this TZ
	findAvailableTables,
	settings // Added settings here
}) {
	const availabilityMap = new Map()
	// Ensure Bookings.Availability is accessible, if not already global or imported at top level
	// const { NONE, HIGH, MEDIUM, LOW } = Bookings.Availability; // Assuming Bookings is available

	// Group slots by their actual calendar date in the local timezone
	for (const slotDayjs of allRawSlotDayjsObjects) {
		// slotDayjs objects are already created with .tz(timeZone) from generateSlotsForPeriod
		const calendarDateStr = slotDayjs.format('YYYY-MM-DD')
		const timeStr = slotDayjs.format('HH:mm')

		if (!availabilityMap.has(calendarDateStr)) {
			availabilityMap.set(calendarDateStr, {
				date: calendarDateStr,
				dayOfWeek: slotDayjs.format('dddd'),
				isToday: slotDayjs.isSame(now, 'day'), // now is already tz-aware
				timeSlots: []
			})
		}

		const partySizeAvailability = {}
		const exactAvailability = {}

		for (let i = 0; i < partySizeRanges.length; i++) {
			const partySizeRange = partySizeRanges[i]
			const representativeSize = representativeSizes[i]
			const { label } = partySizeRange

			// findAvailableTables is called with (partySize, timeSlotDateObject)
			// timeSlotDateObject is expected to be a JS Date by the lambda in Product.availabilityTables
			const availableTables = findAvailableTables(representativeSize, slotDayjs.toDate())

			let availabilityLevel = NONE // Make sure NONE is defined
			if (availableTables.length > 5) availabilityLevel = HIGH // HIGH
			else if (availableTables.length > 2) availabilityLevel = MEDIUM // MEDIUM
			else if (availableTables.length > 0) availabilityLevel = LOW // LOW

			partySizeAvailability[label] = availabilityLevel
			exactAvailability[label] = availableTables.length
		}

		availabilityMap.get(calendarDateStr).timeSlots.push({
			time: timeStr,
			timestamp: slotDayjs.toISOString(), // Absolute UTC timestamp
			partySizeAvailability,
			exactAvailability
		})
	}

	// Convert map to array
	const availabilityByDateArray = Array.from(availabilityMap.values())

	// Sort the array by calendar date (e.g., "2023-01-01" before "2023-01-02")
	availabilityByDateArray.sort((a, b) => a.date.localeCompare(b.date))

	// Sort timeSlots within each day chronologically (e.g., "09:00" before "10:00")
	for (const dayData of availabilityByDateArray) {
		dayData.timeSlots.sort((a, b) => a.time.localeCompare(b.time))
	}

	return availabilityByDateArray
}

/**
 * Get deposit amount for a booking
 * @param	{Object} place
 * @param	{Date} from
 * @param	{Number} partySize
 * @param	{String} customerTier
 * @param	{String} currency
 * @returns {Object|undefined} { value, currency, formatted }
 */
function getDeposit(place, from, partySize, customerTier, currency) {
	const { id: placeId } = place,
		settings = place.getSettingsByName(DEPOSIT),
		fromDate = dayjs(from),
		context = {
			partySize,
			timing: {
				from: fromDate.toISOString(),
				dayOfWeek: dayOfWeek(fromDate)
			},
			customer: {
				tier: customerTier,
			}
		}

	if (!settings?.enabled || !Array.isArray(settings.rules)) {
		return undefined // Deposits not enabled or no rules defined
	}

	for (const rule of settings.rules) {
		try {
			if (satisfyCondition(context, rule.condition)) {
				let depositValue = 0

				if (rule.basis === PERSON) {
					depositValue = rule.amount * partySize
				}
				else { // Default to RESERVATION basis
					depositValue = rule.amount
				}

				if (depositValue > 0) {
					// Found a matching rule requiring deposit
					return {
						value: depositValue,
						currency,
						formatted: currencyFormat(depositValue, currency)
					}
				}
				// Matching rule explicitly requires NO deposit (amount <= 0)
				return undefined

			}
		}
		catch (err) {
			console.error(`Error parsing deposit rule condition for place ${placeId}: ${rule.condition}`, err)
			// Continue to next rule on error
		}
	}

	// No matching rule found
	return undefined
}
