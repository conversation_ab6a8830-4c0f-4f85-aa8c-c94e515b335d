/**
 *  @module Mixin:Calendar - for Resource (bookings)
 * 	reference:	https://developers.google.com/calendar/api/guides/overview
 * 				https://googleapis.dev/nodejs/googleapis/latest/calendar/classes/Calendar.html
 */
const { Providers, Products, Bookings, Contacts } = require('@crm/types'),
	{ rateLimit } = require('@perkd/utils')

const { GOOGLE } = Providers.PROVIDER,
	{ MOBILE } = Contacts.Type,
	{ TABLE } = Products.ResourceKind,
	RATE = {
		limit: 3,
		interval: 3 * 1000,		// 3 seconds
	},
	LIMITER = {},
	{ QUEUING } = Bookings.Source

module.exports = function(Resource) {

	// ----  Event Handlers

	/**
	 * Handle Bookings Confirmed in single order
	 * 	- aggregate multiple identical bookings (same variantId & time start/end time) into one calendar event
	 * @param	{Object} eventData
	 *			{String} orderId
	 *			{Object[]} bookings - {}
	 */
	Resource.handleBookingConfirmedEvent = async function(eventData = {}) {
		const { reservationId, bookings = [] } = eventData,
			{ Person, Booking } = Resource.app.models,
			[ first = {} ] = bookings,
			{ personId, source } = first

		if (source === QUEUING) return // skip calendar events for bookings from queuing

		const aggregated = bookings.reduce((res, booking) => {
				const { id, ...rest } = booking,
					{ startTime, endTime, resourceId } = booking,
					found = res.find(b => b.resourceId === resourceId && b.startTime === startTime && b.endTime === endTime)

				if (found) {
					found.quantity += booking.quantity
					found.bookingIds.push(id)
				}
				else {
					res.push({ ...rest, bookingIds: [ id ] })
				}
				return res
			}, []),
			resourceIds = aggregated.reduce((res, { resourceId }) => {
				if (!res.includes(resourceId)) res.push(resourceId)
				return res
			}, []),
			filter = {
				where: { id: { inq: resourceIds } },
				fields: [ 'id', 'calendarId' ]
			}

		try {
			const [ person, resources, google ] = await Promise.all([
					Person.findById(personId).catch(() => undefined),
					Resource.find(filter),
					Resource.getProvider(GOOGLE),
				]),
				{ fullName, familyName = '', givenName = '', phoneList = [] } = person ?? {},
				{ fullNumber: mobile } = phoneList.find(p => p.type === MOBILE) ?? {},
				[ phone ] = phoneList

			if (!google) {
				appNotify('[Resource]handleBookingConfirmedEvent', { err: 'google not available', eventData }, 'error')
				return
			}

			for (const { kind, bookingIds, startTime, endTime, quantity, partySize, resourceId } of aggregated) {
				const { calendarId } = resources.find(rsrc => String(rsrc.id) === String(resourceId)) ?? {}

				if (!calendarId) continue

				try {
					LIMITER[calendarId] ||= rateLimit(RATE)
					await LIMITER[calendarId].removeTokens(1)

					const suffix = kind === TABLE ? ` (${partySize}x👤)` : ` (${quantity} tickets)`,
						name = fullName || `${familyName} ${givenName}`,
						title = `${name}${quantity > 1 || partySize ? suffix : ''}`,
						data = {
							title,
							description: `📞 +${mobile || phone?.fullNumber || ''}`,
							startTime: new Date(startTime),
							endTime: new Date(endTime)
						},
						filter = {
							where: { id: { inq: bookingIds } },
						}

					const [ event, bookings ] = await Promise.all([
							google.calendars.addEvent(calendarId, data),
							Booking.find(filter)
						]),
						calendar = { id: calendarId, eventId: event.id }

					for (const booking of bookings) {
						booking.updateAttributes({ calendar })
							.catch(err => appNotify('[Resource]handleBookingConfirmedEvent', { bookingIds, resourceId, startTime, endTime, err }, 'error'))
					}
				}
				catch (err) {
					appNotify('[Resource]handleBookingConfirmedEvent', { bookingIds, resourceId, startTime, endTime, err }, 'error')
				}
			}
		}
		catch (err) {
			appNotify('[Resource]handleBookingConfirmedEvent', { err, reservationId, bookings }, 'error')
		}
	}

	/**
	 * Handle Bookings Cancelled in single order
	 * @param	{Object} eventData
	 *			{String} orderId
	 *			{Object[]} bookings - {}
	 */
	Resource.handleBookingCancelledEvent = async function(eventData) {
		const { bookings = [] } = eventData,
			{ first = {} } = bookings,
			{ source } = first

		if (source === QUEUING) return // skip for bookings from queuing

		const events = bookings.reduce((res, { calendar = {} }) => {
			const { id: calendarId, eventId } = calendar

			if (!res.some(r => r.calendarId === calendarId && r.eventId === eventId)) {
				res.push({ calendarId, eventId })
			}
			return res
		}, [])

		try {
			const google = await Resource.getProvider(GOOGLE)

			if (!google) {
				appNotify('[Resource]handleBookingCancelledEvent', { err: 'google not available', eventData }, 'error')
				return
			}

			for (const { calendarId, eventId } of events) {
				LIMITER[calendarId] ||= rateLimit(RATE)

				LIMITER[calendarId].removeTokens(1).then(() => {
					google.calendars.removeEvent(calendarId, eventId)
						.catch(err => appNotify('[Resource]handleBookingCancelledEvent', { err, calendarId, eventId }))
				})
			}
		}
		catch (err) {
			appNotify('[Resource]handleBookingCancelledEvent', { err, bookings }, 'error')
		}
	}

	/**
	 * Purge ALL events of ALL calendars BEFORE given time
	 * @param	{Date} before
	 * @return	{Object} { total, deleted }
	 */
	Resource.calendarPurgeEvents = async function(before) {
		try {
			const filter = {
					where: {
						calendarId: { exists: true, neq: '' },
					},
					fields: [ 'calendarId' ]
				},
				[ allResources, google ] = await Promise.all([
					Resource.find(filter),
					Resource.getProvider(GOOGLE),
				]),
				range = { timeMax: before },
				calendars = [],
				removes = [],
				res = { total: 0, deleted: 0 }

			if (!google) {
				appNotify('[Resource]calendarPurgeEvents', { err: 'google not available', before }, 'error')
				return
			}

			for (const { calendarId } of allResources) {
				const events = await google.calendars.listEvents(calendarId, range),
					eventIds = events.map(evt => evt.id)

				res.total += eventIds.length
				calendars.push({ calendarId, eventIds })
			}

			for (const { calendarId, eventIds } of calendars) {
				LIMITER[calendarId] ||= rateLimit(RATE)

				for (const eventId of eventIds) {
					removes.push(
						LIMITER[calendarId].removeTokens(1).then(() => {
							google.calendars.removeEvent(calendarId, eventId)
								.catch(({ code, message }) => {
									if (code !== 410) {
										appNotify('calendarPurgeEvents', { code, message, calendarId, eventId })
									}
								})
							res.deleted++
						})
					)
				}
			}

			await Promise.all(removes)
			return res
		}
		catch (err) {
			appNotify('[calendarPurgeEvents]', { err, before }, 'error')
		}
	}

	// ----  Instance Methods

	/**
	 * List all events for Resource
	 * @param {Date} [from]
	 * @param {Date} [to]
	 * @return	{Object[]} events
	 */
	Resource.prototype.calendarEvents = async function(from, to) {
		const { calendarId } = this,
			google = await Resource.getProvider(GOOGLE),
			range = {
				timeMin: from,
				timeMax: to
			}

		return google.calendars.listEvents(calendarId, range)
	}

	Resource.prototype.calendarPermission = async function() {
		const { calendarId } = this,
			google = await Resource.getProvider(GOOGLE)

		return google.calendars.listAcls(calendarId)
	}

	Resource.prototype.setCalendarPermission = async function(role, type, value) {
		const { calendarId } = this,
			scope = { type, value },
			google = await Resource.getProvider(GOOGLE)

		return google.calendars.addAcl(calendarId, role, scope)
	}

	// -----  Remote Methods  -----

	Resource.remoteMethod('calendarPurgeEvents', {
		description: 'Purge ALL events of ALL calendars BEFORE stipulated time',
		http: { path: '/calendars/events', verb: 'delete' },
		accepts: [
			{ arg: 'before', type: 'date', required: true, description: 'Keep events AFTER this date' },
		],
		returns: { type: 'object', root: true },
	})

	Resource.remoteMethod('prototype.calendarEvents', {
		description: 'Get Events for Calendar of resource',
		http: { path: '/calendars/events', verb: 'get' },
		accepts: [
			{ arg: 'from', type: 'date' },
			{ arg: 'to', type: 'date' },
		],
		returns: { type: 'object', root: true },
	})

	Resource.remoteMethod('prototype.calendarPermission', {
		description: 'Get permissions for Calendar of resource',
		http: { path: '/calendars/permission', verb: 'get' },
		accepts: [],
		returns: { type: 'object', root: true },
	})

	Resource.remoteMethod('prototype.setCalendarPermission', {
		description: 'Add permission for Calendar of resource',
		http: { path: '/calendars/permission', verb: 'post' },
		accepts: [
			{ arg: 'role', type: 'string', required: true, description: 'none|freeBusyReader|reader|writer|owner' },
			{ arg: 'type', type: 'string', required: true, description: 'scope type: default|user|group|domain' },
			{ arg: 'value', type: 'string', description: 'scope value: email address of a user or group, or the name of a domain, depending on the scope type' },
		],
		returns: { type: 'object', root: true },
	})
}
