/**
 *  @module Mixin:Provision - Product & Variant
 */
const { Products, Fulfillments } = require('@crm/types'),
	{ StoredValues } = require('@perkd/provisions')

const { STOREDVALUE } = Products.Kind,
	{ HIDE } = Products.Availability,
	{ PERKD } = Products.Channel,
	{ CONTINUE } = Products.Policy,
	{ BASE, MAX } = Products.PriceName,
	{ DIGITAL } = Fulfillments.Service,
	{ TOPUP, PRELOAD } = StoredValues.Type,
	// labels
	VARIABLE = 'Variable',
	DENOMINATION = 'Denomination'

module.exports = function(Product) {

	/**
	 * Stored Value - create Product & Variants
	 * @param {String} type - topup | preload
	 * @param {Object} currency
	 * 			{String} code
	 * 			{String} symbol
	 * 			{Number} precision
	 * 			{Number} exchangeRate
	 * @param {Number[]} denominations - eg. [ 10, 25, 50 ]
	 * @param {Object} [variable]
	 * 			{Number} min - lowest topup value
	 * 			{Number} [max] - highest topup value
	 * 			{Number} [increment] - of value
	 * @returns {Object[]} list of { productId, variantId }
	 */
	Product.provisionStoredValue = async function (type, currency = {}, denominations = [], variable) {
		const { Variant } = this.app.models,
			sorted = denominations.sort((a, b) => a - b),
			{ code, symbol = '', precision, exchangeRate = 1 } = currency,
			min = Math.min(variable?.min || Infinity, Math.min(...sorted)),
			max = Math.max(variable?.max || 0, Math.max(...sorted)),
			{ increment = 1 } = variable ?? {},
			inventory = {
				management: PERKD,
				policy: CONTINUE,
				lowQuantityWarningThreshold: 0,
				quantity: 0
			},
			title = type.charAt(0).toUpperCase() + type.slice(1),	// initial cap
			data = {
				title,
				priceRange: { min, max, currency: code },
				visible: false,
				availability: HIDE,
				tags: { category: [ STOREDVALUE ] }
			},
			{ id: productId } = await Product.create(data),
			products = []

		// create variants
		let pos = 1

		for (const value of sorted) {
			const label = `${symbol}${value}`,
				prices = [ {
					name: BASE,
					price: { value },
					notPaymentMethods: [ STOREDVALUE ]
				} ],
				data = {
					kind: STOREDVALUE,
					title: label,
					sku: `${type}-${value}`,
					channels: [ PERKD ],
					prices,
					inventory,
					storedValue: {
						balance: value * exchangeRate,
						currency: { code, precision }
					},
					fulfillmentService: DIGITAL,
					variations: [
						{ name: DENOMINATION, value: label }
					],
					visible: false,
					position: pos++,
					productId
				},
				variant = await Variant.create(data),
				{ id: variantId } = variant

			products.push({
				variantId: String(variantId),
			})
		}

		if (variable) {
			const prices = [
					{
						name: BASE,
						price: { value: min },
						increment,
						notPaymentMethods: [ STOREDVALUE ]
					},
					{
						name: MAX,
						price: { value: max },
					}
				],
				variant = {
					kind: STOREDVALUE,
					title: VARIABLE,
					sku: `${type}-${VARIABLE.toLowerCase()}`,
					channels: [ PERKD ],
					prices,
					inventory,
					fulfillmentService: DIGITAL,
					variations: [
						{ name: DENOMINATION, value: VARIABLE }
					],
					visible: false,
					position: pos++,
					productId
				}

			await Variant.create(variant)
		}

		return products
	}

	/**
	 * De-provision Stored Value
	 * @param {String} type - topup | preload
	 * @param {Object} currency
	 * 			{String} code
	 * 			{String} symbol
	 * 			{Number} precision
	 * 			{Number} exchangeRate
	 * @param {Number[]} denominations - eg. [ 10, 25, 50 ]
	 * @param {Object} [variable]
	 * 			{Number} min - lowest topup value
	 * 			{Number} [max] - highest topup value
	 * 			{Number} [increment] - of value
	 * @returns {Object[]} list of { productId, variantId }
	 */
	Product.deprovisionStoredValue = async function (type, currency = {}, denominations = [], variable) {
		const { Variant } = this.app.models,
			sorted = denominations.sort((a, b) => a - b),
			{ code, symbol = '', precision, exchangeRate = 1 } = currency,
			min = Math.min(variable?.min || Infinity, Math.min(...sorted)),
			max = Math.max(variable?.max || 0, Math.max(...sorted)),
			{ increment = 1 } = variable ?? {},
			inventory = {
				management: PERKD,
				policy: CONTINUE,
				lowQuantityWarningThreshold: 0,
				quantity: 0
			},
			title = type.charAt(0).toUpperCase() + type.slice(1),	// initial cap
			data = {
				title,
				priceRange: { min, max, currency: code },
				visible: false,
				availability: HIDE,
				tags: { category: [ STOREDVALUE ] }
			},
			product = await Product.create(data),
			{ id: productId } = product,
			products = []

		// create variants
		let pos = 1

		for (const value of sorted) {
			const label = `${symbol}${value}`,
				prices = [ {
					name: BASE,
					price: { value },
					notPaymentMethods: [ STOREDVALUE ]
				} ],
				data = {
					kind: STOREDVALUE,
					title: label,
					sku: `${type}-${value}`,
					channels: [ PERKD ],
					prices,
					inventory,
					storedValue: {
						balance: value * exchangeRate,
						currency: { code, precision }
					},
					fulfillmentService: DIGITAL,
					variations: [
						{ name: DENOMINATION, value: label }
					],
					visible: false,
					position: pos++,
					productId
				},
				variant = await Variant.create(data),
				{ id: variantId } = variant

			products.push({
				productId: String(productId),
				variantId: String(variantId),
			})
		}

		if (variable) {
			const prices = [
					{
						name: BASE,
						price: { value: min },
						increment,
						notPaymentMethods: [ STOREDVALUE ]
					},
					{
						name: MAX,
						price: { value: max },
					}
				],
				variant = {
					kind: STOREDVALUE,
					title: VARIABLE,
					sku: `${type}-${VARIABLE.toLowerCase()}`,
					channels: [ PERKD ],
					prices,
					inventory,
					fulfillmentService: DIGITAL,
					variations: [
						{ name: DENOMINATION, value: VARIABLE }
					],
					visible: false,
					position: pos++,
					productId
				}

			await Variant.create(variant)
		}

		return products
	}

	// -----  Remote Methods  -----

	Product.remoteMethod('provisionStoredValue', {
		description: 'Create Product & Variants for Stored Value Topup (provision only)',
		http: { path: '/provision/storedvalue', verb: 'post' },
		accepts: [
			{ arg: 'type', type: 'string', enum: [ TOPUP, PRELOAD ], required: true },
			{ arg: 'currency', type: 'object', required: true, description: '{ code, symbol, precision, exchangeRate }' },
			{ arg: 'denominations', type: [ 'number' ], description: 'list of amounts' },
			{ arg: 'variable', type: 'object', description: '{ min, [max], [increment] }' },
		],
		returns: { type: 'object', root: true },
	})
}
