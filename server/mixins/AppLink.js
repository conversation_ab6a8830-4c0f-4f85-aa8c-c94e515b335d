/**
 * @module Mixin: AppLink
 */
const { pickObj, shortId } = require('@perkd/utils'),
	{ PerkdWallet } = require('@perkd/sdk'),
	{ ApiRemote } = require('@perkd/api-request'),
	Perkd = new PerkdWallet()

// Environment
const {
	NODE_ENV,
} = process.env

/* TODO:
	test sites
	globalize sites
*/
const PRODUCT_PAGE = 'product',
	PROMO_PAGE = {
		Product: 'product_promo',
		Variant: 'variant_promo',
		Collection: 'collection_promo'
	},
	PRODUCT_ATTRIBUTES = [ 'title', 'description', 'brand', 'isSoldOut' ],
	VARIANT_ATTRIBUTES = [ 'title', 'position', 'sku', 'taxable', 'variations', 'options', 'minOrderQuantity' ],
	{ accessToken } = Perkd,
	ACTION_API = {
		url: `https://9wbqsw6uza.execute-api.us-west-2.amazonaws.com/${NODE_ENV === 'production' ? 'live' : 'test'}`, // action-link lambda
		headers: { 'x-access-token': `${accessToken}` },
	},
	ACTION_PREFIX = 'https://go.perkd.me',
	S3_PREFIX = 'https://s3.ap-southeast-1.amazonaws.com/microsite.perkd.me',
	FIREBASE_PREFIX = `https://perkd.page.link/?apn=${NODE_ENV === 'production' ? 'me.perkd' : 'me.perkd.dev'}&amv=151&link=`,
	BASE_PRICE = 'base',
	STATE = { ACTIVE: 'active', ENDED: 'ended' }

module.exports = function(Model) {

	/**
	 * Create Add-to-Bag product promotion page (microsite)
	 * @param	{object} data
	 * 			{string} title
	 * 			{string} description
	 * 			{string} campaignId
	 * 			{object} custom - { terms }
	 * @return	{Promise<object>} { id }
	 */
	Model.applinkAddToBag = async function(data) {
		const { MicrositeTemplate } = Model.app.models,
			{ campaignId } = data,
			kind = PROMO_PAGE.Collection

		data.name = `${kind}_${campaignId}_${shortId()}`
		return MicrositeTemplate.createMicrosite(kind, data)
	}

	/**
	 * Deploy Add-to-Bag microsite (optionally shortened uri)
	 * @param	{string} id - Microsite id
	 * @param	{string[]} productIds
	 * @param	{string} fallbackActionId - i.e. card add action
	 * @param	{date} [start]
	 * @param	{date} [end]
	 * @param	{boolean} shorten
	 * @param	{object} options
	 * 				{string} slug
	 * 				{string} state - 'ended'
	 * 				{string} shopUri // TODO: get shop url from program?
	 * @return	{Promise<object>} { id, slug (s3), uri: https://go.perkd.me/s1TBoH }
	 */
	Model.applinkAddToBagDeploy = async function(id, productIds, fallbackActionId, start, end, shorten, options = {}) {
		const { Microsite } = Model.app.models,
			[ data, site ] = await Promise.all([
				this.applinkPrepare(productIds, fallbackActionId, options),
				Microsite.findById(id),
			]),
			{ uri } = await site.deploy(data, end)

		if (!shorten) return { id, uri }

		const shortUri = await applink(getResource(uri), start, end, options.slug),
			slug = getSlug(shortUri)

		return { id, slug, uri: shortUri }
	}

	Model.applinkPrepare = async function(productIds, fallbackActionId, options = {}) {
		const { Business, ProductImage } = Model.app.models,
			{ state, shopUri } = options,
			imageFilter = {
				where: { ownerId: { inq: productIds } },
				order: 'position ASC',
			},
			businessFilter = {
				where: { isMain: true },
				fields: [ 'id', 'name' ],
			},
			filter = {
				where: { id: { inq: productIds } },
				include: {
					relation: 'variants',
					scope: { where: { deletedAt: null }, order: 'position ASC' },
				},
			},
			[ action, images, business, products ] = await Promise.all([
				Perkd.actions.get(fallbackActionId),
				ProductImage.find(imageFilter),
				Business.findOne(businessFilter),
				Model.find(filter),
			]),
			{ cardMasterIds } = action.data,
			productList = products.map(p => p.toJSON()),
			links = {}

		for (const product of productList) {
			const { variants = [] } = product

			for (const variant of variants) {
				const payload = Model.applinkAction(cardMasterIds, product, variant, images),
					addtobag = await Perkd.actions.uri(fallbackActionId, payload)

				links[variant.id] = addtobag
			}
		}

		return applinkSiteDataMany(productList, business, images, links, state, shopUri)
	}

	Model.applinkAddToBagUndeploy = async function(id, slug) {
		const { Microsite } = Model.app.models,
			site = await Microsite.findById(id)

		await applinkDelete([ slug ])
		return site.undeploy()
	}

	Model.applinkAddToBagDelete = function(id) {
		const { Microsite } = Model.app.models

		return Microsite.destroyById(id)
	}

	Model.applinkAction = function(cardMasterIds, product = {}, variant = {}, images = []) {
		const { title, external, sku, prices = [], imageIds = [] } = variant,
			productImages = images.filter(i => String(i.ownerId) === String(product.id)),
			image = (imageIds.length ? productImages.find(i => imageIds.includes(i.id.toString())) : productImages[0])?.original?.url,
			price = prices.find(p => p.name === BASE_PRICE),
			{ salePrice } = price,
			unitPrice = typeof salePrice.value === 'number' ? salePrice.value : price.price.value, // salePrice of undefined
			action = {
				object: 'bag',
				action: 'additems',
				data: {
					cardMasterIds,
					cardRequired: true,
					items: [ {
						variantId: external.variantId,
						title: `${product.title} - ${title}`,
						productId: product.external.productId,
						sku,
						unitPrice,
						quantity: 1,
						images: image ? [ image ] : undefined,
						tags: product.tags.keyword,
					} ],
				},
			}

		return action
	}

	Model.applinkSiteData = function(product, variants = [], business, images, addtobagLinks, state, shopUri) {
		const android = { uri: `${FIREBASE_PREFIX}${shopUri}` },
			shop = shopUri ? { uri: shopUri, android } : {},
			siteData = {
				business: { name: business.name },
				products: [],
				shop,
				state: state || STATE.ACTIVE,
			},
			variantsAction = variants.map(variant => {
				const { id, prices, gtin, weight, inventory, imageIds = [] } = variant,
					price = prices.find(p => p.name === BASE_PRICE),
					action = {
						addtobag: { uri: addtobagLinks[id] },
						android: { uri: `${FIREBASE_PREFIX}${addtobagLinks[id]}` },
					},
					productImages = images.filter(i => String(i.ownerId) === String(product.id)),
					variantImages = (imageIds.length
						? productImages.filter(i => imageIds.includes(String(i.id)))
						: productImages)
						.map(i => i.original.url)

				return {
					...pickObj(variant, VARIANT_ATTRIBUTES),
					gtin: gtin.value,
					grams: weight?.grams,
					inventory: inventory.quantity,
					originalPrice: price.price.value,
					price: price.salePrice.value,
					images: variantImages,
					action: state !== STATE.ENDED ? action : undefined
				}
			}),
			products = {
				product: pickObj(product, PRODUCT_ATTRIBUTES),
				variants: variantsAction
			}

		siteData.products.push(products)
		return siteData
	}

	// -----  Instance Methods  -----

	/**
	 * Refresh Perkd applinks & related content
	 *	1. get action link from 'X'
	 *	2. request Microsite service to deploy(name, data, options)
	 *	3. shorten S3/microsite URL returned
	 *	4. save URL to 'buylink' property
	 *		Note:  'ElastiCache' mixin will auto-push to cache/redis AFTER this
	 * @param	{string} fallbackActionId
	 * @return	{Promise<string>} uri
	 */
	Model.prototype.applinkRefresh = async function(fallbackActionId) {
		const { id } = this,
			{ Microsite } = Model.app.models,
			kind = Model.name.toLowerCase(),
			name = id.toString(),
			data = await this.applinkPrepare(fallbackActionId),
			{ uri } = await Microsite.deploy(kind, name, data),
			add2bagUri = await applink(getResource(uri))

		await this.updateAttributes({ add2bagUri })
		return add2bagUri
	}

	// ---  Add-to-Bag Methods  ---

	/**
	 * Create Add-to-Bag product promotion page (microsite)
	 * @param	{object} data
	 * 			{string} title
	 * 			{string} description
	 * 			{string} campaignId
	 * 			{object} custom - { terms }
	 * @return	{Promise<object>} { id }
	 */
	Model.prototype.applinkAddToBag = async function(data) {
		const { id } = this,
			{ app, name } = Model,
			{ MicrositeTemplate } = app.models,
			{ campaignId } = data,
			kind = PROMO_PAGE[name]

		data.name = `${kind}_${campaignId}_${id}`
		return MicrositeTemplate.createMicrosite(kind, data)
	}

	/**
	 * Deploy Add-to-Bag microsite (optionally shortened uri)
	 * @param	{string} id - Microsite id
	 * @param	{string} fallbackActionId - i.e. card add action
	 * @param	{date} [start]
	 * @param	{date} [end]
	 * @param	{boolean} shorten
	 * @param	{object} options
	 * 				{string} slug
	 * 				{string} state - 'ended'
	 * 				{string} shopUri // TODO: get shop url from program?
	 * @return	{Promise<object>} { id, slug, uri: https://go.perkd.me/s1TBoH }
	 */
	Model.prototype.applinkAddToBagDeploy = async function(id, fallbackActionId, start, end, shorten, options = {}) {
		const { Microsite } = Model.app.models,
			[ data, site ] = await Promise.all([
				this.applinkPrepare(fallbackActionId, options),
				Microsite.findById(id),
			])

		site.deploy(data, end).then(({ uri }) => {
			if (!shorten) return { id, uri }

			return applink(getResource(uri), start, end, options.slug)
				.then(shortUri => ({ id, slug: getSlug(shortUri), uri: shortUri }))
		})
	}

	if (!Model.prototype.applinkPrepare) {
		Model.prototype.applinkPrepare = function() {
			return this.serverErr('not_implemented', { method: 'prototype.applinkPrepare' })
		}
	}

	function applinkSiteDataMany(productList, business, images, links, state, shopUri) {
		const many = productList.map(product => Model.applinkSiteData(product, product.variants, business, images, links, state)),
			products = many.map(m => m.products[0]),
			[ first ] = products,
			siteData = {
				business: first.business,
				products,
				state: first.state,
				shop: first.shop
			}

		return siteData
	}

	async function applink(uri, start, end, id) {
		const api = new ApiRemote(),
		{ url, headers } = ACTION_API,
		config = { headers },
		data = {
			uri,
			start: start ? start.toISOString() : undefined,
			end: end ? end.toISOString() : undefined,
			id,
		}

		return api.post(url, data, config)
	}

	async function applinkDelete(ids) {
		const api = new ApiRemote(),
		{ url, headers } = ACTION_API,
		config = { headers, data: { ids } }

		return api.delete(url, config)
	}

	function getSlug(uri) {
		return uri.split(`${ACTION_PREFIX}/`).pop()
	}

	function getResource(uri) {
		return uri.split(S3_PREFIX).pop()
	}

	// TODO Auto-refresh applinks & content
	// TODO 'after delete', purge applinks & content

	// -----  Remote Methods  -----

	Model.remoteMethod('applinkAddToBag', {
		description: 'Create Add-to-Bag product collection promotion page (microsite)',
		http: { path: '/applink/addtobag', verb: 'post' },
		accepts: [
			{ arg: 'data', type: 'object', http: { source: 'body' }, required: true },
		],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod('applinkAddToBagDeploy', {
		description: 'Generate Add-to-Bag product collection promotion page',
		http: { path: '/applink/addtobag/:micrositeId/deploy', verb: 'post' },
		accepts: [
			{ arg: 'micrositeId', type: 'string', http: { source: 'path' }, required: true },
			{ arg: 'productIds', type: 'array', required: true },
			{ arg: 'fallbackActionId', type: 'string', required: true },
			{ arg: 'start', type: 'date' },
			{ arg: 'end', type: 'date' },
			{ arg: 'shorten', type: 'boolean', default: true },
			{ arg: 'options', type: 'object' },
		],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod('applinkAddToBagUndeploy', {
		description: 'Remove Add-to-Bag product promotion page & its assets on s3',
		http: { path: '/applink/addtobag/:micrositeId/undeploy', verb: 'post' },
		accepts: [
			{ arg: 'micrositeId', type: 'string', http: { source: 'path' }, required: true },
			{ arg: 'slug', type: 'string', required: true },
		],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod('applinkAddToBagDelete', {
		description: 'Delete Add-to-Bag product promotion page',
		http: { path: '/applink/addtobag/:micrositeId', verb: 'delete' },
		accepts: [
			{ arg: 'micrositeId', type: 'string', http: { source: 'path' }, required: true },
		],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod('prototype.applinkRefresh', {
		description: 'Refresh ',
		http: { path: '/applink/refresh', verb: 'post' },
		accepts: [
			{ arg: 'fallbackActionId', type: 'string', required: true },
		],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod('prototype.applinkAddToBag', {
		description: 'Create Add-to-Bag product promotion page (microsite)',
		http: { path: '/applink/addtobag', verb: 'post' },
		accepts: [
			{ arg: 'data', type: 'object', http: { source: 'body' }, required: true },
		],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod('prototype.applinkAddToBagDeploy', {
		description: 'Generate Add-to-Bag product promotion page',
		http: { path: '/applink/addtobag/:micrositeId/deploy', verb: 'post' },
		accepts: [
			{ arg: 'micrositeId', type: 'string', http: { source: 'path' }, required: true },
			{ arg: 'fallbackActionId', type: 'string', required: true },
			{ arg: 'start', type: 'date' },
			{ arg: 'end', type: 'date' },
			{ arg: 'shorten', type: 'boolean', default: true },
			{ arg: 'options', type: 'object' },
		],
		returns: { type: 'object', root: true },
	})
}
