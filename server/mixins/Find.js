/**
 *  @module Mixin:Find
 */
const { Fulfillments } = require('@crm/types')
const { statusOf } = require('@perkd/orders')

const { ERROR, FAILURE } = Fulfillments.Status

module.exports = function(Order) {

	Order.findOneByProviderOrderId = async function(provider = '', orderId, where = {}) {
		const externalId = orderId ? String(orderId) : null,
			key = `external.${provider.toLowerCase()}.orderId`,
			filter = {
				where: {
					[key]: externalId,
					...where
				},
			}

		return Order.findOne(filter)
	}

	/**
	 * Get Pending (payment) order for payment Transaction id
	 * @param	{String} transactionId - of payment
	 * @param	{String} [sourceType] - Business | Program | Variant
	 * @return {Order} order (with status injected)
	 */
	Order.findByTransaction = async function(transactionId, sourceType) {
		const filter = {
				where: {
					sourceType,
					'billingList.paymentMethod.transactionId': transactionId || null,
				},
				order: 'createdAt DESC',
			},
			order = await Order.findOne(filter)

		if (order) {
			order.status = statusOf(order)
			return order
		}
		return null
	}

	/**
	 * Find main fulfillment of order
	 * @param	{String} orderId
	 * @return	{Fulfillment}
	 */
	Order.findMainFulfillment = async function(orderId) {
		const { Fulfillment } = Order.app.models,
			filter = {
				where: {
					orderId,
					mainFulfillmentId: { exists: false }
				}
			}

		return Fulfillment.findOne(filter)
	}

	/**
	 * All Orders (with main fulfillment) of store for period
	 * 	NOTE: excludes kitchen fulfillments
	 * @param	{String} storeId
	 * @param	{String[]} [types] of fulfillment (omit for all types)
	 * @param	{Date} from start time
	 * @param	{Date} to end time
	 * @param	{String[]} fields of fulfillment
	 */
	Order.allListForPeriod = async function (storeId, types, from, to, fields) {
		const filter = {
			where: {
				storeId,
				createdAt: { between: [ from, to ] }
			},
			order: 'createdAt ASC',
			include: {
				relation: 'fulfillments',
				scope: {
					where: {
						or: [
							{ mainFulfillmentId: { exists: false } },		// main only
							{ status: { nin: [ ERROR, FAILURE ] } },		// kitchen with error
						]
					},
					fields
				}
			},
		}

		if (types) {
			filter.include.scope.where.type = { inq: types }
		}

		const list = await Order.find(filter),
			filtered = list.filter(o => {
				const orderFulfillments = o.fulfillments() // Get fulfillments for the current order

				// Exclude orders with no fulfillments
				if (!orderFulfillments || orderFulfillments.length === 0) {
					return false
				}

				// If 'types' are specified, at least one fulfillment must match one of the types
				if (types) {
					return orderFulfillments.some(f => types.includes(f.type))
				}

				// If no 'types' are specified, and the order has fulfillments, include it
				return true
			}),
			trimmed = filtered.map(o => {
				const { itemList, discountList, billingList, fulfillments, ...order } = o.toJSON(),
					hasError = fulfillments.some(f => f.status === ERROR || f.status === FAILURE)

				order.fulfillments = fulfillments.filter(f => !f.mainFulfillmentId)		// main only, exclude kitchen

				if (hasError && order.fulfillments.length > 0) {
					order.fulfillments[0].status = ERROR
				}

				return order
			})

		return trimmed
	}
}
