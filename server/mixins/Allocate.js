/**
 *  @module Mixin:Allocate for Resource model
 *
 *  Resource Allocation System
 *  ==========================
 *
 *  Resource Timing & Availability
 *  ------------------------------
 *  The system tracks resource availability considering multiple factors:
 *
 *  1. Current Occupancy:
 *     - Active orders (using estimatedEndsAt)
 *     - Current bookings (started but not ended)
 *     Both are processed in parallel for efficiency
 *
 *  2. Future Commitments:
 *     - Future bookings
 *     - Minimum occupancy duration requirements
 *     - Sequential processing to find next valid time slot
 *
 *  3. Availability Calculation:
 *     The system uses a two-step process:
 *     a) Initial Availability:
 *        - Takes maximum of current time and all current activities' end times
 *        - Provides baseline for availability calculation
 *
 *     b) Future Availability:
 *        - Checks each future booking sequentially
 *        - If sufficient time exists before booking (>= minDuration):
 *          * Uses current time slot
 *          * Sets nextUnavailableAt to booking start
 *        - If insufficient time:
 *          * Skips to after the booking
 *          * Continues checking next booking
 *
 *  4. Time Windows:
 *     - availableAt: Earliest time when sufficient occupancy duration is guaranteed
 *     - nextUnavailableAt: When the available window ends (undefined if no restrictions)
 *
 *  Performance Optimizations:
 *  -------------------------
 *  1. Parallel Processing:
 *     - All resources processed concurrently
 *     - Current orders and bookings fetched in parallel
 *
 *  2. Efficient Data Access:
 *     - Single query per data type (orders, current bookings, future bookings)
 *     - Results ordered appropriately (DESC/ASC) to minimize processing
 *
 *  3. Smart Calculations:
 *     - Uses Math.max for efficient initial availability calculation
 *     - Early breaks when valid slots are found
 *     - Minimal object creation and manipulation
 *
 *  Queue Integration
 *  ----------------
 *  This timing system supports queue management by:
 *  - Providing accurate wait time estimates through parallel data processing
 *  - Ensuring sufficient occupancy time with minimum duration checks
 *  - Preventing booking conflicts via nextUnavailableAt tracking
 *  - Enabling efficient resource assignment decisions
 *
 *  Key Methods:
 *  ------------
 *  - candidates(): Finds suitable resource combinations for a party size
 *  - assign(): Attempts immediate seating for walk-in customers
 *  - getTimings(): Retrieves availability information for all resources
 *  - earliestAvailableTime(): Finds earliest available time & resources for a queue entry
 *
 *  Resource Combinations:
 *  --------------------
 *  The system supports combining multiple resources with:
 *  - Adjacent resource detection using Union-Find algorithm
 *  - Configurable maximum number of combinable resources
 *  - Smart scoring system considering:
 *    * Capacity utilization
 *    * Resource adjacency
 *    * Number of resources combined
 *
 *  Configuration:
 *  -------------
 *  Key settings (from BOOKING settings):
 *  - minSize: Minimum party size for resource combinations
 *  - maxCombined: Maximum number of resources to combine
 *  - minCapacity: Minimum capacity ratio (party size multiplier)
 *  - maxCapacity: Maximum capacity ratio (party size multiplier)
 *  - minDuration: Minimum occupancy duration in minutes
 */
const { Products, Bookings, Settings } = require('@crm/types'),
	{ dayjs } = require('@perkd/utils'),
	{ resourceToProduct } = require('@perkd/fulfillments')

const { ResourceKind, ResourceStatus } = Products,
	{ TABLE } = ResourceKind,
	{ STAFF } = Bookings.Source,
	{ BOOKING, LOCALE } = Settings.Name,
	{ AVAILABLE, RESERVED_SOON, OCCUPIED, UNAVAILABLE } = ResourceStatus,
	MAINTENANCE = 'maintenance',
	OVERSTAY_BUFFER = 0.2

// Weights for scoring functions
const
	WEIGHTS = {
		IMMEDIATE: {			// Only two factors that really matter for immediate seating
			CAPACITY_FIT: 0.6,	// Primary concern: right-sized resource
			ADJACENCY: 0.2,		// Secondary: customer comfort
			ISOLATION: 0.2		// Tertiary: customer privacy
		},
		QUEUE: {
			UTILIZATION: 0.6,	// Primary concern: right-sized resource
			ADJACENCY: 0.15,		// Secondary: customer comfort
			ISOLATION: 0.15,	// Tertiary: customer privacy
			MULTIPLE: 0.1		// Multiple penalty for more resources
		}
	},
	DEFAULT_MAX_CAPACITY = 1.5

module.exports = function(Resource) {

	/**
	 * Shortlist one or more resources based on party size and preferences
	 * @param	{Resource[]} available - list of available resources
	 * @param	{Number} partySize - number of people
	 * @param	{Object} preferences
	 *			{Boolean} allowCombined - to allow combined resources
	 *			{Boolean} adjacentOnly - to require adjacent resources
	 * @param	{Boolean} [immediate] - immediate seating
	 * @return	{Object[]} list of resource candidates:
	 * 			- resources: Resource[]
	 * 			- totalCapacity: number
	 * 			- isAdjacent: boolean
	 * 			- score: number
	 */
	Resource.candidates = function(available = [], partySize = 1, preferences = {}, immediate, occupiedTableIds = new Set()) {
		const { app } = Resource,
			{ minSize, maxCombined, minCapacity: min, maxCapacity: maxCapacitySetting } = app.getSettings(BOOKING)[TABLE],
			// Get effective maxCapacity based on party size
			max = getEffectiveMaxCapacity(partySize, maxCapacitySetting),
			minCapacity = Math.ceil(partySize * min),
			maxCapacity = Math.ceil(partySize * max),
			{ allowCombined, adjacentOnly } = preferences,
			scoringFn = immediate
				? (resources, pSize) => calculateImmediateScore(resources, pSize, occupiedTableIds)
				: (resources, pSize) => calculateScore(resources, pSize, occupiedTableIds)

		// Validate inputs
		if (!available.length) return []

		// Get single resource candidates
		const candidates = singleResourceCandidates(available, partySize, scoringFn)

		// Only consider combinations if party size >= minSize
		if (allowCombined && maxCombined >= 2 && partySize >= minSize) {
			candidates.push(...generateCombinations(
				available,
				partySize,
				maxCombined,
				adjacentOnly,
				scoringFn
			))
		}

		// Filter candidates ensuring they:
		// 1. For small parties (< minSize): just need to fit the party
		// 2. For larger parties: apply business rules for capacity
		// 3. Meet adjacency requirements if specified
		const validCandidates = candidates.filter(({ totalCapacity, isAdjacent }) =>
			(!adjacentOnly || isAdjacent)
			&& (partySize < minSize
				? totalCapacity >= partySize
				: totalCapacity >= minCapacity && totalCapacity <= maxCapacity)
		)

		// Sort by score (highest first)
		return validCandidates.sort((a, b) => b.score - a.score)
	}

	/**
	 * Attempt immediate resource assignment for walk-in customers
	 * @param	{Object[]} candidates - list of candidate resources
	 * @param	{Date} from
	 * @param	{Date} to
	 * @param	{Object} customer - { tier, personId, membershipId, cardId }
	 * @param	{Object} preferences
	 *			{Boolean} allowCombined - to allow combined resources
	 *			{Boolean} adjacentOnly - to require adjacent resources
	 * @param	{Object} [options]
	 *			{String} [note] - special request/instructions
	 *			{String} [source] - source of booking: queuing|website (omitted: in-app)
	 *			{Number} [ttl] - milliseconds to hold pending booking (for non-members)
	 * @return	{Object[]} list of assigned resources:
	 * 			- name: of resource
	 * 			- position: of resource
	 * 			- capacity: of resource
	 * 			- startTime: of booking
	 * 			- endTime: of booking
	 * 			- resourceId
	 * 			- bookingId
	 */
	Resource.assign = async function (candidates = [], from, to, partySize, customer = {}, preferences = {}, options) {
		const { app } = Resource,
			{ Event } = app,
			{ Booking, Place } = app.models,
			{ adjacentOnly } = preferences,
			assigned = [],
			{ ttl } = options ?? {}

		for (const { resources, isAdjacent } of candidates) {
			if (adjacentOnly && !isAdjacent) continue

			// Check if all resources are open for the given time period
			if (Resource.isOpen(resources, from, to).length !== resources.length) continue

			// Resources in a candidate set are guaranteed to have same timezone
			// because of validation in Resource.observe('before save')
			const timeZone = resources[0]?.timeZone || app.getSettings(LOCALE).timeZone,
				bookFrom = dayjs(from).tz(timeZone).toDate(),
				bookTo = dayjs(to).tz(timeZone).toDate(),
				products = resources.map(resourceToProduct)

			try {
				const { length: quantity } = products,
					price = undefined,
					instances = ttl
						? await Booking.request(products, bookFrom, bookTo, quantity, price, partySize, preferences, customer, options)
						: await Booking.requestAndConfirm(products, bookFrom, bookTo, quantity, price, partySize, preferences, customer, options),
					bookings = instances.map(b => {
						if (!b.toJSON) return b
						const { id, resourceId, ...rest } = b.toJSON()
						return { id: String(id), resourceId: String(resourceId), ...rest }
					}),
					[ first = {} ] = bookings,
					{ reservationId, placeId } = first,
					place = await Place.findById(placeId)

				if (!ttl) {
					appEmit(Event.sales.booking.reservation.confirmed, { reservationId, bookings, place })
				}

				for (const booking of bookings) {
					const { id: bookingId, startTime, endTime, resourceId, expiresAt, status, createdAt } = booking,
						{ name, position, capacity } = resources.find(r => String(r.id) === resourceId) ?? {},
						{ personId, membershipId } = customer

					assigned.push({
						name,
						position,
						capacity,
						startTime,
						endTime,
						expiresAt,
						status,
						resourceId,
						bookingId,
						reservationId,
						personId,
						membershipId,
						placeId,
						createdAt
					})
				}
				break
			}
			catch (error) {
				console.error('[Resource.assign] skipped to next candidate set', error)
				continue // Try next candidate set
			}
		}

		return assigned
	}

	/**
	 * Get complete list of resources of kind with estimated next available time
	 * @param	{String} placeId - place id
	 * @param	{String} kind - kind of resource
	 * @param	{Date} [endTime] - optional limit for future bookings
	 * @return	{Object[]} list of resource candidates with timing info:
	 *           - id: resource identifier
	 *           - name: resource name
	 *           - capacity: seating capacity
	 *           - position: location information
	 *           - status: current status of the table ('available', 'reserved-soon', 'occupied', 'unavailable')
	 *                 'reservedsoon' indicates a booking will start within minDuration minutes
	 *           - unavailableReason: reason why the table is unavailable (only present when status is 'unavailable')
	 *           - currentOrders: active orders with estimatedEndsAt
	 *           - currentBookings: ongoing bookings (started but not ended)
	 *           - futureBookings: upcoming bookings
	 *           - availableAt: earliest time with guaranteed minimum duration
	 *           - nextUnavailableAt: when slot ends (undefined if no restriction)
	 */
	Resource.getTimings = async function(placeId, kind = TABLE, endTime) {
		const { app } = Resource,
			{ Booking } = app.models,
			{ minDuration, maxDuration } = app.getSettings(BOOKING)[kind],
			{ timeZone: TIMEZONE } = app.getSettings(LOCALE),
			now = new Date()

		// 1. Get all resources of kind
		const resources = await Resource.find({
			where: { placeId, kind },
			order: 'capacity ASC'
		})
		if (!resources.length) return []

		// 2. Get timing information for each resource
		const resourceTimings = resources.map(async resource => {
			// Get all current activities for this resource
			const { id, name, capacity, position, timeZone = TIMEZONE } = resource.toJSON(),
				resourceId = String(id),
				resourceNow = dayjs(now).tz(timeZone).valueOf(),
				{ currentOrders = [], currentBookings = [], futureBookings = [] } = await Booking.bookingsAndOrders(resourceId, timeZone, endTime)

			// Calculate initial availability from current activities
			const orderEndTimes = currentOrders.map(o => dayjs(o.when?.fulfilled || o.estimatedEndsAt).tz(timeZone).valueOf()),
				bookingEndTimes = currentBookings.map(b => dayjs(b.departedAt || b.endTime).tz(timeZone).valueOf()),
				initialAvailableAt = Math.max(resourceNow, ...orderEndTimes, ...bookingEndTimes)

			// Find next valid time slot considering minimum duration
			let availableAt = dayjs(initialAvailableAt).tz(timeZone).toDate()
			let nextUnavailableAt = undefined

			// Process future bookings to find valid slot
			for (let i = 0; i < futureBookings.length; i++) {
				const booking = futureBookings[i]
				const bookingStart = dayjs(booking.startTime).tz(timeZone)
				const bookingEnd = dayjs(booking.endTime).tz(timeZone)
				const timeUntilBooking = bookingStart.diff(dayjs(availableAt).tz(timeZone), 'minute')

				if (timeUntilBooking >= minDuration) {
					// Found valid slot before this booking
					nextUnavailableAt = bookingStart.toDate()
					break
				}
				else {
					// Skip to after this booking
					availableAt = bookingEnd.toDate()
					// If no more bookings after this, slot is valid until closing
					if (!futureBookings[i + 1]) {
						nextUnavailableAt = undefined
						break
					}
				}
			}

			// Determine table status and reason if unavailable using the helper function
			const { status, unavailableReason } = determineResourceStatus(
				currentBookings,
				currentOrders,
				futureBookings,
				resourceNow,
				timeZone,
				minDuration
			)

			return {
				id: resourceId,
				name,
				capacity,
				position,
				status,
				unavailableReason,
				availableAt,
				nextUnavailableAt,
				maxDuration,
				minDuration,
				currentOrders,
				currentBookings,
				futureBookings,
				timeZone
			}
		})

		// Sort results by status priority and then by additional criteria
		return Promise.all(resourceTimings).then(results => {
			// Define status priority (available first, then reserved-soon, etc.)
			const statusPriority = {
				[AVAILABLE]: 1,
				[RESERVED_SOON]: 2,
				[OCCUPIED]: 3,
				[UNAVAILABLE]: 4
			}

			// First, group tables by reservationId for RESERVED_SOON and OCCUPIED statuses
			const reservationGroups = new Map()

			// Extract reservationId for each table with RESERVED_SOON or OCCUPIED status
			results.forEach(resource => {
				if (resource.status === RESERVED_SOON || resource.status === OCCUPIED) {
					const reservationId = resource.currentBookings[0]?.reservationId
						|| resource.futureBookings[0]?.reservationId
						|| ''

					if (!reservationGroups.has(reservationId)) {
						reservationGroups.set(reservationId, [])
					}
					reservationGroups.get(reservationId).push(resource)
				}
			})

			// Sort tables within each reservation group by position.value
			reservationGroups.forEach(group => {
				group.sort((a, b) => {
					const aPosition = Array.isArray(a.position) ? a.position[0] : null
					const bPosition = Array.isArray(b.position) ? b.position[0] : null

					const aPositionValue = aPosition?.value || ''
					const bPositionValue = bPosition?.value || ''

					// Try to compare as numbers if possible
					const aNumValue = parseInt(aPositionValue, 10)
					const bNumValue = parseInt(bPositionValue, 10)

					if (!isNaN(aNumValue) && !isNaN(bNumValue)) {
						return aNumValue - bNumValue
					}

					// Fall back to string comparison
					return aPositionValue.localeCompare(bPositionValue)
				})
			})

			return results.sort((a, b) => {
				// First sort by status
				if (statusPriority[a.status] !== statusPriority[b.status]) {
					return statusPriority[a.status] - statusPriority[b.status]
				}

				// For AVAILABLE status, sort by position.value (small to big)
				if (a.status === AVAILABLE) {
					// Extract position values - position is an array of objects with key and value properties
					const aPosition = Array.isArray(a.position) ? a.position[0] : null
					const bPosition = Array.isArray(b.position) ? b.position[0] : null

					const aPositionValue = aPosition?.value || ''
					const bPositionValue = bPosition?.value || ''

					// Try to compare as numbers if possible
					const aNumValue = parseInt(aPositionValue, 10)
					const bNumValue = parseInt(bPositionValue, 10)

					if (!isNaN(aNumValue) && !isNaN(bNumValue)) {
						return aNumValue - bNumValue
					}

					// Fall back to string comparison
					return aPositionValue.localeCompare(bPositionValue)
				}

				// For RESERVED_SOON and OCCUPIED, first group by reservationId, then sort by availableAt
				if ((a.status === RESERVED_SOON || a.status === OCCUPIED)
					&& (b.status === RESERVED_SOON || b.status === OCCUPIED)) {

					// Find reservationIds
					const aReservationId = a.currentBookings[0]?.reservationId
						|| a.futureBookings[0]?.reservationId
						|| ''
					const bReservationId = b.currentBookings[0]?.reservationId
						|| b.futureBookings[0]?.reservationId
						|| ''

					// If they have different reservationIds, sort by availableAt time (earliest first)
					if (aReservationId !== bReservationId) {
						return a.availableAt.getTime() - b.availableAt.getTime()
					}
				}

				// Default sort by availableAt time (earliest first)
				return a.availableAt.getTime() - b.availableAt.getTime()
			})
		})
	}

	/**
	 * Find earliest available time & resources for a queue entry
	 * @param {String} kind - Kind of resource
	 * @param {Array} resources - Available resources
	 * @param {Number} capacity - Required capacity
	 * @param {Number} minDuration - Minimum duration in minutes
	 * @param {Object} preferences - Preferences
	 *			{Boolean} allowCombined - Whether to allow combined resources
	 *			{Boolean} adjacentOnly - Whether to require adjacent resources
	 * @return {Object|undefined} { scheduledAt: Date, allocatedResources: Array<number> }
	 */
	Resource.earliestAvailableTime = function(kind, resources, capacity, minDuration, preferences) {
		const { app } = Resource,
			{ timeZone } = app.getSettings(LOCALE),
			{ maxCombined } = app.getSettings(BOOKING)[kind],
			{ allowCombined } = preferences

		const now = new Date().getTime()
		const overstayBuffer = Math.ceil(minDuration * OVERSTAY_BUFFER) * 60000 // Convert to milliseconds

		let earliestAvailableTime
		let allocated = []
		let originalAvailableAt
		let adjustedAvailableAt

		if (allowCombined) {
			const combinations = generateCombinationsForQueue(resources, capacity, maxCombined)

			for (const combo of combinations) {
				// Count overstayed resources in this combination
				const overstayedCount = combo.filter(r => r.availableAt.getTime() < now).length

				// Get original availableAt times for each resource in the combo
				const originalTimes = combo.map(r => r.availableAt.getTime())
				const comboOriginalTime = Math.max(...originalTimes)

				// Calculate adjusted available time for the combination
				// For each resource, use max(availableAt, now) + buffer if overstayed
				const adjustedTimes = combo.map(r => {
					const isOverstayed = r.availableAt.getTime() < now
					return isOverstayed
						? now + overstayBuffer
						: r.availableAt.getTime()
				})
				const comboAdjustedTime = Math.max(...adjustedTimes)

				// If we have no time yet OR this combo has earlier adjusted time OR
				// same time but fewer overstayed resources, use this combo
				if (!earliestAvailableTime
					|| comboAdjustedTime < earliestAvailableTime.time
					|| (comboAdjustedTime === earliestAvailableTime.time
						&& overstayedCount < earliestAvailableTime.overstayedCount)) {

					earliestAvailableTime = {
						time: comboAdjustedTime,
						originalTime: comboOriginalTime,
						overstayedCount
					}
					allocated = combo.map(r => resources.indexOf(r))
				}
			}
		}
		else {
			for (let i = 0; i < resources.length; i++) {
				const resource = resources[i]
				const { availableAt, nextUnavailableAt } = resource

				// Check if resource is overstayed
				const isOverstayed = availableAt.getTime() < now

				// Adjust availableAt to be at least the current time + buffer if overstayed
				const adjustedAvailableAtTime = isOverstayed
					? now + overstayBuffer
					: availableAt.getTime()

				// Check if there's enough time between adjusted availableAt and nextUnavailableAt
				const hasEnoughTime = !nextUnavailableAt
					|| (nextUnavailableAt.getTime() - adjustedAvailableAtTime) >= minDuration * 60000

				if (hasEnoughTime && resource.capacity >= capacity) {
					// If we have no time yet OR this resource has earlier adjusted time OR
					// same time but not overstayed (prefer non-overstayed)
					if (!earliestAvailableTime
						|| adjustedAvailableAtTime < earliestAvailableTime.time
						|| (adjustedAvailableAtTime === earliestAvailableTime.time
							&& !isOverstayed && earliestAvailableTime.isOverstayed)) {

						earliestAvailableTime = {
							time: adjustedAvailableAtTime,
							originalTime: availableAt.getTime(),
							isOverstayed
						}
						allocated = [ i ]
					}
				}
			}
		}

		if (!earliestAvailableTime) return undefined

		// Convert timestamps to Date objects
		const scheduledAt = dayjs(earliestAvailableTime.time).tz(timeZone).toDate()
		originalAvailableAt = dayjs(earliestAvailableTime.originalTime).tz(timeZone).toDate()
		adjustedAvailableAt = scheduledAt

		return { scheduledAt, allocated }
	}

	// ---  Remote Methods  ---

	Resource.remoteMethod('getTimings', {
		description: 'Get complete list of resources of kind with their status and timing information',
		http: { verb: 'get', path: '/timings' },
		accepts: [
			{ arg: 'placeId', type: 'string', required: true },
			{ arg: 'kind', type: 'string', enum: [ TABLE ], required: true },
			{ arg: 'endTime', type: 'date', required: false, description: 'Optional limit for future bookings' }
		],
		returns: {
			type: 'array',
			root: true,
			description: 'List of resources with status and timing information'
		}
	})
}

/**
 * Find single resource candidates
 * @param	{Resource[]} available - list of available resources
 * @param	{Number} partySize - number of people
 * @return	{Object[]} list of resource candidates
 */
function singleResourceCandidates(available, partySize, scoringFn) {
	const candidates = []

	for (const resource of available) {
		if (resource.capacity >= partySize) {
			candidates.push({
				resources: [ resource ],
				totalCapacity: resource.capacity,
				isAdjacent: true,
				score: scoringFn([ resource ], partySize)
			})
		}
	}
	return candidates
}

/**
 * Generate all valid combinations up to maxCombined  (recursive)
 * @param	{Resource[]} available - list of available resources
 * @param	{Number} partySize - number of people
 * @param	{Number} maxCombined - maximum number of resources to consider (undefined will cause infinite loop!)
 * @param	{Boolean} adjacentOnly - whether to consider only adjacent resources
 * @param	{Function} scoringFn - scoring function
 * @param	{Number} startIndex - index of the first resource to consider
 * @param	{Resource[]} combinations - current combination of resources
 * @param	{Number} currentCapacity - total capacity of the current combination
 * @return	{Object[]} list of resource candidates
 */
function generateCombinations(available = [], partySize, maxCombined = 4, adjacentOnly, scoringFn, startIndex = 0, combinations = [], currentCapacity = 0) {
	const candidates = []

	if (combinations.length >= maxCombined) return candidates

	for (let i = startIndex; i < available.length; i++) {
		const resource = available[i],
			newCombo = [ ...combinations, resource ],
			newCapacity = currentCapacity + resource.capacity

		if (newCapacity >= partySize) {
			const isAdjacent = areResourcesAdjacent(newCombo)

			if (!adjacentOnly || isAdjacent) {
				candidates.push({
					resources: newCombo,
					totalCapacity: newCapacity,
					isAdjacent,
					score: scoringFn(newCombo, partySize)
				})
			}
		}

		candidates.push(...generateCombinations(
			available,
			partySize,
			maxCombined,
			adjacentOnly,
			scoringFn,
			i + 1,
			newCombo,
			newCapacity
		))
	}

	return candidates
}

/**
 * Generate valid resource combinations for queue management
 * @param {Array} resources - Available resources
 * @param {Number} requiredCapacity - Required total capacity
 * @param {Number} maxCombined - Maximum number of resources to combine
 * @return {Array} Array of valid resource combinations
 */
function generateCombinationsForQueue(resources, requiredCapacity, maxCombined) {
	const validCombinations = []

	function findCombinations(start, currentCombo, currentCapacity) {
		if (currentCapacity >= requiredCapacity) {
			validCombinations.push([ ...currentCombo ])
			return
		}

		if (currentCombo.length >= maxCombined) return

		for (let i = start; i < resources.length; i++) {
			const resource = resources[i]
			currentCombo.push(resource)
			findCombinations(i + 1, currentCombo, currentCapacity + resource.capacity)
			currentCombo.pop()
		}
	}

	findCombinations(0, [], 0)
	return validCombinations
}

/**
 * Calculate score for a resource combination
 * 	- Capacity utilization is most important
 * 	- Adjacent resources are strongly preferred
 * 	- Fewer resources are better
 * @param	{Resource[]} resources - list of resources
 * @param	{Number} partySize - number of people
 * @return	{Number} score
 */
function calculateScore(resources, partySize, occupiedTableIds = new Set()) {
	const { UTILIZATION, ADJACENCY, ISOLATION, MULTIPLE } = WEIGHTS.QUEUE,
		totalCapacity = resources.reduce((sum, r) => sum + r.capacity, 0),
		capacityDiff = totalCapacity - partySize

	// Capacity utilization score (0-1)
	// Perfect match = 1, Over/under capacity reduces score
	const utilizationScore = Math.max(0, 1 - (Math.abs(capacityDiff) / partySize))

	// Multiple resources penalty (0-0.3)
	const mutiplePenalty = (resources.length - 1) * 0.15

	// Adjacency bonus (0-0.2)
	const adjacencyBonus = areResourcesAdjacent(resources) ? 0.2 : 0

	// Average isolation score for the combination
	const totalIsolationScore = resources.reduce((sum, r) => sum + calculateIsolationScore(r.adjacentIds, occupiedTableIds), 0)
	const isolationScore = totalIsolationScore / resources.length

	// Combine scores with weights
	const finalScore = (utilizationScore * UTILIZATION)		// Capacity utilization is most important
		+ (adjacencyBonus * ADJACENCY)		// Adjacent resources are strongly preferred
		+ (isolationScore * ISOLATION)		// Isolated resources are also preferred
		- (mutiplePenalty * MULTIPLE)		// Fewer resources are better

	return Math.max(0, Math.min(1, finalScore))		// Ensure score is between 0 and 1
}

/**
 * Calculate immediate seating score for resource combination
 * @param {Object[]} resources - List of resources in combination
 * @param {Number} partySize - Number of guests
 * @return {Number} score between 0-1
 */
function calculateImmediateScore(resources, partySize, occupiedTableIds = new Set()) {
	const { CAPACITY_FIT, ADJACENCY, ISOLATION } = WEIGHTS.IMMEDIATE

	// Capacity scoring with thresholds
	const totalCapacity = resources.reduce((sum, r) => sum + r.capacity, 0)
	if (totalCapacity < partySize) return 0
	if (totalCapacity > partySize * 1.5) return 0.5

	const capacityScore = 1 - ((totalCapacity - partySize) / partySize),
		adjacencyScore = resources.length > 1
			? (areResourcesAdjacent(resources) ? 1 : 0)
			: 1

	// Average isolation score for the combination
	const totalIsolationScore = resources.reduce((sum, r) => sum + calculateIsolationScore(r.adjacentIds, occupiedTableIds), 0)
	const isolationScore = totalIsolationScore / resources.length

	return (capacityScore * CAPACITY_FIT)
		+ (adjacencyScore * ADJACENCY)
		+ (isolationScore * ISOLATION)
}

/**
 * Check if all resources in the combination are adjacent using Union-Find
 * @param	{Resource[]} resources - list of resources
 * @return	{Boolean} true if all resources are adjacent, false otherwise
 */
function areResourcesAdjacent(resources = []) {
	if (resources.length <= 1) return true

	// Simple Union-Find implementation
	const parent = {}

	// Find root with path compression
	function find(id) {
		if (parent[id] === undefined) parent[id] = id
		if (parent[id] !== id) {
			parent[id] = find(parent[id])
		}
		return parent[id]
	}

	// Union by linking
	function union(id1, id2) {
		parent[find(id1)] = find(id2)
	}

	// Process all adjacency relationships
	for (const resource of resources) {
		const id = String(resource.id)
		const adjacentIds = resource.adjacentIds?.map(String) || []

		// Only process adjacencies within our resource set
		for (const adjId of adjacentIds) {
			if (resources.some(r => String(r.id) === adjId)) {
				union(id, adjId)
			}
		}
	}

	// Check if all resources are in the same set
	const firstRoot = find(String(resources[0].id))
	return resources.every(resource => find(String(resource.id)) === firstRoot)
}

/**
 * Get the effective maxCapacity for a given party size
 * @param {Number} partySize - Number of people in the party
 * @param {Object} maxCapacitySetting - maxCapacity setting as an object with party size keys
 * @return {Number} The effective maxCapacity to use
 */
function getEffectiveMaxCapacity(partySize, maxCapacitySetting) {
	// If we have an exact match for this party size, use it
	if (maxCapacitySetting[partySize] !== undefined) {
		return maxCapacitySetting[partySize]
	}

	// For larger party sizes, use the largest defined value
	const keys = Object.keys(maxCapacitySetting).map(Number).sort((a, b) => a - b)
	if (keys.length > 0) {
		const maxKey = Math.max(...keys)

		if (partySize > maxKey && maxCapacitySetting[maxKey] !== undefined) {
			return maxCapacitySetting[maxKey]
		}
	}

	// Default fallback value if nothing else works
	return DEFAULT_MAX_CAPACITY
}

/**
 * Determine the status of a resource based on its current bookings, orders, and future bookings
 * @param {Array} currentBookings - Current bookings for the resource
 * @param {Array} currentOrders - Current orders for the resource
 * @param {Array} futureBookings - Future bookings for the resource
 * @param {Number} resourceNow - Current time in milliseconds for the resource's timezone
 * @param {String} timeZone - Timezone of the resource
 * @param {Number} minDuration - Minimum duration in minutes for bookings
 * @return {Object} { status, unavailableReason } - Status and reason if unavailable
 */
function determineResourceStatus(currentBookings, currentOrders, futureBookings, resourceNow, timeZone, minDuration) {
	// Helper function to check if a booking is upcoming within minDuration
	const isUpcomingSoon = booking => {
		const startTime = dayjs(booking.startTime).tz(timeZone).valueOf()
		const timeUntil = Math.floor((startTime - resourceNow) / (60 * 1000))
		return timeUntil <= minDuration
	}

	// Check for staff bookings first (highest priority)
	const staffBooking = currentBookings.find(booking => booking.source === STAFF)
	if (staffBooking) {
		return {
			status: UNAVAILABLE,
			unavailableReason: staffBooking.note || MAINTENANCE
		}
	}

	// Check for current activity (bookings or orders)
	if (currentBookings.length > 0 || currentOrders.length > 0) {
		return {
			status: OCCUPIED,
			unavailableReason: null
		}
	}

	// Check for upcoming staff bookings (maintenance)
	const upcomingStaffBooking = futureBookings.find(booking =>
		isUpcomingSoon(booking) && booking.source === STAFF
	)

	if (upcomingStaffBooking) {
		return {
			status: UNAVAILABLE,
			unavailableReason: upcomingStaffBooking.note || MAINTENANCE
		}
	}

	// Check for early arrivals - customers who have checked in before their booking start time
	const earlyArrivalBooking = futureBookings.find(booking =>
		booking.arrivedAt && booking.source !== STAFF
	)

	if (earlyArrivalBooking) {
		return {
			status: OCCUPIED,
			unavailableReason: null
		}
	}

	// Check for upcoming regular bookings
	const upcomingBooking = futureBookings.some(booking =>
		isUpcomingSoon(booking) && booking.source !== STAFF
	)

	if (upcomingBooking) {
		return {
			status: RESERVED_SOON,
			unavailableReason: null
		}
	}

	// Default status if none of the above conditions are met
	return {
		status: AVAILABLE,
		unavailableReason: null
	}
}

/**
 * Calculate isolation score for a table based on occupied adjacent tables and adjacency potential
 * @param {Array} adjacentIds - Array of adjacent table IDs
 * @param {Set} occupiedTableIds - Set of currently occupied table IDs
 * @return {Number} isolation score (0-1, where 1 is completely isolated)
 */
function calculateIsolationScore(adjacentIds = [], occupiedTableIds = new Set()) {
	// Tables with no adjacent tables are completely isolated
	if (!adjacentIds || adjacentIds.length === 0) {
		return 1.0
	}

	// Count how many adjacent tables are occupied
	const occupiedAdjacentCount = adjacentIds.filter(id => occupiedTableIds.has(id)).length

	// Primary factor: avoid occupied neighbors (customer privacy/comfort)
	const occupiedPenalty = occupiedAdjacentCount / adjacentIds.length

	// Secondary factor: preserve adjacent tables for combinations (10% penalty per adjacent table)
	const adjacencyPenalty = adjacentIds.length * 0.1

	// Combined score: prioritize avoiding occupied neighbors, then prefer fewer total neighbors
	return Math.max(0, 1 - occupiedPenalty - adjacencyPenalty)
}
