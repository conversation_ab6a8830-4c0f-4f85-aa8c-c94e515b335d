/**
 * @module Mixin:TableWidgetHand<PERSON>
 * Listens to the following events to confirm booking: membership.card.registered.booking, sales.order.paid.booking
 * Listens to booking events and emits widget data events
 * Handles (1) widget data updates & (2) booking confirmation for table bookings
 */
const { Products, Bookings, Wallet } = require('@crm/types')

const { TABLE } = Products.ResourceKind,
	{ PENDING, OPEN } = Bookings.Status,
	{ QUEUING } = Bookings.Source,
	{ BOOK_TABLE } = Wallet.Widgets.Key

module.exports = function(Product) {
	Product.widgetEventsHandler = function() {
		const { app } = this,
			{ Booking } = app.models,
			{ Event } = app

		app.on(Event.sales.booking.reservation.confirmed, ({ bookings, place }) => {
			const [ first = {} ] = bookings
			updateBookingWidget(first, OPEN, place)
		})

		// TODO: necessary?
		// app.on(Event.sales.booking.ended, ({ action, data }) => {
		// 	const { membershipId, kind } = data || {}
		// 	if (!membershipId || kind !== TABLE) return

		// 	updateBookingWidget(data, ENDED)
		// })

		app.on(Event.membership.card.reservation.registered, async ({ data }) => {
			console.log('membership.card.registered.reservation event received:', JSON.stringify(data))

			const { id: membershipId, personId, digitalCard, bookings } = data || {}
			const { id: cardId } = digitalCard ?? {}

			if (!bookings || bookings.length === 0) return

			const [ first = {} ] = bookings,
				{ reservationId } = first

			// Array to collect confirmed booking objects
			const confirmedBookings = []

			for (const booking of bookings) {
				try {
					const { id } = booking
					const customer = { personId, membershipId, cardId }
					const confirmedBooking = await Booking.confirm(id, undefined, customer)
					if (confirmedBooking) {
						confirmedBookings.push(confirmedBooking)
					}
				}
				catch (err) {
					appNotify('[TableWidgetHandler] Error confirming booking', { err, bookingId: booking.id, membershipId }, 'error')
				}
			}

			// Only emit the event if we have confirmed bookings
			if (confirmedBookings.length > 0) {
				const bookingsAsPlainObjects = confirmedBookings.map(booking =>
					booking.toJSON ? booking.toJSON() : booking
				)

				appEmit(Event.sales.booking.reservation.confirmed, {
					reservationId,
					bookings: bookingsAsPlainObjects
				})
			}
		})

		app.on(Event.sales.order.reservation.paid, async ({ data }) => {
			console.log('sales.order.paid.reservation event received:', JSON.stringify(data))
			const { id: orderId } = data || {},
				filter = {
					where: {
						orderId,
						kind: TABLE,
						status: PENDING,
						expiresAt: { gte: new Date() },
						endTime: { gte: new Date() }
					}
				},
				bookings = await Booking.find(filter)

			if (!bookings || bookings.length === 0) return

			const [ first = {} ] = bookings
			const { reservationId, membershipId, digitalCard } = first
			const { id: cardId } = digitalCard ?? {}

			// Array to collect confirmed booking objects
			const confirmedBookings = []

			for (const booking of bookings) {
				try {
					const { id, personId } = booking
					const customer = { personId, membershipId, cardId }
					const confirmedBooking = await Booking.confirm(id, orderId, customer)
					if (confirmedBooking) {
						confirmedBookings.push(confirmedBooking)
					}
				}
				catch (err) {
					appNotify('[TableWidgetHandler] Error confirming booking', { err, bookingId: booking.id, orderId }, 'error')
				}
			}

			// Only emit the event if we have confirmed bookings
			if (confirmedBookings.length > 0) {
				const bookingsAsPlainObjects = confirmedBookings.map(booking =>
					booking.toJSON ? booking.toJSON() : booking
				)

				appEmit(Event.sales.booking.reservation.confirmed, {
					reservationId,
					bookings: bookingsAsPlainObjects
				})
			}
		})
	}

	/**
	 * Update widget data for existing booking
	 * @param {Object} booking - The booking object
	 * @param {String} status - booking status (confirmed, cancelled, ended)
	 * @param {Object} place - The place object
	 */
	async function updateBookingWidget(booking, status, place) {
		try {
			const { app } = Product,
				{ Event, models } = app,
				{ Place } = models,
				{ reservationId, partySize, digitalCard, kind, placeId, startTime, endTime, expiresAt, createdAt } = booking,
				{ id: cardId } = digitalCard,
				{ name: placeName, addressList } = place || await Place.findById(placeId),
				now = new Date()

			if (kind !== TABLE) return
			if (!placeName) return

			// Skip widget data update for bookings with source 'queuing'
			const { source } = booking
			if (source === QUEUING) return

			appEmit(Event.widget.data.update, {
				key: BOOK_TABLE,
				cardId,
				reservations: [ {
					id: reservationId,
					partySize,
					status,
					startTime,
					expiresAt,
					endTime,
					place: { id: placeId, name: placeName, addressList },
					createdAt,
					modifiedAt: now
				} ],
				createdAt,
				modifiedAt: now
			})
		}
		catch (err) {
			appNotify('[TableWidgetHandler] update error', { err, booking, status }, 'error')
		}
	}
}
