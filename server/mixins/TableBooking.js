/**
 * @module Mixin:TableBooking
 * Mixin that adds table booking functionality to Product model.
 * Handles validation, availability checking, and table assignment for restaurant
 * table bookings with timezone support.
 */
const { dayjs } = require('@perkd/utils'),
	{ Products, Settings } = require('@crm/types'),
	{ PRODUCT: PRODUCT_ERR } = require('@perkd/errors/dist/service')

const { TABLE } = Products.ResourceKind,
	{ BOOKING, LOCALE } = Settings.Name,
	{ BOOKING_UNAVAILABLE } = PRODUCT_ERR

module.exports = function(Product) {

	/**
	 * Request Table Bookings for partySize at time with options
	 * 	1. Validate request
	 * 	2. Find available tables
	 * 	3. Get list of candidate tables (with score, most optimal first)
	 * 	4. Create booking
	 * @param	{Object} customer - { tier, personId, membershipId, cardId }
	 * @param	{String} placeId - place id
	 * @param	{Number} partySize - number of people
	 * @param	{Date} from - start time
	 * @param	{Boolean} [allowCombined] - accept combined tables
	 * @param	{Boolean} [adjacentOnly] - adjacent tables only
	 * @param	{String} [note] - special request/instructions
	 * @param	{Number} [ttl] - milliseconds to hold pending booking (for non-members)
	 * @return	{Object[]} list of { name, position, capacity, startTime, endTime, resourceId }
	 */
	Product.requestTableBookings = async function (customer, placeId, partySize, from, preferences = {}, options = {}) {
		const { app } = Product,
			{ Resource, Booking } = app.models,
			{ enabled, minPartySize, maxPartySize, minDuration, leadTime, maxAdvanceBooking } = app.getSettings(BOOKING)[TABLE],
			{ timeZone } = app.getSettings(LOCALE),
			nowInTz = dayjs().tz(timeZone), // Create nowInTz once
			fromInTz = dayjs(from).tz(timeZone),
			toInTz = fromInTz.add(minDuration, 'minutes').subtract(1, 'millisecond'),
			fromDate = fromInTz.toDate(),
			toDate = toInTz.toDate(),
			{ allowCombined, adjacentOnly } = preferences

		if (!enabled) throw 'NOT_ENABLED'

		// 1. Validate request
		if (!validateTimeWindow(fromInTz, nowInTz, leadTime, maxAdvanceBooking)) {
			throw 'INVALID_TIME_WINDOW'
		}
		if (partySize < minPartySize || partySize > maxPartySize) {
			throw 'INVALID_PARTY_SIZE'
		}

		// 2. Find available tables
		const { available, occupiedIds } = await Resource.available(placeId, TABLE, partySize, fromDate, minDuration, allowCombined)
		if (!available.length) throw BOOKING_UNAVAILABLE

		// 3. Get list of candidate tables
		const candidates = Resource.candidates(available, partySize, preferences, false, occupiedIds)
		if (!candidates.length) throw BOOKING_UNAVAILABLE

		// 4. Book tables
		const tables = await Resource.assign(candidates, fromDate, toDate, partySize, customer, preferences, options)

		return tables
	}

	/**
	 * Validate the booking time window against configured constraints
	 * @param {Dayjs} atInTz - Requested booking time (timezone-aware)
	 * @param {Dayjs} nowInTz
	 * @param {Number} leadTime - in minutes, minimum advance notice required for bookings
	 * @param {Number} maxAdvanceBooking - in days, maximum days in advance for bookings
	 * @returns {Boolean} Whether the time window is valid
	 *
	 * Validates that:
	 * - Booking time is in the future
	 * - Meets minimum lead time requirement
	 * - Doesn't exceed maximum advance booking window
	 */
	function validateTimeWindow(atInTz, nowInTz, leadTime, maxAdvanceBooking) {
		// Check if start time is in the future
		if (atInTz.isBefore(nowInTz)) return false

		// Check lead time requirement using timezone-aware diff
		const leadTimeMinutes = atInTz.diff(nowInTz, 'minute')
		if (leadTimeMinutes < leadTime) return false

		// Check maximum advance booking using timezone-aware diff
		const daysAhead = atInTz.diff(nowInTz, 'day')
		if (daysAhead > maxAdvanceBooking) return false

		return true
	}
}
