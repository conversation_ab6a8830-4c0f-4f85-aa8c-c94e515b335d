/**
 *  @module Mixin:Booking - handle bookings for Order mixin
 */
const { Bookings, Orders, Settings } = require('@crm/types'),
	{ roundAmount, hoursFor, date2TimeString, dayOfWeek, Time, openFrom, openUntil, getStartEndTime } = require('@perkd/utils')

const { MINUTE, DAY } = Bookings.Unit,
	{ BOOKING } = Orders.ItemKind,
	{ LOCALE } = Settings.Name,
	{ DEFAULT } = Settings,
	{ TIMEZONE } = DEFAULT

module.exports = function(Product) {

	/**
	 * Request bookings of order (atomic)						(used by Order mixin)
	 * 	- atomic booking for (booking) items of order, ie. all or none
	 * 	- substitute placeholder items in order with variants of actual committed booking
	 * 	- return bookings, item.custom.soldOut = true when fully booked
	 * @param	{mOrder} mOrder	- items mutated
	 * @return	{Object[]} list of booked
	 */
	Product.requestBookings = async function(mOrder) {
		const { app } = Product,
			{ Booking } = app.models,
			placeholderItems = mOrder
				.removeBookingItems()
				.sort((a, b) => b.quantity - a.quantity),		// fulfill larger quantities first
			requests = Product.buildBookingRequests(placeholderItems),
			booked = []

		for (const request of requests) {
			const { id, productIds, start, end, price, quantity } = request,
				filter = {
					where: { id: { inq: productIds } },
					include: {
						relation: 'resource',
					}
				},
				products = await Product.find(filter),
				list = products.map(({ id, priceRange, resource }) => ({ id, priceRange, resourceId: resource().id })),
				bookings = await Booking.request(list, start, end, quantity, price)

			for (const booking of bookings) {
				const { productId, startTime, endTime, createdAt } = booking,
					product = products.find(p => String(p.id) === productId)

				booking.startTime = new Date(startTime)
				booking.endTime = new Date(endTime)
				booking.createdAt = new Date(createdAt)
				booked.push({ id, booking, product })
			}
		}

		let completeRequest = true

		for (const item of placeholderItems) {
			const { quantity, capacity, custom, admit } = item,
				{ id, start, end } = custom,
				bookings = booked.filter(b => b.id === id),
				quantityBooked = bookings.reduce((tot, { booking }) => tot += booking.quantity, 0)

			if (quantityBooked === quantity) {
				const builds = []

				for (const { booking, product } of bookings) {
					const { id: bookingId } = booking
					builds.push(
						product.buildBookingItems(custom, new Date(start), new Date(end), bookingId, capacity, admit)
					)
				}

				const items = await Promise.all(builds)
				mOrder.addItems(items.flat())
			}
			else {
				completeRequest = false
				custom.shortfall = true
				item.quantity = quantityBooked		// quantityBooked === 0 => soldout
				mOrder.addItems([ item ])
			}
		}

		if (!completeRequest) {		// roll-back bookings
			for (const { booking } of booked) {
				await Booking.deleteById(booking.id)
			}
		}

		return booked
	}

	/**
	 * Convert occupied list into time periods (hours) with quantity, aggregated for exact same periods
	 * @param	{Object[]} occupied { resourceId, startTime, endTime, quantity }
	 * @param	{String} timeZone
	 * @return	{Object[]} { resourceId, open, close, quantity }
	 */
	Product.occupiedPeriods = function(occupied = [], timeZone) {
		return occupied.reduce((res, { resourceId, startTime, endTime, quantity }) => {
			const day = dayOfWeek(new Date(startTime), timeZone),
				open = { day, time: date2TimeString(startTime, timeZone) },
				close = { day, time: Time.add(date2TimeString(endTime, timeZone), 1, MINUTE) },
				exist = res.find(r => r.resourceId === resourceId
					&& r.open.day === open.day && r.open.time === open.time
					&& r.close.day === close.day && r.close.time === close.time)

			if (exist) {
				exist.quantity += quantity
			}
			else {
				res.push({ resourceId, open, close, quantity })
			}
			return res
		}, [])
	}

	/**
	 * Generate time periods (hours) for a specific day
	 * @param	{Object[]} resources { id, hours, capacity, shared }
	 * @param	{Number} year
	 * @param	{Number} month
	 * @param	{Number} day
	 * @return	{Object[]} { resourceId, hours, capacity, shared }
	 */
	Product.resourcePeriods = function(resources = [], year, month, day) {
		return resources.map(({ id, hours, capacity, shared }) => ({
			resourceId: String(id),
			hours: hoursFor(hours, year, month, day),
			capacity,
			shared
		}))
	}

	Product.buildBookingRequests = function(items = []) {
		const { timeZone = TIMEZONE } = Product.app.getSettings(LOCALE)

		return items.reduce((res, { quantity, price, custom }) => {
			const { id, from, duration, start, end, productIds } = custom,
				exist = res.find(r => r.id === id)

			if (exist) {
				exist.price += price
				exist.duration += duration
				exist.start = new Date(start).getTime() < exist.start.getTime() ? new Date(start) : exist.start
				exist.end = new Date(end).getTime() > exist.end.getTime() ? new Date(end) : exist.end
			}
			else {
				if (start) {
					custom.start = new Date(start)
					custom.end = new Date(end)
				}
				else {
					const { start, end } = getStartEndTime(from, duration, timeZone)
					custom.start = start
					custom.end = end
				}
				res.push({ ...custom, quantity, price })
			}
			return res
		}, [])
	}

	/**
	 * Build (order) items from selected variants applicable to time period, considering:
	 * 	- unitPriceMeasure (unit), unitPrice, minOrderQuantity
	 * 	- compute: quantity & price
	 * @param	{Object} book - definition of booking: { id, productIds, from, duration, quantity }
	 * @param	{Date} from - inclusive
	 * @param	{Date} to - inclusive of last unit
	 * @param	{String} [bookingId]
	 * @param	{Number} [capacity] - max number allowed (usually person)
	 * @param	{Number} [admit] - persons
	 */
	Product.prototype.buildBookingItems = async function(book, from, to, bookingId, capacity, admit) {
		const { title, tags = [] } = this,
			{ app } = Product,
			{ Pricing } = app.models,
			{ timeZone = TIMEZONE } = app.getSettings(LOCALE),
			variants = await this.variants(true),
			[ pricings, productImages ] = await Promise.all([
				Pricing.forVariants(variants),
				this.images.find({ fields: [ 'original' ] })
			]),
			applicable = Pricing.applicable(pricings, from, to),
			images = productImages.map(i => i.original.url),
			items = []

		for (const { pricing, start: priceStart, end: priceEnd } of applicable) {
			const { hours } = pricing,
				variantId = String(pricing.variantId),
				variant = variants.find(v => String(v.id) === variantId),
				{ sku, productId, unitPriceMeasure = {}, minOrderQuantity = 1 } = variant,
				{ unit = MINUTE } = unitPriceMeasure,
				start = unit === DAY ? openFrom(hours.toJSON(), priceStart, timeZone) : priceStart,
				end = unit === DAY ? openUntil(hours.toJSON(), priceStart, timeZone) : priceEnd,
				duration = Math.round((end.getTime() - start.getTime()) / (1000 * 60)) + 1, // in minutes
				units = pricing.units(start, end, unitPriceMeasure),
				quantity = Math.max(minOrderQuantity, 1),
				{ value: unitPrice, currency } = variant.priceAt(from),
				price = roundAmount(unitPrice * units * quantity, currency),
				item = {
					kind: BOOKING,
					title, images, unitPriceMeasure, unitPrice,
					units, quantity, price,
					capacity, admit, sku, tags,
					productId, variantId,
					custom: { ...book, start, end, duration, bookingId },
				}

			items.push(item)
		}

		return items
	}

	// ----  Private Functions  ----

	/**
	 * Get available Products & Resources of productIds for period
	 * @param	{String[]} productIds
	 * @param	{Date} start
	 * @param	{Date} end
	 * @return	{Object} { products, resources }
	 */
	Product.openForBooking = async function(productIds = [], start, end) {
		const { Resource } = Product.app.models,
			filter = {
				where: { productId: { inq: productIds } }
			},
			[ products, rsrcs ] = await Promise.all([
				Product.findWithVariantsByIds(productIds),
				Resource.find(filter)
			])

		if (!rsrcs.length) return { products, resources: [] }

		const { available: resources } = await Resource.availableBy(rsrcs, start, end)

		return { products, resources }
	}
}
