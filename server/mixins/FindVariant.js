/**
 * @module Mixin: FindVariant
 */
const { Providers } = require('@crm/types'),
	{ allItems } = require('@perkd/commerce')

const { SHOPIFY } = Providers.PROVIDER

module.exports = function(Variant) {

	/**
	 * Lookup (CRM) Variants from EXTERNAL variantId / sku / gtin  (ie. Shopify, SFCC, etc)
	 * @param	{Object[]} items of mOrder
	 * @return	{Promise<Variant[]>}
	 */
	Variant.findFromExternalIds = async function(items = [], provider = SHOPIFY) {
		const all = allItems(items),
			filter = {
				where: { or: [], deletedAt: null },
				order: 'createdAt DESC'
			},
			{ where } = filter,
			variantIds = [], skus = [], gtins = []

		for (const { variantId, sku, gtin } of all) {
			if (variantId) variantIds.push(String(variantId))
			if (sku) skus.push(sku)
			if (gtin) gtins.push(gtin)
		}

		if (variantIds.length) {
			where.or.push(
				{ [`external.${provider}.variantId`]: { inq: variantIds } },
				{ id: { inq: variantIds } }
			)
		}
		if (skus.length) where.or.push({ sku: { inq: skus } })
		if (gtins.length) where.or.push({ 'gtins.value': { inq: gtins } })

		return where.or.length ? Variant.find(filter) : []
	}

	Variant.findByInventoryItemId = async function (provider, inventoryItemId) {
		const key = `external.${provider}.inventoryItemId`,
			filter = {
				where: { [key]: String(inventoryItemId) },
			}

		return Variant.findOne(filter)
	}
}
