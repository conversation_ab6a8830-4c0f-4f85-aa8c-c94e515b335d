/**
 *  @module Model:ProductApi
 */
const { Products } = require('@crm/types')

const { PERKD } = Products.Channel,
	FIELDS = [ 'id', 'kind', 'title', 'gtin', 'sku', 'mpn', 'weight', 'prices', 'taxable', 'taxCode',
		'variations', 'options', 'attributes', 'unitCostMeasure', 'unitPriceMeasure',
		'minOrderQuantity', 'globalize', 'inventory', 'productId', 'resourceId', 'placeId', 'imageIds' ],
	PRODUCT_FIELDS = [ 'brand', 'title', 'description', 'priceRange', 'tags', 'availability' ],
	MAX_RESULTS = 1000

module.exports = function(Variant) {

	/**
	 * Get Variants for custom storefronts (for applets)
	 * @param {Object} where - filter
	 * @param {String} channel - default to 'perkd'
	 * @param {String} [soldAt] - restrict to variants available at this placeId
	 */
	Variant.findForApp = async function(where = {}, channel, soldAt) {
		// inject constraints
		where.visible = true
		where.deletedAt = null
		where.channels = channel
		if (soldAt) {
			where.or = [
				{ 'preparation.placeId': soldAt },
				{ placeId: soldAt }
			]
		}

		const { Product, ProductImage } = Variant.app.models,
			filter = {
				where,
				fields: FIELDS,
				order: 'title ASC',
				limit: MAX_RESULTS
			},
			list = await Variant.find(filter),
			variants = list.map(v => {
				const { id: variantId, title: subtitle, inventory = {}, productId, resourceId, imageIds, ...rest } = v.toJSON()
				return { variantId, subtitle, productId, inventory, imageIds, ...rest }
			}),
			productIds = new Set(),
			imageIds = new Set()

		for (const variant of variants) {
			const { imageIds: ids = [], productId } = variant

			productIds.add(String(productId))

			for (const id of ids) {
				imageIds.add(id)
			}
		}

		const productFilter = {
				where: {
					id: { inq: Array.from(productIds) }
				}
			},
			imgIds = Array.from(imageIds),
			[ productList, urlsWithIds ] = await Promise.all([
				Product.find(productFilter, { fields: PRODUCT_FIELDS }),
				imgIds.length ? ProductImage.getUrlsWithIds(imgIds) : [],
			]),
			products = {},
			result = []

		for (const p of productList) {
			products[String(p.id)] = p.toJSON()
		}

		for (const variant of variants) {
			const { imageIds: ids, ...rest } = variant,
				{ productId } = variant,
				imageUrls = ids.map(id => urlsWithIds[id]).filter(url => !!url),
				product = products[String(productId)] || {}

			result.push({ ...rest, ...product, imageUrls })
		}

		return result
	}

	// -----  Remote Methods  -----

	Variant.remoteMethod('findForApp', {
		description: 'Find Variants (Product API)',
		http: { path: '/app/products', verb: 'get' },
		accepts: [
			{ arg: 'where', type: 'object', http: { source: 'query' }, required: true },
			{ arg: 'channel', type: 'string', http: { source: 'query' }, default: PERKD },
			{ arg: 'soldAt', type: 'string', http: { source: 'query' }, description: 'placeId of store' },
		],
		returns: { type: 'array', root: true },
	})
}
