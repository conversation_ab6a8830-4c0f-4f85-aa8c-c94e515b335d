/**
 *  @module Model:BagApi
 */

module.exports = function (Variant) {

	/**
	 * Construct Bag from Receipt (for app)
	 * @param {Object[]} receipt
	 * 			{String} id - item
	 * 			{String} kind
	 * 			{Number} quantity
	 * 			{Number} units
	 * 			{Number} discountAmount
	 * 			{Number} price
	 * 			{Number} unitPrice
	 * 			{Object} product - { id, title, brand tags, bundleList }
	 * 			{Object} variant - { id, productId, kind, title, gtin, sku, prices, taxable, imageIds }
	 * 			{Object[]} variantOptions
	 * 				{String} key
	 * 				{String} title
	 * 				{String} type
	 * 				{Object[]} values - { id, title, price, quantity }
	 * 				{Boolean} required
	 * 				{Number} min
	 * 				{Number} max
	 * 			{String[]} bundled - (receipt) item id of products in bundle
	 * 			{String[]} images - urls of images
	 * 			{Object[]} properties
	 * @param {String[]} [fulfillments]
	 * @returns {Object} bag - {
	 * 				kind, productId, variantId, sku, title, quantity, unitPrice,
	 * 				bundles, options, fulfillments, tags, images
	 * 			}
	 */
	Variant.bagFromReceipt = async function(receipt = [], fulfillments) {
		const { Product } = Variant.app.models,
			bag = []

		for (const { kind, product, variant, unitPrice, quantity, variantOptions, bundled, bundleId, images } of receipt) {
			if (bundleId) continue

			const { tags: t } = product,
				{ category: tags } = t,
				{ id: variantId, productId, sku, title } = variant,
				[ productInst, variantInst ] = await Promise.all([
					Product.findById(productId).catch(() => undefined),
					Variant.findById(variantId).catch(() => undefined)
				]),
				{ bundleList } = productInst?.toJSON() ?? {},
				{ options: optionsList } = variantInst?.toJSON() ?? {}

			// Calculate total price of bundled items
			const bundledItemsPrice = bundled?.reduce((total, id) => {
				const bundledItem = receipt.find(item => item.id === id)
				return total + (bundledItem?.price || 0)
			}, 0) || 0

			// Subtract bundled items price from main item price
			const adjustedUnitPrice = unitPrice - bundledItemsPrice
			const adjustedPrice = adjustedUnitPrice * quantity

			const bundles = getBundles(bundleList, bundled, receipt),
				options = getOptions(optionsList, variantOptions)

			bag.push({
				kind,
				productId,
				variantId,
				sku,
				title,
				quantity,
				price: adjustedPrice,
				unitPrice: adjustedUnitPrice,
				fulfillments,
				bundles,
				options,
				images,
				tags
			})
		}

		return bag
	}

	// ---  Private Functions  ---

	/**
	 * Update bundle list of product with values from receipt
	 * For each bundled id:
	 * 	1. Get sku of corresponding variant
	 * 	2. Get bundle item
	 * 	3. Update value of bundle item to value in receipt
	 *
	 * @param {Object[]} bundleList of product
	 * @param {String[]} bundled of receipt
	 * @param {Object[]} receipt
	 * @returns {Object[]} bundleList - mutated
	 */
	function getBundles(bundleList = [], bundled = [], receipt = []) {
		for (const id of bundled) {
			const bundledItem = receipt.find(item => item.id === id) ?? {},
				{ variant, quantity, variantOptions } = bundledItem,
				{ sku } = variant ?? {},
				value = findBundleItemValue(bundleList, sku),
				{ options } = value ?? {}

			if (!variant || !value) continue

			// Calculate per-unit quantity by dividing bundle item quantity by bundle quantity
			const bundleQuantity = receipt.find(item => item.bundled?.includes(id))?.quantity || 1,
				perUnitQuantity = quantity / bundleQuantity

			value.selected = true
			value.quantity = perUnitQuantity
			value.options = getOptions(options, variantOptions)
		}

		return bundleList
	}

	/**
	 * Find first bundle item value that is not set/selected
	 * @param {Object[]} bundleList
	 * @param {String} sku
	 * @returns {Object|void} value
	 */
	function findBundleItemValue(bundleList = [], sku) {
		for (const { values } of bundleList) {
			for (const value of values) {
				if (value.sku === sku && !value.selected) return value
			}
		}
	}

	/**
	 * Update options list of variant with values from receipt
	 * For each option:
	 * 	a. Get option values
	 * 	b. Update value of option to value in receipt
	 *
	 * @param {Object[]} optionsList of variant
	 * @param {Object[]} variantOptions of receipt
	 * @returns {Object[]} optionsList - mutated
	 */
	function getOptions(optionsList = [], variantOptions = []) {

		// deselect default values
		for (const { values } of optionsList) {
			for (const value of values) {
				value.selected = false
			}
		}

		for (const { key, values } of variantOptions) {
			const { values: optionValues = [] } = optionsList.find(opt => opt.key === key) ?? {}

			for (const { id, quantity } of values) {
				const value = optionValues.find(val => val.id === id)

				if (!value) continue

				value.selected = true
				value.quantity = quantity
			}
		}

		return optionsList
	}

	// -----  Remote Methods  -----

	Variant.remoteMethod('bagFromReceipt', {
		description: 'Build Bag from Receipt (Bag API)',
		http: { path: '/app/bag/from', verb: 'post' },
		accepts: [
			{ arg: 'receipt', type: 'array', required: true },
			{ arg: 'fulfillments', type: [ 'string' ] },
		],
		returns: { type: 'object', root: true, description: 'Bag' },
	})
}
