/**
 * @module Mixin: LookupApi
 */

module.exports = function(Variant) {

	/**
	 * Lookup Item (simplified variant for ordering) by GTIN, concurrently search from 3rd party sources (Amazon, Google)
	 * @param	{String} gtin - barcode
	 * @return	{Promise<Object>} {}
	 */
	Variant.findItemByGtin = async function(gtin) {
		const filter = {
				where: { 'gtin.value': gtin }
			},
			variant = await Variant.findOne(filter)

		if (!variant) return null

		const { id: variantId, title, prices = [], tags = [], options = [], taxes = [], globalize } = variant,
			[ base ] = prices,
			unitPrice = (base && base.value) || 0,
			images = []

		return { variantId, title, unitPrice, images, tags, options, taxes, globalize }
	}

	// -----  Remote Methods  -----

	Variant.remoteMethod('findItemByGtin', {
		description: 'Get Item by GTIN (app API)',
		http: { path: '/app/item/gtin/:gtin', verb: 'get' },
		accepts: [
			{ arg: 'gtin', type: 'string', http: { source: 'path' }, required: true },
		],
		returns: { type: 'object', root: true },
	})
}
