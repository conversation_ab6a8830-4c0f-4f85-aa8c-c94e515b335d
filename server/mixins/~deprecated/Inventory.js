/**
 * @module Mixin: Inventory
 */

const PERKD = 'perkd'

module.exports = function(Model, options) {
	// -----  Instance Methods  -----

	/**
	 * Deduct inventory in Cache
	 * @param	{Number} quantity
	 * @return	{Promise<Number>} balance
	 */
	Model.prototype.inventoryDeduct = function(quantity) {
		const self = this,
			{ Inventory } = Model.app.models,
			{ id } = self

		return Inventory.deductQuantity(id, quantity)
	}

	Model.prototype.updateInventory = function(quantity, management = true, policy = false, lowQuantityWarningThreshold = 10) {
		const self = this,
			{ Inventory } = Model.app.models,
			inventory = { quantity, management, policy, lowQuantityWarningThreshold }

		return self.updateAttributes({ inventory }).tap(() =>
			management === PERKD ? Inventory.cacheCreate(self.id, self.inventory.toJSON()) : Promise.resolve())
	}

	// -----  Remote & Operation Hooks  -----

	// Model.observe('loaded', async ({ isNewInstance, data }) => {
	// 	const { app } = Model,
	// 		{ Inventory } = app.models,
	// 		{ id, inventory = {} } = data,
	// 		{ management, quantity } = inventory;

	// 	if (management && quantity) {
	// 		if (!isNewInstance) {
	// 			return Inventory.cacheGet(id.toString()).then(inst =>
	// 				(inst && inst.quantity) ? inventory.quantity = inst.quantity : null);
	// 		}
	// 	}
	// });

	// TODO:
	Model.observe('after save', async ({ isNewInstance, instance }) => {
		const { app } = Model,
			{ Inventory } = app.models,
			{ id, inventory = {} } = instance,
			{ management } = inventory

		if (management === PERKD) {
			if (isNewInstance) return Inventory.cacheCreate(id, inventory.toJSON())

			// console.log('write-through to cache if quantity updated');
			// return inventory.add('quantity', 5);
		}
		return Promise.resolve()
	})

	// -----  Remote Methods  -----

	Model.remoteMethod('prototype.updateInventory', {
		description: 'Update inventory and cache',
		http: { path: '/inventory', verb: 'post' },
		accepts: [
			{ arg: 'quantity', type: 'number', required: true },
			{ arg: 'management', type: 'boolean', default: true },
			{ arg: 'policy', type: 'boolean', default: false },
			{ arg: 'lowQuantityWarningThreshold', type: 'number', default: 10 },
		],
		returns: { type: 'object', root: true },
	})
}
