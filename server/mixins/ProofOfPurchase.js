/**
 * @module Mixin: ProofOfPurchase (oncetoken)
 */
const { Wallet } = require('@crm/types'),
	{ OnceTokens } = require('@perkd/once-tokens')

// Environment (once-tokens)
// const {
// 	ONCETOKEN_HOST,
// 	ONCETOKEN_PORT = 6379,
// 	ONCETOKEN_USERNAME,
// 	ONCETOKEN_PASSWORD
// } = process.env

const { ONCE_TOKEN } = Wallet.Actions.NameSpace,
	onceToken = new OnceTokens(ONCE_TOKEN)

module.exports = function(Variant) {

	/**
	 * Get All Actions (id) in use
	 * @return	{Promise<string[]>} actionIds
	 */
	Variant.popActionIds = async function() {
		const all = await onceToken.allIds()

		return all.map(id => id.split(':').shift())
	}

	// -----  Instance Methods  -----

	/**
	 * Generate codes for variant
	 * @param	{String} actionId
	 * @param	{Number} quantity
	 * @param	{Date} expireAt
	 * @return	{Promise<string[]>} tokens
	 */
	Variant.prototype.popGenerate = async function(actionId, quantity, expireAt) {
		const { value: gtin } = this.gtin || {},
			id = `${actionId}:${gtin}`

		if (!gtin) throw new Error('gtin required')

		const tokens = await onceToken.generate(quantity, undefined, id)

		await onceToken.setExpire(expireAt, id)

		// TODO validate actionId ??

		return tokens
	}

	/**
	 * Get available (not used) codes for variant
	 * @param	{String} [actionId]
	 * @return	{Promise<string[]>} tokens
	 */
	Variant.prototype.popAvailable = async function(actionId) {
		const { value: gtin } = this.gtin || {},
			one = `${actionId}:${gtin}`,
			all = actionId ? [ one ] : await onceToken.allIds(),
			ids = all.filter(id => id.includes(`:${gtin}`)),
			tokens = []

		for (const id of ids) {
			const list = await onceToken.get(id)
			tokens.push(...list)
		}
		return tokens
	}

	/**
	 * Get Actions (id) associated with variant
	 * @return	{Promise<string[]>} actionIds
	 */
	Variant.prototype.popActionIds = async function() {
		const { value: gtin } = this.gtin || {},
			pattern = `:${gtin}`,
			all = await onceToken.allIds(),
			ids = all.filter(id => id.includes(pattern))

		return ids.map(id => id.replace(pattern, ''))
	}

	// -----  Remote Methods  -----

	Variant.remoteMethod('popActionIds', {
		description: 'All Actions (id)',
		http: { path: '/proof/purchase/actions', verb: 'get' },
		accepts: [
		],
		returns: { type: 'object', root: true },
	})

	Variant.remoteMethod('prototype.popGenerate', {
		description: 'Generate Proof-of-Purchase codes',
		http: { path: '/proof/purchase', verb: 'post' },
		accepts: [
			{ arg: 'actionId', type: 'string', required: true },
			{ arg: 'quantity', type: 'number', required: true },
			{ arg: 'expiresAt', type: 'date', required: true }
		],
		returns: { type: 'object', root: true },
	})

	Variant.remoteMethod('prototype.popAvailable', {
		description: 'Available (not used) Proof-of-Purchase codes',
		http: { path: '/proof/purchase', verb: 'get' },
		accepts: [
			{ arg: 'actionId', type: 'string' },
		],
		returns: { type: 'object', root: true },
	})

	Variant.remoteMethod('prototype.popActionIds', {
		description: 'Actions (id) associated with variant',
		http: { path: '/proof/purchase/actions', verb: 'get' },
		accepts: [
		],
		returns: { type: 'object', root: true },
	})
}
