/**
 * @module Mixin: FindProduct
 */

module.exports = function(Product) {

	/**
	 * Find by Ids with related variants
	 * @param	{String[]} ids
	 * @return	{Promise<Product[]>}
	 */
	Product.findWithVariantsByIds = function(ids = []) {
		const filter = {
			where: {
				id: { inq: ids }
			},
			include: 'variants'
		}

		return Product.find(filter)
	}

	/**
	 * Get Resources of products (ids)
	 * @param	{String[]} ids
	 * @return	{Promise<Resource[]>}
	 */
	Product.findResourcesByIds = async function(ids = []) {
		const { Resource } = Product.app.models,
			filter = {
				where: {
					productId: { inq: ids }
				}
			}

		return Resource.find(filter)
	}
}
