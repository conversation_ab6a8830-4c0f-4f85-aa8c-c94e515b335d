/**
 * @module Mixin: Bag
 */

const BASE = 'base',	// original unit price
	VARIANT_ATTRIBUTES = [ 'id', 'title', 'prices', 'taxes', 'tags', 'options', 'imageIds' ]

module.exports = function(Variant) {

	/**
	 * Add list of items to Bag of Card, will mix in variant properties where missing
	 * @param	{String} cardId
	 * @param	{Object[]} list
	 *				{String} variantId
	 *				{String} title
	 *				{Number} quantity
	 *				{Number} unitPrice
	 *				{String[]} images - list of image url
	 *				{String[]} tags
	 *				{Object[]} taxes: { title, rate, price }
	 *				{Object[]} options
	 *					{String} name
	 *					{Boolean} required - true => must select
	 *					{Number} multiple
	 *					{Object[]} values
	 *						{String} variantId
	 *						{String} name - eg. L, Medium, Blue
	 *						{Number} price - price of option, overrides Variant price
	 * @param	{String} [bag] name (use current bag if omitted)
	 * @param	{Boolean} [notify] true => send notification
	 * @return	{Object}
	 */
	Variant.addItemsBag = async function(cardId, list, bag, notify = false) {
		const { Perkd } = appModule('perkd'),
			variantIds = list.map(i => i.variantId),
			filter = {
				where: { id: { inq: variantIds } },
				include: {
					relation: 'images',
					scope: { fields: [ 'original' ] }
				},
				fields: VARIANT_ATTRIBUTES,
			},
			variants = await Variant.find(filter),
			items = variants.map(variant => {
				const item = list.find(i => i.variantId === variant.id)
				return fillProperties(item, variant)
			})

		return Perkd.bags.addItems(cardId, bag, items, notify)
	}

	Variant.updateItemsBag = function(cardId, list, bag, notify = false) {
		// FIXME
		return Promise.reject(new Error('not implemented'))
	}

	Variant.removeItemsBag = function(cardId, list, bag, notify = false) {
		// FIXME
		return Promise.reject(new Error('not implemented'))
	}

	// -----  Instance Methods  -----

	/**
	 * Add Variant into Bag of Card
	 * @param	{String} cardId
	 * @param	{Number} quantity
	 * @param	{String} [bag] - name
	 * @param	{Boolean} [notify]
	 * @return	{Object}
	 */
	Variant.prototype.addItemsBag = async function(cardId, quantity = 1, bag, notify = false) {
		await this.images()

		const { id, title, prices, tags, taxes, options } = this,
			{ Perkd } = appModule('perkd'),
			variantId = id.toString(),
			unitPrice = prices.find(p => p.name === BASE)?.price.value || 0,
			images = this.images.toJSON(),
			item = { variantId, quantity, title, unitPrice, images, tags, taxes, options }

		return Perkd.bags.addItems(cardId, bag, [ item ], notify)
	}

	Variant.prototype.updateItemsBag = function(cardId, quantity, bag, notify = false) {
		// FIXME
		return Promise.reject(new Error('not implemented'))
	}

	Variant.prototype.removeItemsBag = function(cardId, bag, notify = false) {
		// FIXME
		return Promise.reject(new Error('not implemented'))
	}

	// -----  Private functions  -----

	function fillProperties(item, variant) {
		const { title, prices, taxes, tags, options } = variant,
			unitPrice = prices.find(p => p.name === BASE)?.price.value || 0,
			imgageList = variant.images.toJSON(),
			images = imgageList.map(img => img.original.url)

		return Object.assign({}, { quantity: 1, title, unitPrice, images, tags, taxes, options }, item)
	}

	// -----  Remote Methods  -----

	Variant.remoteMethod('addItemsBag', {
		description: 'Add Variants to Bag of Card',
		http: { path: '/bag', verb: 'post' },
		accepts: [
			{ arg: 'cardId', type: 'string', required: true },
			{ arg: 'list', type: 'array', required: true, description: 'List of items with variantId' },
			{ arg: 'bag', type: 'string', description: 'Name of bag' },
			{ arg: 'notify', type: 'boolean', default: false },
		],
		returns: { type: 'object', root: true },
	})

	Variant.remoteMethod('updateItemsBag', {
		description: 'Update Variants in Bag of Card',
		http: { path: '/bag', verb: 'patch' },
		accepts: [
			{ arg: 'cardId', type: 'string', required: true },
			{ arg: 'list', type: 'array', required: true, description: 'List of items with variantId' },
			{ arg: 'bag', type: 'string', description: 'Name of bag' },
			{ arg: 'notify', type: 'boolean', default: false },
		],
		returns: { type: 'object', root: true },
	})

	Variant.remoteMethod('removeItemsBag', {
		description: 'Remove Variants from Bag of Card',
		http: { path: '/bag', verb: 'delete' },
		accepts: [
			{ arg: 'cardId', type: 'string', required: true },
			{ arg: 'list', type: 'array', required: true, description: 'List of items with variantId' },
			{ arg: 'bag', type: 'string', description: 'Name of bag' },
			{ arg: 'notify', type: 'boolean', default: false },
		],
		returns: { type: 'object', root: true },
	})

	Variant.remoteMethod('prototype.addItemsBag', {
		description: 'Add variant to Bag of Card ',
		http: { path: '/bag', verb: 'post' },
		accepts: [
			{ arg: 'cardId', type: 'string', required: true },
			{ arg: 'quantity', type: 'number', required: true },
			{ arg: 'bag', type: 'string', description: 'Name of bag' },
			{ arg: 'notify', type: 'boolean', default: false },
		],
		returns: { type: 'object', root: true },
	})

	Variant.remoteMethod('prototype.updateItemsBag', {
		description: 'Update variant in Bag of Card',
		http: { path: '/bag', verb: 'patch' },
		accepts: [
			{ arg: 'cardId', type: 'string', required: true },
			{ arg: 'quantity', type: 'number', required: true },
			{ arg: 'bag', type: 'string', description: 'Name of bag' },
			{ arg: 'notify', type: 'boolean', default: false },
		],
		returns: { type: 'object', root: true },
	})

	Variant.remoteMethod('prototype.removeItemsBag', {
		description: 'Remove variant from Bag of Card',
		http: { path: '/bag', verb: 'delete' },
		accepts: [
			{ arg: 'cardId', type: 'string', required: true },
			{ arg: 'bag', type: 'string', description: 'Name of bag' },
			{ arg: 'notify', type: 'boolean', default: false },
		],
		returns: { type: 'object', root: true },
	})
}
