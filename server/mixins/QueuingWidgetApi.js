/**
 *  @module Mixin:QueuingWidgetApi - for table queuing via Widgets
 */
const { Queuings, Bookings, Settings, Products, Wallet } = require('@crm/types')
const { MEMBERSHIP } = require('@perkd/errors/dist/service')
const { dayjs } = require('@perkd/utils')

const { LOCALE, BOOKING } = Settings.Name
const { NONE, ASSIGNED, QUEUED, REASSIGNED, EARLY } = Queuings.Status
const { VIP, ASAP } = Queuings.Tier
const { TABLE } = Products.ResourceKind
const { SUCCESS, RESERVED, OPEN, PENDING, CANCELLED } = Bookings.Status
const { BOOK_TABLE } = Wallet.Widgets.Key
const { WAITING, NOTIFIED } = Queuings.Status
const { MEMBERSHIP_NOT_FOUND } = MEMBERSHIP
const UPDATE_INTERVAL = 2000

module.exports = function(Product) {

	/**
	 * Queue-only for tables, do not handle reservations
	 * 	1. If customer is already in queue, return their current queue status with existing: true
	 * @param	{String} cardId
	 * @param	{String} placeId
	 * @param	{Number} partySize - number of people
	 * @param	{Boolean} [allowCombined] - accept combined tables
	 * @param	{Boolean} [adjacentOnly] - adjacent tables only
	 * @returns {Promise<Object>} { status, queueId?, scheduledAt?, endTime?, partySize?, placeId?, existing? }
	 */
	Product.queueOnlyTablesWidget = async function(cardId, placeId, partySize, allowCombined, adjacentOnly) {
		const { app } = Product,
			{ Membership } = app.models,
			membership = await Membership.findActiveByCardId(cardId)

		if (!membership) throw MEMBERSHIP_NOT_FOUND

		const { id, personId } = membership,
			membershipId = String(id),
			customer = { tier: VIP, personId, membershipId, cardId },
			preferences = { allowCombined, adjacentOnly }

		// Check if customer is already in queue
		const queued = await findActiveQueueEntry(app, cardId, placeId, TABLE)
		if (queued) {
			console.log(`[queueOnlyTablesWidget] Customer with cardId: ${cardId} is already in queue for placeId: ${placeId}`)
			return {
				status: queued.status,
				queueId: String(queued.id),
				scheduledAt: queued.scheduledAt,
				endTime: queued.endTime,
				partySize: queued.capacity,
				placeId,
				existing: true
			}
		}

		return Product.requestTableQueue(customer, placeId, partySize, preferences)
	}

	/**
	 * Queue Check-in for tables, may have reservation
	 * 	1. If reservationId provided, retrieve bookings, otherwise try to find bookings for the member
	 * 	2. If bookings found, check if tables are immediate available (return bookings), if not, queue for tables
	 * 	3. If no bookings found, respond 'No bookings found' (client calls queueOnlyTableWidget() to queue for tables)
	 * 	4. If customer is already in queue, return their current queue status with existing: true
	 * @param	{String} cardId
	 * @param	{String} placeId
	 * @param	{String} [reservationId]
	 * @returns {Promise<Object>} { kind, status, resources?, queueId?, scheduledAt?, endTime?, placeId?, bookings?, existing? }
	 */
	Product.queueTablesWidget = async function (cardId, placeId, reservationId) {
		const { app } = Product
		const { Membership, Booking, Resource } = app.models
		const { timeZone } = app.getSettings(LOCALE)
		const { gracePeriod, minDuration, earlyCheckinTime } = app.getSettings(BOOKING)[TABLE]
		const now = dayjs().tz(timeZone)

		// 1. Get Membership
		const membership = await Membership.findActiveByCardId(cardId)
		if (!membership) {
			throw MEMBERSHIP_NOT_FOUND
		}

		const { personId, id: membershipId } = membership
		// Base customer object for members
		const customer = { tier: VIP, personId, membershipId: String(membershipId), cardId }

		// Check if customer is already in queue
		const queued = await findActiveQueueEntry(app, cardId, placeId, TABLE)
		if (queued) {
			console.log(`[queueTablesWidget] Customer with cardId: ${cardId} is already in queue for placeId: ${placeId}`)
			return {
				kind: queued.kind,
				status: queued.status,
				queueId: String(queued.id),
				scheduledAt: queued.scheduledAt,
				endTime: queued.endTime,
				placeId,
				existing: true
			}
		}

		// 2. Find Bookings
		// Update filter to handle all scenarios:
		// - Include bookings where user is arriving late but within grace period
		// - Include bookings where user is arriving early within earlyCheckinTime
		// - Exclude bookings where user is arriving too early (before startTime - minDuration)
		const filter = {
			where: {
				status: OPEN,		// confirmed but not yet arrived
				placeId,
				personId,
				// Find bookings starting within a wider window
				and: [
					// Lower bound: Include bookings that started up to gracePeriod minutes ago
					{
						startTime: { gte: now.subtract(gracePeriod, 'minute').toDate() }
					},
					// Upper bound: Include bookings starting up to minDuration minutes from now
					// This excludes "too early" bookings
					{
						startTime: { lte: now.add(minDuration, 'minute').toDate() }
					}
				]
			},
			order: 'startTime ASC' // Prioritize the earliest booking if multiple match
		}

		// If specific reservationId provided, look for that booking regardless of time window
		if (reservationId) {
			filter.where.reservationId = reservationId
		}

		const list = await Booking.find(filter)

		// 3. Handle No Booking Found
		// This covers both users with no bookings and users who are too early
		if (!list.length) {
			console.log(`[queueTablesWidget] No suitable booking found for cardId: ${cardId}, placeId: ${placeId}, reservationId: ${reservationId}`)
			return { kind: TABLE, status: NONE }
		}

		// 4. Handle first Reservation (bookings) found
		const [ firstBooking ] = list,
			{ reservationId: bookedReservationId, startTime, endTime, partySize, preferences, note } = firstBooking,
			bookings = list.filter(b => b.reservationId === bookedReservationId),
			bookingStartTime = dayjs(startTime).tz(timeZone),
			gracePeriodEndTime = bookingStartTime.add(gracePeriod, 'minute'),
			earlyCheckinStartTime = bookingStartTime.subtract(earlyCheckinTime, 'minute'),
			isLate = now.isAfter(gracePeriodEndTime),	// past grace period
			isEarlyCheckin = now.isBefore(bookingStartTime)	// before booking start time

		// 5. Auto-Queue if Late
		if (isLate) {
			const queued = await Product.requestTableQueue(customer, placeId, partySize, preferences)
			return { ...queued, bookings }
		}

		// 6. Handle Early Check-in
		if (isEarlyCheckin) {
			console.log(`[queueTablesWidget] Customer with cardId: ${cardId} is checking in early for reservation: ${bookedReservationId}`)
			// Calculate minDurationStartTime (earliest reasonable check-in)
			const minDurationStartTime = bookingStartTime.subtract(minDuration, 'minute')

			// Check if they're checking in between minDuration and earlyCheckinTime thresholds
			const isTooEarlyButInRange = now.isAfter(minDurationStartTime) && now.isBefore(earlyCheckinStartTime)

			if (isTooEarlyButInRange) {
				// They're too early (before earlyCheckinTime) but after minDuration
				// Return EARLY status with their reservation details
				console.log(`[queueTablesWidget] Customer with cardId: ${cardId} is checking in too early (before earlyCheckinTime) for reservation: ${bookedReservationId}`)

				return {
					kind: TABLE,
					status: EARLY,
					toCheckinAt: earlyCheckinStartTime.toDate(),
					earlyCheckinTime,
					reservation: {
						reservationId: bookedReservationId,
						startTime,
						endTime,
						partySize
					}
				}
			}
		}

		// --- Process On-Time Arrivals with Smart Release and Reassign ---
		try {
			customer.tier = ASAP	// High priority for booked customers
			const fromDate = now.toDate() // Assign starting now

			// Check if the booking has already started (customer is checking in after start time)
			const bookingStarted = now.isAfter(bookingStartTime)

			// Calculate adjusted duration based on whether booking has started
			let adjustedDuration = minDuration

			if (bookingStarted) {
				// Calculate how late the customer is (in minutes)
				const lateMinutes = now.diff(bookingStartTime, 'minute')

				// Calculate time until original booking end, apply safety factor (1 minute) to prevent potential overlap
				const timeUntilBookingEnd = dayjs(endTime).diff(now, 'minute') - 1

				adjustedDuration = timeUntilBookingEnd

				console.log(
					`[queueTablesWidget] Customer checked in ${lateMinutes} minutes after booking start. `
					+ `Original booking ends in ${timeUntilBookingEnd} minutes. `
					+ `Using adjusted duration: ${adjustedDuration} minutes (includes 1-minute safety buffer).`
				)
			}

			const toDate = now.add(adjustedDuration, 'minutes').toDate()

			// Step 1: Check if original tables are available (excluding customer's own bookings)
			const originalTableIds = bookings.map(b => b.resourceId)
			const originalTablesAvailable = await areTablesAvailableNow(app, originalTableIds, fromDate, adjustedDuration, bookedReservationId)

			// Step 2: If original tables are available, seat at original tables
			if (originalTablesAvailable) {
				console.log(`[queueTablesWidget] Original tables available for reservation ${bookedReservationId}, seating customer at original tables.`)
				await updateBookingsToCheckedIn(app, bookings, cardId, timeZone)

				// Map bookings to resources format for consistent response
				const resources = await Promise.all(bookings.map(async b => {
					const { resourceId, id: bookingId } = b
					const { name, position, capacity } = await Resource.findById(resourceId)
					return {
						name,
						position,
						capacity,
						startTime: fromDate,
						endTime: toDate,
						resourceId,
						bookingId,
						reservationId: bookedReservationId
					}
				}))

				return { kind: TABLE, status: ASSIGNED, resources, bookings }
			}

			// Step 3: If original tables not available, release them and find alternatives
			console.log(`[queueTablesWidget] Original tables NOT available for reservation ${bookedReservationId}, releasing and finding alternatives.`)
			await cancelOriginalBookings(app, bookings, cardId)

			// Step 4: Check for alternative tables (include overstaying bookings check)
			const { available, occupiedIds } = await Resource.available( placeId, TABLE, partySize, fromDate, minDuration, preferences, true )

			if (!available.length) {
				console.log(`[queueTablesWidget] No alternative tables available for reservation ${bookedReservationId}, queuing customer.`)
				// Queue if no resources immediately available AT ALL
				const queued = await joinQueueWithReservationHistory(app, placeId, TABLE, partySize, preferences, customer, bookedReservationId)
				return { kind: TABLE, status: QUEUED, ...queued, originalBookings: bookings }
			}

			// Find specific candidates (table combinations) from available resources
			const candidates = Resource.candidates(available, partySize, preferences, true, occupiedIds) // true = check for immediate assignment

			if (!candidates.length) {
				console.log(`[queueTablesWidget] No suitable table combination available for reservation: ${bookedReservationId}, queuing customer.`)
				// Queue if specific combination/layout not immediately available
				const queued = await joinQueueWithReservationHistory(app, placeId, TABLE, partySize, preferences, customer, bookedReservationId)
				return { kind: TABLE, status: QUEUED, ...queued, originalBookings: bookings }
			}

			// Assign alternative tables - this will now return the full resource structure
			console.log(`[queueTablesWidget] Assigning alternative tables for reservation: ${bookedReservationId}. Candidates:`, candidates)
			const resources = await Resource.assign(candidates, fromDate, toDate, partySize, customer, preferences, { note })

			if (!resources || resources.length === 0) {
				// Should not happen if candidates were found, but handle as fallback
				console.warn(`[queueTablesWidget] Assignment failed unexpectedly for reservation: ${bookedReservationId} despite candidates found`)
				const queued = await joinQueueWithReservationHistory(app, placeId, TABLE, partySize, preferences, customer, bookedReservationId)
				return { kind: TABLE, status: QUEUED, ...queued, originalBookings: bookings }
			}

			// Step 5: Update bookings to checked-in status
			await updateBookingsToCheckedIn(app, resources, cardId, timeZone)

			return { kind: TABLE, status: REASSIGNED, resources, originalBookings: bookings }
		}
		catch (error) {
			console.error(`[queueTablesWidget] Error processing check-in for card: ${cardId}, reservation: ${bookedReservationId}:`, error)
			throw error
		}
	}

	/**
	 * Cancel queue for tables
	 * @param	{String} id - queue id
	 */
	Product.cancelQueueTablesWidget = async function(id) {
		return Product.cancelTableQueue(id)
	}

	// -----  Remote Methods  -----

	Product.remoteMethod('queueOnlyTablesWidget', {
		description: 'Queue-only for tables, do not handle reservations (Table Queuing Widget API)',
		http: { path: '/app/queuing/tables', verb: 'post' },
		accepts: [
			{ arg: 'cardId', type: 'string', required: true },
			{ arg: 'placeId', type: 'string', required: true },
			{ arg: 'partySize', type: 'number', required: true, description: 'number of people' },
			{ arg: 'allowCombined', type: 'boolean', default: true, description: 'accept combined tables' },
			{ arg: 'adjacentOnly', type: 'boolean', default: true, description: 'adjacent tables only' },
		],
		returns: {
			type: 'object',
			root: true,
			description: 'Queue status with existing flag if customer is already in queue'
		}
	})

	Product.remoteMethod('queueTablesWidget', {
		description: 'Queue Check-in for tables, may have reservation (Table Queuing Widget API)',
		http: { path: '/app/queuing/checkin', verb: 'post' },
		accepts: [
			{ arg: 'cardId', type: 'string', required: true },
			{ arg: 'placeId', type: 'string', required: true },
			{ arg: 'reservationId', type: 'string' },
		],
		returns: {
			type: 'object',
			root: true,
			description: 'Queue or booking status with existing flag if customer is already in queue'
		}
	})

	Product.remoteMethod('cancelQueueTablesWidget', {
		description: 'Cancel queue for tables (Table Queuing Widget API)',
		http: { path: '/app/queuing/tables/:id', verb: 'delete' },
		accepts: [
			{ arg: 'id', type: 'string', http: { source: 'path' }, required: true },
		],
		returns: { type: 'object', root: true }
	})
}

// -----  Private functions  -----

/**
 * Find active queue entry for a customer
 * @param {Object} app - Application object
 * @param {String} cardId - Card ID
 * @param {String} placeId - Place ID
 * @param {String} kind - Resource kind (e.g., TABLE)
 * @returns {Promise<Object|null>} Queue entry or null if not found
 */
async function findActiveQueueEntry(app, cardId, placeId, kind) {
	const { Queue } = app.models
	const { timeZone } = app.getSettings(LOCALE)
	const now = dayjs().tz(timeZone)

	// Find active queue entries for this customer
	// Only consider entries with WAITING or NOTIFIED status
	// and that haven't passed their endTime
	const filter = {
		where: {
			'digitalCard.id': cardId,
			placeId,
			kind,
			status: WAITING,
			endTime: { gt: now.toDate() } // Not considered abandoned
		},
		order: 'createdAt DESC' // Get the most recent entry
	}

	return Queue.findOne(filter)
}

/**
 * Check if specific tables are available right now, excluding the customer's own bookings
 * @param {Object} app - Application object
 * @param {String[]} tableIds - Array of table IDs
 * @param {Date} [fromDate] - Start time (defaults to now)
 * @param {Number} [duration] - Duration in minutes (defaults to 15)
 * @param {String} [reservationId] - Reservation ID to exclude from occupancy check
 * @returns {Promise<Boolean>} Whether all tables are physically available
 */
async function areTablesAvailableNow(app, tableIds, fromDate = new Date(), duration = 15, reservationId = null) {
	const { Booking } = app.models
	const toDate = new Date(fromDate.getTime() + (duration * 60000))

	// We need to check if these tables are physically available (not just reserved by this customer)
	// This requires checking if any OTHER bookings (not belonging to this customer) occupy these tables

	// Get all bookings including overstaying ones
	const allBookings = await Booking.occupied(tableIds, fromDate, toDate, { includeOverstay: true })

	// Filter out the customer's own bookings
	const otherBookings = reservationId
		? allBookings.filter(booking => booking.reservationId !== reservationId)
		: allBookings

	// If there are any bookings from other customers, the tables are not available
	if (otherBookings.length > 0) {
		console.log(`Tables not available: ${otherBookings.length} other bookings found`)
		return false
	}

	// Check if tables are physically available (not in maintenance, etc.)
	const { Resource } = app.models
	const resources = await Resource.find({
		where: {
			id: { inq: tableIds }
		}
	})

	// All tables must be found and available
	const availableTableIds = resources.map(r => String(r.id))
	const allTablesAvailable = tableIds.map(String).every(id => availableTableIds.includes(id))

	return allTablesAvailable
}

/**
 * Cancel original bookings to release tables
 * @param {Object} app - Application object
 * @param {Object[]} bookings - Original booking records
 * @param {String} cardId - Card ID
 * @returns {Promise<void>}
 */
async function cancelOriginalBookings(app, bookings, cardId) {
	const { Event, models } = app,
		{ Booking } = models,
		{ reservationId, createdAt } = bookings[0] || {},
		now = new Date()

	// Cancel all bookings for this reservation
	for (const booking of bookings) {
		await Booking.cancel(booking.id)
	}

	appEmit(Event.widget.data.delete, {
		key: BOOK_TABLE,
		cardId,
		reservations: [ { id: reservationId, status: CANCELLED, deletedAt: now } ],
		createdAt,
		modifiedAt: now
	})

	if (reservationId) {
		console.log(`[cancelOriginalBookings] Reservation ${reservationId} cancelled`)
	}
}

/**
 * Update bookings to checked-in status
 * @param {Object} app - Application object
 * @param {Object[]} bookings - Booking records to update
 * @param {String} cardId - Card ID
 * @param {String} timeZone - Timezone for calculations
 * @returns {Promise<void>}
 */
async function updateBookingsToCheckedIn(app, bookings, cardId, timeZone) {
	const { Event, models } = app,
		{ Booking } = models,
		now = dayjs().tz(timeZone),
		{ reservationId, placeId, source = '', personId = '', partySize = 1, startTime, endTime, createdAt } = bookings[0] || {}

	if (!reservationId || !placeId) return

	await Booking.updateAll(
		{ reservationId, placeId, status: OPEN },
		{ status: SUCCESS, arrivedAt: now.toDate() }
	)

	// Skip widget data update for bookings with source 'queuing'
	if (source !== 'queuing') {
		// Delay to ensure widget.data.update for `open` status is processed first (race condition)
		setTimeout(() => {
			appEmit(Event.widget.data.update, {
				key: BOOK_TABLE,
				cardId,
				reservations: [ { id: reservationId, status: SUCCESS, createdAt, modifiedAt: now.toDate() } ],
				createdAt,
				modifiedAt: now.toDate()
			})
		}, UPDATE_INTERVAL)
	}

	// Emit person.visit.arrive event when customer checks in
	if (personId) {
		const visitData = {
			id: personId,
			context: {
				spot: { placeId },
				booking: {
					reservationId,
					kind: TABLE,
					partySize,
					placeId,
					startTime,
					endTime
				}
			},
			occurredAt: now.toDate()
		}

		appEmit(Event.person.visit.arrive, visitData)
	}
}

/**
 * Join queue with reservation history
 * @param {Object} app - Application object
 * @param {String} placeId - Place ID
 * @param {String} kind - Resource kind
 * @param {Number} partySize - Party size
 * @param {Object} preferences - Seating preferences
 * @param {Object} customer - Customer information
 * @param {String} reservationId - Original reservation ID
 * @returns {Promise<Object>} Queue entry
 */
async function joinQueueWithReservationHistory(app, placeId, kind, partySize, preferences, customer, reservationId) {
	const { Queue } = app.models

	// Add reservation history to customer object
	const customerWithHistory = {
		...customer,
		hadReservation: true,
		originalReservationId: reservationId
	}

	// Join queue with high priority
	return Queue.join(placeId, kind, partySize, preferences, customerWithHistory)
}
