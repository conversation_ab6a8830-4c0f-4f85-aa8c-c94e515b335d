/**
 *  @module Model:EventWidgetApi - event management (for staff)
 */

const { Products, Settings } = require('@crm/types'),
	{ App } = require('@crm/loopback'),
	{ getLanguage } = App,
	{ dayjs } = require('@perkd/utils')

const { EVENT } = Products.ResourceKind,
	{ LOCALE } = Settings.Name

module.exports = function(Product) {

	/**
	 * Get Event resources				(App/applet API)
	 * @param	{Date} [until]
	 * @param	{String} language
	 */
	Product.getEventResources = async function(until) {
		const { app } = Product,
			{ timeZone } = app.getSettings(LOCALE),
			{ Resource } = app.models

		// Use dayjs with timezone for proper date handling
		until = until || dayjs().tz(timeZone).toDate()

		const language = getLanguage(),
			filter = {
				where: {
					kind: EVENT,
					endTime: { gt: until }
				},
				include: [
					{ relation: 'place' },
					{ relation: 'variants' },
					{ relation: 'product',
						scope: {
							where: { visible: true },
							fields: [ 'id', 'title', 'description', 'tags' ],
							include: {
								relation: 'images',
								scope: {
									where: { visible: true },
									order: 'position ASC'
								}
							}
						}
					},
				],
			},
			events = await Resource.find(filter),
			result = []

		for (const event of events) {
			const { id, name, startTime, endTime, capacity, productId, product, variants, place } = event.toJSON()

			if (!product) continue

			const { title = '', description = '' } = event.product().translations([ 'title', 'description' ], language),
				{ tags: t = {}, images: imgs } = product,
				images = imgs.map(i => i.original.url),
				{ category: tags = [] } = t,
				{ addressList = [] } = place,
				[ address ] = addressList,
				{ formatted, geo } = address,
				venue = {
					name: place.name,
					address: formatted,
					geo
				},
				[ first ] = variants,
				{ unitPriceMeasure, prices } = first ?? {},
				productIds = [ productId ]

			result.push({
				id, name, title, description, startTime, endTime, capacity, venue, unitPriceMeasure, prices,
				images, tags, productIds })
		}

		return result
	}

	// -----  Remote Methods  -----

	Product.remoteMethod('getEventResources', {
		description: 'Get Events (Event Widget API)',
		http: { path: '/staff/events', verb: 'post' },
		accepts: [
			{ arg: 'until', type: 'Date' }
		],
		returns: { type: 'object', root: true },
	})
}
