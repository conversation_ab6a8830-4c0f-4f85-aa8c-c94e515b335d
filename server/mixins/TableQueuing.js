/**
 * @module Mixin:TableQueuing
 * Provides table queuing functionality for restaurant/venue table management.
 * Handles immediate table assignments and queue management when tables are not immediately available.
 */
const { dayjs, isOpen } = require('@perkd/utils'),
	{ Products, Settings, Queuings, Bookings: BookingTypes, Wallet } = require('@crm/types')

const { TABLE } = Products.ResourceKind,
	{ BOOKING, LOCALE } = Settings.Name,
	{ ASSIGNED, CANCELLED, NONE } = Queuings.Status,
	{ SUCCESS } = BookingTypes.Status,
	{ QUEUEING } = Wallet.Widgets.Key

module.exports = function(Product) {

	/**
	 * Request a table or join the queue if no tables are immediately available
	 * @param	{Object} customer - { tier, personId, membershipId, cardId }
	 * @param	{String} placeId - place id
	 * @param	{Number} partySize - number of people
	 * @param	{Object} preferences
	 *			{Boolean} allowCombined - to allow combined resources
	 *			{Boolean} adjacentOnly - to require adjacent resources
	 *			{Boolean} immediate - request immediate assignment, don't queue otherwise
	 * @return	{Object} { kind, status, queueId, scheduledAt } | { kind, status, resources }
	 */
	Product.requestTableQueue = async function (customer, placeId, partySize, preferences = {}) {
		const _start = new Date(),
			{ app } = Product,
			{ Metric, models, Event } = app,
			{ Resource, Queue, Booking } = models,
			{ enabled, minDuration, maxPartySize } = app.getSettings(BOOKING)[TABLE],
			{ timeZone } = app.getSettings(LOCALE),
			now = dayjs().tz(timeZone),
			from = now.toDate(),
			to = now.add(minDuration, 'minutes').toDate(),
			{ QUEUING } = BookingTypes.Source,
			{ immediate, ...pref } = preferences,
			NO_IMMEDIATE = { kind: TABLE, status: NONE },
			options = { source: QUEUING },
			tags = { placeId, kind: TABLE }

		if (!enabled) throw 'NOT_ENABLED'

		if (partySize < 1 || partySize > maxPartySize) {
			const error = new Error('INVALID_PARTY_SIZE')
			error.statusCode = 400
			throw error
		}

		// Validate queuing hours - check if queue requests are allowed at current time
		const { Place } = models
		const place = await Place.findById(placeId)
		if (!place) {
			const error = new Error('PLACE_NOT_FOUND')
			error.statusCode = 404
			throw error
		}

		// Check queuing hours first, fall back to opening hours if queuing hours not defined
		const { queuing = {}, openingHours = {} } = place
		const { hours: queuingHours } = queuing
		const businessHours = queuingHours || openingHours

		// Validate current time against queuing/business hours
		const checkTime = now.toDate()
		const isQueuingOpen = isOpen(businessHours, checkTime, undefined, timeZone)

		if (!isQueuingOpen) {
			const error = new Error('OUTSIDE_QUEUING_HOURS')
			error.statusCode = 400
			throw error
		}

		// Default preferences
		pref.allowCombined ??= true
		pref.adjacentOnly ??= true

		try {
			// Include overstaying bookings check for tables
			const { available, occupiedIds: occupiedTableIds } = await Resource.available(
				placeId, TABLE, partySize, from, minDuration, pref.allowCombined, true
			)
			if (!available.length) {
				return immediate ? NO_IMMEDIATE : Queue.join(placeId, TABLE, partySize, pref, customer)
			}

			const candidates = Resource.candidates(available, partySize, pref, true, occupiedTableIds)
			if (!candidates.length) {
				return immediate ? NO_IMMEDIATE : Queue.join(placeId, TABLE, partySize, pref, customer)
			}

			const resources = await Resource.assign(candidates, from, to, partySize, customer, pref, options)
			if (!resources.length) {
				return immediate ? NO_IMMEDIATE : Queue.join(placeId, TABLE, partySize, pref, customer)
			}

			// Auto check-in the bookings since customer is present
			const bookingIds = resources.map(r => r.bookingId)
			await Booking.updateAll(
				{ id: { inq: bookingIds } },
				{ kind: TABLE, status: SUCCESS, arrivedAt: now.toDate() }
			)

			appMetric(Metric.queuing.wait, 0, { tags })
			appMetric(Metric.queuing.latency.request, _start, { tags })

			// Emit person.visit.arrive event when customer is assigned a table, skip for staff-assisted operations (no personId)
			if (customer.personId) {
				const { reservationId, startTime, endTime } = resources[0] || {}
				const visitData = {
					id: customer.personId,
					context: {
						spot: { placeId },
						booking: {
							reservationId,
							kind: TABLE,
							partySize,
							placeId,
							startTime,
							endTime
						}
					},
					occurredAt: now.toDate()
				}

				appEmit(Event.person.visit.arrive, visitData)
			}

			return { kind: TABLE, status: ASSIGNED, partySize, placeId, resources }
		}
		catch (error) {
			appMetric(Metric.queuing.error.request, 1, { tags, error })
			appMetric(Metric.queuing.latency.request, _start, { tags, error })

			const { statusCode = 500 } = error
			console.error(`[requestTableQueue] placeId: ${placeId} partySize: ${partySize}`, error)
			throw new Error('QUEUEING_FAILED', { statusCode })
		}
	}

	Product.cancelTableQueue = async function(id) {
		const { app } = Product,
			{ Metric, Event, models } = app,
			{ Queue } = models,
			instance = await Queue.findById(id)

		if (!instance) return false

		const { id: queueId, kind, digitalCard, capacity, placeId, createdAt } = instance,
			{ id: cardId } = digitalCard ?? {},
			tags = { placeId, kind },
			now = new Date()

		// Emit widget.data.update event before deleting the queue entry
		appEmit(Event.widget.data.update, {
			id: queueId,
			key: QUEUEING,
			cardId,
			partySize: capacity,
			status: CANCELLED,
			createdAt,
			modifiedAt: now,
			deletedAt: now
		})

		await Queue.deleteById(id)
		appMetric(Metric.queuing.total, -1, { tags })
		return true
	}
}
