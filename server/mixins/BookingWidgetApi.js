/**
 *  @module Model:BookingWidgetApi
 */
const { Context } = require('@perkd/multitenant-context'),
	{ Bookings, Products, Settings } = require('@crm/types'),
	{ Time, periodsMerge, periodsDifference, dayOfWeek, shortId, getStartEndTime, dayjs, HOUR_0 } = require('@perkd/utils'),
	{ PRODUCT: PRODUCT_ERR } = require('@perkd/errors/dist/service')

const { MINUTE, DAY } = Bookings.Unit,
	{ VENUE, EVENT, TABLE } = Products.ResourceKind,
	{ LOCALE } = Settings.Name,
	{ DEFAULT } = Settings,
	{ TIMEZONE } = DEFAULT,
	{ BOOKING_UNAVAILABLE } = PRODUCT_ERR

module.exports = function(Product) {

	/**
	 * Get all resources for type				(App/applet API)
	 * @param	{String} kind
	 * @returns	{Object[]} list of resources
	 */
	Product.resourcesForBookingWidget = async function(kind = VENUE) {
		const { app } = Product,
			{ Resource } = app.models,
			{ language } = Context,
			filter = {
				where: { kind },
				include: [
					{ relation: 'variants' },
					{ relation: 'product',
						scope: {
							where: { visible: true },
							fields: [ 'id', 'title', 'description', 'tags' ],
							include: {
								relation: 'images',
								scope: {
									where: { visible: true },
									order: 'position ASC'
								}
							}
						}
					}
				],
			},
			venues = await Resource.find(filter),
			result = []

		for (const venue of venues) {
			const { id, name, capacity = 1, interval = 60, leadTime = 0, product, variants, shared, productId, placeId } = venue.toJSON()

			if (!product) continue

			const { title = '', description = '' } = venue.product().translations([ 'title', 'description' ], language),
				{ tags: t = {}, images: imgs } = product,
				images = imgs.map(i => i.original.url),
				{ category: tags = [] } = t,
				[ first ] = variants,
				{ unitPriceMeasure, prices } = first ?? {},
				productIds = [ productId ]

			result.push({
				id, name, title, description, capacity, interval, leadTime, unitPriceMeasure, prices,
				shared, images, tags, productIds, placeId
			})
		}

		return result
	}

	/**
	 * Availability of products for booking
	 * @param	{String[]} productIds
	 * @param	{Date} from
	 * @param	{Number} duration - in MINUTES
	 * @return	{Object} { earliest, schedule }
	 */
	Product.availableBookingWidget = async function(productIds, from, duration) {
		const { app } = Product,
			{ models, Metric } = app,
			{ Booking } = models,
			{ timeZone = TIMEZONE } = app.getSettings(LOCALE),
			now = new Date()

		try {
			// Get resources first to check their lead times
			const instances = await this.findResourcesByIds(productIds),
				resources = instances.map(r => r.toJSON()),
				leadTime = Math.max(...resources.map(r => r.leadTime || 0)),	// Use max of all resources
				minBookingTime = dayjs(now).add(leadTime - 1, 'minutes').toDate()

			// Enforce lead time policy
			if (dayjs(from).isBefore(minBookingTime)) {
				throw {
					code: 'BOOKING_LEADTIME',
					message: `Booking must be at least ${leadTime} minutes in advance`
				}
			}

			const resourceIds = resources.map(r => String(r.id)),
				{ start, end } = getStartEndTime(from, duration, timeZone),
				first = dayjs(start).tz(timeZone),
				numDays = dayjs(end).tz(timeZone).diff(first, DAY),
				schedule = []

			for (let i = 0; i <= numDays; i++) {
				const day = first.add(i, DAY).startOf(DAY),
					to = day.endOf(DAY).toDate(),
					date = day.toDate(),
					yy = day.year(),
					mm = day.month() + 1,
					dd = day.date(),
					occupied = await Booking.occupied(resourceIds, date, to),
					occupiedPeriods = this.occupiedPeriods(occupied, timeZone),
					resourcePeriods = this.resourcePeriods(resources, yy, mm, dd),
					openPeriodsList = []

				// derive available hours & quantity (shared only) for each resource
				let available

				for (const { resourceId, hours, capacity, shared } of resourcePeriods) {
					const { busy, quantity } = occupiedPeriods.reduce((res, occupiedPeriod) => {
						if (occupiedPeriod.resourceId === resourceId) {
							res.quantity += occupiedPeriod.quantity
							if (!shared || occupiedPeriod.quantity >= capacity) {
								res.busy.push(occupiedPeriod)
							}
						}
						return res
					}, { busy: [], quantity: 0 })

					if (shared) {
						available = (available || 0) + (capacity - quantity)	// shared resource has only 1 periods
					}
					openPeriodsList.push(periodsDifference(hours, busy))
				}

				// flatten into single list of periods, trim periods entirely enclosed by another (sorted by open.time)
				const openPeriods = periodsMerge(openPeriodsList),
					open = []

				for (const period of openPeriods) {
					const dow = dayOfWeek(date, timeZone)
					open.push({
						start: period.open.day === dow ? period.open.time : HOUR_0,
						end: Time.subtract(period.close.time, 1, MINUTE)
					})
				}

				schedule.push({ date, open, capacity: available })
			}

			appMetric(Metric.booking.availability)
			appMetric(Metric.booking.latency.availability, now)

			return { schedule }
		}
		catch (error) {
			appMetric(Metric.booking.error.availability, null, { error })
		}
	}

	/**
	 * Get Items for booking						(App/applet API)
	 * @param	{String[]} productIds
	 * @param	{Date} from
	 * @param	{Number} duration - in MINUTES
	 * @return	{Object[]} items with custom = { id, productIds, from, duration, start, end, capacity }
	 */
	Product.itemsForBooking = async function(productIds, from, duration) {
		const { app } = Product,
			{ timeZone = TIMEZONE } = app.getSettings(LOCALE),
			now = new Date()

		// Get resources first to check their lead times
		const instances = await this.findResourcesByIds(productIds),
			resources = instances.map(r => r.toJSON()),
			leadTime = Math.max(...resources.map(r => r.leadTime || 0)),	// Use max of all resources
			minBookingTime = dayjs(now).add(leadTime - 1, 'minutes').toDate()

		// Enforce lead time policy
		if (dayjs(from).isBefore(minBookingTime)) {
			throw {
				code: 'BOOKING_LEADTIME',
				message: `Booking must be at least ${leadTime} minutes in advance`
			}
		}

		const { start, end } = getStartEndTime(from, duration, timeZone),
			{ products } = await this.openForBooking(productIds, start, end)

		if (!resources.length) throw BOOKING_UNAVAILABLE

		const book = { id: shortId(), productIds, from, duration },
			[ first ] = resources,
			{ productId, capacity, shared } = first,
			product = products.find(p => String(p.id) === String(productId)),
			admit = shared ? 1 : capacity,
			items = await product.buildBookingItems(book, start, end, undefined, capacity, admit)

		return items
	}

	/**
	 * Get Items for Extending a Booking			(App/applet API)
	 * @param	{String} bookingId
	 * @param	{Number} duration - in MINUTES
	 * @return	{Object} { booking, items}
	 */
	Product.itemsForExtension = async function(bookingId, duration) {
		const { app } = Product,
			{ Booking } = app.models,
			{ timeZone = TIMEZONE } = app.getSettings(LOCALE),
			existing = await Booking.findById(bookingId),
			{ endTime, productId } = existing,
			from = dayjs(endTime).tz(timeZone).add(1, MINUTE).toDate(),
			booking = {
				productIds: [ productId ],
				from,
				duration,
			},
			items = await this.itemsForBooking([ productId ], from, duration)

		return { booking, items }
	}

	// -----  Remote Methods  -----

	Product.remoteMethod('resourcesForBookingWidget', {
		description: 'Get resources for booking (Booking Widget API)',
		http: { path: '/app/booking/resources', verb: 'get' },
		accepts: [
			{ arg: 'kind', type: 'string', http: { source: 'query' }, enum: [ EVENT, VENUE, TABLE ] },
		],
		returns: { type: 'array', root: true },
	})

	Product.remoteMethod('availableBookingWidget', {
		description: 'Availability of products for booking (Booking Widget API)',
		http: { path: '/app/booking/products/availability', verb: 'post' },
		accepts: [
			{ arg: 'productIds', type: 'array', required: true },
			{ arg: 'from', type: 'Date', required: true },
			{ arg: 'duration', type: 'number', required: true, description: 'in MINUTES' },
		],
		returns: { type: 'object', root: true, description: '{ earliest, schedule }' },
	})

	Product.remoteMethod('itemsForBooking', {
		description: 'Get Items for adding to Bag for booking (Booking Widget API)',
		http: { path: '/app/booking/products/items', verb: 'post' },
		accepts: [
			{ arg: 'productIds', type: 'array', required: true },
			{ arg: 'from', type: 'Date', required: true },
			{ arg: 'duration', type: 'number', required: true, description: 'in MINUTES' },
		],
		returns: { type: 'array', root: true },
	})

	Product.remoteMethod('itemsForExtension', {
		description: 'Get Items for extending booking (Booking Widget API)',
		http: { path: '/app/booking/items/extend', verb: 'post' },
		accepts: [
			{ arg: 'bookingId', type: 'string', required: true },
			{ arg: 'duration', type: 'number', required: true, description: 'in MINUTES' },
		],
		returns: { type: 'array', root: true },
	})
}
