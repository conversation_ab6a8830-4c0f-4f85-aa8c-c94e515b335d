/**
 *  @module Mixin:PrintReceipt with Gov Invoice create & cancel (TW only)
 */
const { Providers, Settings, Printers, Fulfillments } = require('@crm/types'),
	{ Identities, dayjs, rateLimit, Currencies } = require('@perkd/utils'),
	{ Print } = require('@perkd/orders'),
	{ formatPrice } = Print,
	{ spotName } = require('@perkd/fulfillments'),
	{ isBusinessRegNumber } = Identities.Taiwan

const { INVOICE } = Providers.Service,
	{ RECEIPT } = Printers.Purpose,
	{ LOCALE, ORDER, FULFILLMENT } = Settings.Name,
	{ DELIVER, DINEIN } = Fulfillments.Type,
	Q_PRINT_RECEIPT = 'print:receipt',
	limiter = rateLimit({ limit: 4, interval: 2000 })

module.exports = function(Order) {

	/**
	 * Print Receipt + Taiwan-only: submit and print official Invoice
	 *	invoice - { number, merchantName, merchantTaxId, taxId, id, key, checkSum,
 	 *				amount, currency, tax, taxRate, taxPeriod, issuedAt, barcode, qrCodeLeft, qrCodeRight }]
	 * @param	{Place} [to] print to this place
	 */
	Order.prototype.printReceipt = async function(to) {
		const { app } = Order,
			{ id: orderId, receipt = {}, itemList: items, storeId } = this.toJSON(),
			{ taxId, invoice } = receipt,
			{ timeZone } = app.getSettings(LOCALE),
			{ handleInvoice, invoiceTemplate, invoiceHeader: merchantName, invoiceFooter: footer } = app.getSettings(ORDER),
			settings = app.getSettings(FULFILLMENT),
			toPrint = !taxId || isBusinessRegNumber(taxId),
			printInvoice = handleInvoice && invoice && toPrint

		try {
			const [ mainFulfill, store ] = await Promise.all([
					Order.findMainFulfillment(orderId),
					to || this.store.get(),
				]),
				{ type, destination, deliver = {}, receipt: recpt = {} } = mainFulfill.toJSON(),
				{ ticketTemplate } = settings[type] ?? {}

			if (!ticketTemplate && !printInvoice) return

			await this.queue(`${Q_PRINT_RECEIPT}:${storeId}`, async () => {
				const [ printer ] = store ? await store.selectPrinter(RECEIPT) : []

				if (ticketTemplate) {
					await mainFulfill.printTicket(store, printer)
				}
				if (printInvoice) {
					const { number, issuedAt: at } = invoice,
						issuedAt = dayjs(at).tz(timeZone).format('YYYY-MM-DD HH:mm:ss'),
						note = (type === DINEIN)
							? spotName(destination)
							: (type === DELIVER) ? deliver.ticket : recpt.queue,
						content = { items, ...invoice, issuedAt, note, merchantName, footer }

					appNotify(`====== ${number} =======`, null, null, '-printer')

					if (printer) {
						await printer.print(content, invoiceTemplate)
							.catch(err => appNotify('[Order]printReceipt', { err, invoiceTemplate, content }, 'error'))
					}
					appNotify('', content, null, '-printer')
				}
			})
		}
		catch (err) {
			console.error('[Order]printReceipt', err)
			appNotify('[Order]printReceipt', { err }, 'error')
		}
	}

	/**
	 * Print physical receipt
	 * @param	{Place} [to] print to this place
	 */
	Order.prototype.printCustomReceipt = async function(to) {
		const { app } = Order,
			{ Place } = app.models,
			{ physicalReceiptTemplate } = app.getSettings(ORDER),
			{ amount, currency: code, receipt = {}, discountAmount = 0, shippingPrice = 0, taxAmount = 0, itemList: items, storeId, when } = this.toJSON(),
			{ invoice, number } = receipt,
			{ paid } = when,
			{ timeZone, languages = [] } = app.getSettings(LOCALE),
			[ language = EN ] = languages,
			store = to || await this.store.get(),
			currency = Currencies.currency(code, true),
			receiptNumber = invoice?.number ?? number

		try {
			if (!physicalReceiptTemplate) {
				appNotify('[Order]printCustomReceipt', 'physicalReceiptTemplate is not configured', 'warn')
				return
			}

			// Calculate item prices and prepare formatted items
			let itemsBeforeDiscount = 0
			const formatedItems = items.reduce((acc, item) => {
				const { price = 0, unitPrice = 0, quantity = 1, variant = {}, bundleId, bundled = [] } = item,
					{ title } = variant

				// Only process main items (not bundled items)
				if (!bundleId) {
					itemsBeforeDiscount += unitPrice * quantity

					let mainItemPrice = unitPrice * quantity

					const mainItem = {
						title,
						quantity,
						price: formatPrice(mainItemPrice, currency)
					}
					acc.push(mainItem)
					
					// Add any sub-items
					if (bundled && bundled.length > 0) {
						const subItems = items.filter(i => bundled.includes(i.id))
						
						for (const subItem of subItems) {
							const { 
								variant: subVariant = {}, 
								quantity: subQuantity = 1,
								unitPrice: subUnitPrice = 0
							} = subItem,
							{ title: subTitle } = subVariant

							mainItemPrice -= subUnitPrice * subQuantity
							
							acc.push({
								title: `-${subTitle}`,
								quantity: subQuantity,
								price: formatPrice(subUnitPrice * subQuantity, currency),
								isBundled: true
							})
						}

						mainItem.price = formatPrice(mainItemPrice, currency)
					}
				}
				
				return acc
			}, []),
			subtotal = itemsBeforeDiscount,
			total = amount
			
			// Prepare content for the receipt
			const content = {
				header: invoice?.title ?? 'Receipt',
				receiptNumber,
				time: dayjs(paid).tz(timeZone).format('YYYY-MM-DD HH:mm'),
				items: formatedItems,
				subtotal: formatPrice(subtotal, currency),
				discount: formatPrice(discountAmount * -1, currency),
				shipping: formatPrice(shippingPrice, currency),
				total: formatPrice(total, currency),
				tax: formatPrice(taxAmount, currency)
			}

			await this.queue(`${Q_PRINT_RECEIPT}:${storeId}`, async () => {
				const [ printer ] = await store.selectPrinter(RECEIPT)

				appNotify(`====== ${receiptNumber} =======`, null, null, '-printer')

				if (printer) {
					await printer.print(content, physicalReceiptTemplate, { language })
						.catch(err => appNotify('[Order]printCustomReceipt', { err, physicalReceiptTemplate, content, language }, 'error'))
				}
				appNotify('', content, null, '-printer')
			})
		}
		catch (error) {
			console.error('[Order]printCustomReceipt', error)
			appNotify('[Order]printCustomReceipt', { error }, 'error')
		}
	}

	/**
	 * Create Tax Invoice & update order receipt (TW only)
	 * @param {Boolean} print - immediately
	 * @return {Invoice|void}
	 */
	Order.prototype.createInvoice = async function(print) {
		const { app } = Order,
			{ Place } = app.models,
			{ handleInvoice } = app.getSettings(ORDER),
			{ id, amount, currency, receipt = {}, shippingPrice, itemList: items, storeId } = this.toJSON(),
			{ taxId, invoice } = receipt,
			orderId = String(id),
			details = { amount, currency, items, shippingPrice, orderId }

		if (!storeId || !handleInvoice || amount <= 0) return undefined		// skip
		if (invoice) return invoice

		try {
			const { name: shop } = await Place.findById(storeId),
				provider = await Order.getProviderByService(INVOICE, undefined, shop)

			if (!provider) return undefined

			const created = await limiter.removeTokens(1).then(() => provider.invoices.create(details, { taxId })),
				{ merchantTaxId } = created

			receipt.invoice = created
			receipt.merchantTaxId = merchantTaxId
			receipt.print = print

			await this.updateAttributes({ receipt })

			return created
		}
		catch (err) {
			console.error('[Order]createInvoice', err)
			appNotify('[Order]createInvoice', { err }, 'error')
		}
	}

	/**
	 * Cancel Tax Invoice (TW only)
	 * @param	{String} [reason] for cancellation
	 * @param	{Date} [at]
	 */
	Order.prototype.cancelInvoice = async function(reason, at) {
		const { app } = Order,
			{ Place } = app.models,
			{ receipt = {}, storeId } = this.toJSON(),
			{ invoice } = receipt,
			{ id } = invoice ?? {}

		const { name: shop } = storeId ? await Place.findById(storeId) : {},
			provider = shop
				? await Order.getProviderByService(INVOICE, undefined, shop)
				: await Order.getProviderByService(INVOICE)

		if (!provider || !id) return

		try {
			await provider.invoices.cancel(id, reason, at)
			delete receipt.invoice
			await this.updateAttributes({ receipt })
		}
		catch (err) {
			console.error('[Order]cancelInvoice', err)
			appNotify('[Order]cancelInvoice', { err }, 'error')
		}
	}
}
