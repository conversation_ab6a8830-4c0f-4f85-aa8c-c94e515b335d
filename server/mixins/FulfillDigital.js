/**
 *  @module Mixin:FulfillDigital - synchronous fulfillment of digital products (required by Buy mixin)
 */

const { Products, Orders, Fulfillments, Wallet } = require('@crm/types'),
	{ shortId } = require('@perkd/utils')

const { Notify } = Wallet,
	{ BOOKING } = Orders.ItemKind,
	{ STOREDVALUE, TICKET, GIFT_CARD, VOUCHER, WIDGET, MEMBERSHIP } = Products.Kind,
	{ DIGITAL } = Fulfillments.Type,
	{ SUCCESS } = Fulfillments.Status,
	{ ISSUE } = Notify.Action,
	ADMIT = 'admit'

module.exports = function(Variant) {

	/**
	 * Fulfill digital products - Stored Value, Voucher/Tickets, Gift Cards
	 * @param	{String} orderId
	 * @param	{Object[]} items
	 * @param	{Object} recipient - either
	 *			{String} [membershipId]
	 *			{String} [cardId]
	 * @return	{Promise<Object>} fulfilled - { [{ kind, id, productId, variantId, quantity, membershipId, cardId ... }] }
	 *			{Object[]} [storedvalue] - [{ ... cardId, amount }]
	 *			{Offer[]} [offer] - [{ ... quantity, digitalIds }]
	 *			{Object[]} [giftcard] - [{ ... fulfillmentService }]
	 */
	Variant.fulfillDigital = async function(orderId, items = [], recipient = {}) {
		const { models, Event } = Variant.app,
			{ Business, OfferMaster, Fulfillment, ProductImage } = models,
			{ membershipId, cardId } = recipient,
			bookingItems = items.filter(i => i.kind === BOOKING),
			tickets = await this.buildTickets(bookingItems),
			fulfillment = { type: DIGITAL, orderId, status: SUCCESS, itemList: [] },
			ticketsIssued = [],
			storedvalue = [],
			offers = [],
			widgets = [],
			fulfilled = {}

		// --- Issue Tickets (multiple items (variants) with same productId => same ticket)
		for (const { id, quantity, ticket, masterId } of tickets) {
			const kind = BOOKING,
				noNotify = true,
				issued = await OfferMaster.issue(masterId, membershipId, { quantity, ticket, noNotify, orderId })
					.catch(err => {
						appLog('digital_fulfill_fail', { err, kind, masterId, membershipId, quantity })
						return []
					}),
				ids = issued.map(o => o.id),
				digitalIds = issued.map(o => o.digital.id)

			ticketsIssued.push({ id, quantity, membershipId, ids, digitalIds })
		}

		for (const item of items) {
			const { id, product = {}, variant = {}, quantity = 1, discountAmount = 0, custom } = item,
				{ id: productId } = product,
				{ id: variantId, kind, digital = {}, storedValue = {}, fulfillmentService, imageIds = [] } = variant,
				{ masterId, widgetKey } = digital

			switch (kind) {
			case STOREDVALUE: {
				const amount = quantity * (storedValue.balance || (item.price + discountAmount)),
					discount = discountAmount / amount,
					{ balance, currency } = await Business.storedvalueTopup(cardId, amount, discount)

				storedvalue.push({ id, kind, quantity, amount, balance, currency, productId, variantId, membershipId, cardId })
				break
			}

			case VOUCHER: {
				const issued = await OfferMaster.issue(masterId, membershipId, { quantity, orderId })
						.catch(err => {
							appLog('digital_fulfill_fail', { err, kind, masterId, membershipId, quantity })
							return []
						}),
					ids = issued.map(o => o.id),
					digitalIds = issued.map(o => o.digital.id)

				offers.push({ id, kind, quantity, productId, variantId, membershipId, cardId, ids, digitalIds })
				break
			}

			case TICKET: {
				const issued = ticketsIssued.find(t => t.id === custom.id),
					{ ids = [], digitalIds = [] } = issued ?? {}

				offers.push({ id, kind, quantity, custom, productId, variantId, membershipId, cardId, ids, digitalIds, orderId })
				break
			}

			case WIDGET: {
				const key = widgetKey,
					widgetId = shortId(5),
					urlsWithIds = await ProductImage.getUrlsWithIds(imageIds),
					imageUrls = Object.values(urlsWithIds),
					data = { cardId, key, id: widgetId, custom, variant, imageUrls }

				appEmit(Event.widget.data.update, data)
				widgets.push({ id, kind, productId, variantId, cardId, widgetId, key })
				break
			}

			case GIFT_CARD: {
				appNotify(`[Variant]fulfillDigital gift card, provider: '${fulfillmentService}'`, { masterId, quantity, item })
				break
			}

			default:
				appNotify('[Variant]fulfillDigital unsupported digital kind', { kind, item, recipient }, 'error')
			}
		}

		if (ticketsIssued.length) {
			Variant.notifyTickets(ticketsIssued)
		}
		if (offers.length) {
			fulfilled.offer = offers
		}
		if (storedvalue.length) fulfilled.storedvalue = storedvalue

		fulfillment.itemList = [ ...storedvalue, ...offers, ...widgets ]
		Fulfillment.create(fulfillment)
		return fulfilled
	}

	/**
	 * Cancel fulfilled digital products - Stored Value, Voucher/Tickets, Gift Cards
	 * @param	{Object[]} fulfilled items
	 * 			{String} kind
	 * 			{String} id - of item
	 * 			{String} productId
	 * 			{String} variantId
	 * 			{Number} quantity
	 * 			{String} membershipId
	 * 			{String} cardId
	 * 			{Number} [amount] - stored value
	 * 			{String[]} [ids] - voucher, ticket
	 * 			{String[]} [key] - widget
	 * 			{String[]} [widgetId] - widget
	 * 			{String} [fulfillmentService] - gift card
	 * @return	{Promise<Item[]>} cancelled
	 */
	Variant.cancelDigitalFulfill = async function(fulfilled = []) {
		const { models, Event } = Variant.app,
			{ Business, Offer } = models,
			cancelled = []

		for (const item of fulfilled) {
			const { kind, cardId, amount, ids, key, widgetId, fulfillmentService } = item

			switch (kind) {
			case STOREDVALUE: {
				await Business.storedvalueDeduct(cardId, amount)
				cancelled.push(item)
				break
			}

			case VOUCHER:
			case TICKET: {
				await Offer.cancel(ids)
				cancelled.push(item)
				break
			}

			case WIDGET: {
				appEmit(Event.widget.data.delete, { cardId, key, id: widgetId })
				cancelled.push(item)
				break
			}

			case GIFT_CARD:
			case MEMBERSHIP:
				// FIXME no action required if card not issued (async payment), need to fix if already issued
				break

			default:
				appNotify('[Variant]cancelDigitalFulfill unsupported kind:', { kind, item, ids }, 'error')
				break
			}
		}

		return cancelled
	}

	Variant.buildTickets = async function(bookingItems = []) {
		const { Booking, Resource, Place } = Variant.app.models,
			uniqueItems = bookingItems.reduce((res, booking) => {
				const { variant = {}, custom } = booking,
					{ productId, id: variantId, digital = {} } = variant,
					{ masterId } = digital,
					{ id, bookingId } = custom

				if (!res.some(b => b.bookingId === bookingId)) {
					res.push({ id, bookingId, masterId, productId, variantId })
				}
				return res
			}, []),
			tickets = []

		for (const item of uniqueItems) {
			const { bookingId } = item,
				booked = await Booking.findById(bookingId),
				{ startTime, endTime, capacity: admit, status, quantity, resourceId } = booked,
				resource = await Resource.findById(resourceId),
				{ name, description, position, placeId = null } = resource,
				place = await Place.findById(placeId),
				{ geo, addressList = [] } = place || {},
				{ formatted: formattedAddress, short: shortAddress } = addressList.find(a => a.valid) || {},
				venue = { name, geo, position, formattedAddress, shortAddress, placeId },
				fields = [
					{ key: ADMIT, value: admit },
				]

			tickets.push({
				...item,
				quantity,
				ticket: { description, startTime, endTime, status, venue, fields, bookingId, resourceId }
			})
		}

		return tickets
	}

	/**
	 * Notify total tickets fulfilled
	 * @param {Offers[]} tickets
	 */
	Variant.notifyTickets = async function(tickets = []) {
		const { Perkd } = appModule('perkd'),
			{ ids, total } = tickets.reduce((res, { digitalIds, quantity }) => {
				res.total += quantity
				res.ids.push(...digitalIds.map(String))

				return res
			}, { ids: [], total: 0 })

		if (total > 0) {
			const notification = { action: ISSUE, options: { fetch: true, banner: true } }
			await Perkd.offers.notify(ids, notification)
		}
	}
}
