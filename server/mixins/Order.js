/**
 *  @module Mixin:Order - implements methods required by Buy mixin
 */

const { Memberships, Fulfillments, Orders, Providers, Touchpoints } = require('@crm/types'),
	{ MOrder, allItems } = require('@perkd/commerce'),
	{ pickObj } = require('@perkd/utils'),
	{ groupItemsByService, reservationsFrom } = require('@perkd/orders'),
	{ ORDER: ORDER_ERR } = require('@perkd/errors/dist/service')

const { ACTIVE } = Memberships.State,
	{ Qualifier: QUALIFIER } = Memberships,
	{ DIGITAL } = Fulfillments.Service,
	{ BOOKING } = Orders.ItemKind,
	{ Attributed } = Touchpoints,
	{ PERKD, SHOPIFY } = Providers.PROVIDER,		// channels
	{ ORDER_INVALID, ITEM_NOT_FOUND, ITEM_NO_STOCK, ITEM_PRICING_INVALID } = ORDER_ERR,
	{ item: ITEM_FIELDS, product: PRODUCT_FIELDS, variant: VARIANT_FIELDS } = appSettings().order.fields

module.exports = function(Variant) {

	/**
	 * Build a mOrder, complete with Products, Variants & Offers	(used by Buy mixin)
	 * @param	{String} userId
	 * @param	{Object[]} payments
	 * @param	{Object} order
	 * @param	{Object[]} [pricings] - not used
	 * @param	{Object} [options]
	 * @return	{MOrder} mOrder
	 */
	Variant.orderPrepare = async function(userId, payments, order, pricings, options = {}) {
		const { app, name: modelName } = this,
			{ Membership, Booking } = app.models,
			{ items = [], discounts = [], fulfillment = {} } = order,
			{ cardId, through = {} } = options,
			{ format, attributedTo = {} } = through,
			{ destination = {} } = fulfillment,
			{ resourceId } = destination,
			provider = SHOPIFY, // default to shopify for now, use param when we have multiple storefronts
			[ membership, variants, offers, [ booking ] ] = await Promise.all([
				cardId ? Membership.findLastJoinByCardId(cardId) : undefined,
				Variant.findFromExternalIds(items, provider),
				findOffers(discounts),
				resourceId ? Booking.forResources([ resourceId ]) : [],
				Variant.injectRecipientProfile(userId, order)		// mutates order.fulfillment
			]),
			{ id: membershipId, memberId, personId, state } = membership || {}

		// inject ids into order
		order.modelName = modelName
		order.personId = personId
		order.memberId = memberId
		if (state === ACTIVE) {
			order.membershipId = membershipId
		}

		if (booking && format === BOOKING) {
			attributedTo.type = Attributed.BOOKING
			attributedTo.id = String(booking.id)
			through.attributedTo = attributedTo
		}
		options.through ||= through

		replaceOfferIds(discounts, offers)		// mutates discounts
		external2CrmIds(order, variants)		// mutates order.items, throw if missing items

		const mOrder = new MOrder(order, getPricings(variants), payments, userId, options),
			valid = mOrder.validate()

		if (valid === true) return mOrder

		const err = valid,
			{ code, details } = err,
			zeroQuantityItems = details.items?.map(i => ({ variantId: i.variantId, quantity: 0 }))

		appNotify('[orderPrepare]', { code, details }, 'error')

		return (code === ITEM_PRICING_INVALID)
			? this.rejectErr(ITEM_NOT_FOUND, { items: zeroQuantityItems, code })
			: this.rejectErr(ORDER_INVALID, { err, code }, true)
	}

	/**
	 * Qualify order and inject qualified into mOrder, to be used by orderApplyQualified()		(used by Buy mixin)
	 * 	- throw if any offer, item invalid, insufficient stock, or booking unavailable
	 * @param	{mOrder} mOrder - mutated
	 * @return	{Object[]} [ { booking } ]
	 */
	Variant.orderQualify = async function(mOrder) {
		const { Product } = this.app.models,
			bookings = await Product.requestBookings(mOrder),		// mOrder mutated
			{ channel, items } = mOrder,
			[ inventory ] = await Promise.all([
				this.checkInventory(items, channel),
				this.checkOffers(mOrder),
				this.checkHours(mOrder)
			]),
			notFound = inventory[ITEM_NOT_FOUND],
			outOfStock = inventory[ITEM_NO_STOCK],
			qualified = []

		if (notFound.length) {
			return this.rejectErr(ITEM_NOT_FOUND, { items: notFound })
		}
		if (outOfStock.length) {
			return this.rejectErr(ITEM_NO_STOCK, { items: outOfStock })
		}

		for (const booking of bookings) {
			qualified.push({ booking })
		}

		return qualified
	}

	/**
	 * Apply Qualified list AFTER securing payment				(used by Buy mixin)
	 * IMPORTANT:
	 * 	1.	for sync payments: called BEFORE createOrder() with mOrder
	 * 	2.	for async payments: called AFTER createOrder() with Order instance
	 * @param 	{Order|MOrder} order - CRM order (OR) mOrder (mutated)
	 * @return	{Object[]} entitlements - [{ booking } | { item }], to be used for orderFulfill()
	 */
	Variant.orderApplyQualified = async function(order) {
		const { Event, models } = Variant.app,
			{ Membership, Program, Booking, Place } = models,
			{ options = {}, external = {} } = order,
			{ itemList, items = itemList } = order,		// either CRM Order (async payments) or mOrder
			orderId = String(order.id),
			personId = order.personId ? String(order.personId) : undefined,
			membershipId = order.membershipId ? String(order.membershipId) : undefined,
			isMOrder = order instanceof MOrder,
			{ cardId } = isMOrder ? options : external[PERKD],
			confirmed = [],
			booked = [],
			deductions = [],
			entitlements = []

		try {
			if (!membershipId && cardId) {
				const membership = await Membership.findLastJoinByCardId(cardId),
					{ state, programId, memberId } = membership ?? {},
					qualifiers = [ QUALIFIER.JOIN ],
					programs = [ programId ]

				if (state !== ACTIVE) {
					const qualified = await Program.qualifyAll(memberId, null, { qualifiers, programs })

					if (qualified) {
						order.membershipId = qualified.id
					}
				}
			}

			for (const item of items) {
				const { kind, variant: v = {}, variantId = v.id, quantity, custom = {} } = item,
					variant = await Variant.findById(variantId)

				// draw-down inventory
				if (kind === BOOKING) {
					const { bookingId: id } = custom

					// confirm bookings ONCE, multiple items may be same booking
					if (!confirmed.includes(id)) {
						confirmed.push(id)
						booked.push(
							Booking.confirm(id, orderId, { personId, membershipId, cardId })
						)
					}

					entitlements.push({ booking: { id, variantId, orderId } })
				}
				else {
					deductions.push(
						variant.deductInventory(quantity)
					)
					entitlements.push({ item })
				}
			}

			if (booked.length) {
				Promise.all(booked)
					.then(async (instances)=> {
						const [ first = {} ] = instances,
							{ placeId } = first,
							place = await Place.findById(placeId),
							reservations = reservationsFrom(instances.map(instance => instance.toJSON()))

						// Emit event for each reservation
						for (const reservationId in reservations) {
							appEmit(Event.sales.booking.reservation.confirmed, {
								reservationId,
								bookings: reservations[reservationId],
								place
							})
						}
					})
					.catch(err => {
						appNotify('[bookingConfirm]', { err, order }, 'error')
					})
			}

			await Promise.all(deductions)
			return entitlements
		}
		catch (err) {
			appNotify('[orderApplyQualified]', { err, order }, 'error')
			throw err
		}
	}

	/**
	 * Reverse qualified or applied benefit for order, eg. cancelling booking		(used by Buy mixin)
	 * @param	{mOrder} mOrder
	 * @param	{Object[]} qualified - returned by orderQualify()
	 * @param	{Object[]} entitlements - [{ booking } | { item }]  returned by orderAppliedQualified()
	 */
	Variant.orderWithdrawQualified = async function(mOrder, qualified = [], entitlements = []) {
		const { Booking } = this.app.models

		if (entitlements.length) {
			const items = []

			for (const { booking, item } of entitlements) {
				if (item) {
					items.push(item)
				}
				if (booking) {
					Booking.deleteById(booking.id)
						.catch(err => appNotify('[Variant]orderWithdrawQualified', { err, booking }, 'error'))
				}
			}

			Variant.returnInventory(items)
		}
		else {
			for (const { booking } of qualified) {
				if (booking) {
					Booking.deleteById(booking.id)
						.catch(err => appNotify('[Variant]orderWithdrawQualified', { err, booking }, 'error'))
				}
			}
		}
	}

	/**
	 * Create Order instance				(used by Buy mixin)
	 * @param	{mOrder} mOrder
	 * @param	{Object[]} payments
	 * @param	{Object[]} qualifiedOrEntitlements - not used.  returned by orderApplyQualified()
	 * @return	{Order} order
	 */
	Variant.orderCreate = async function(mOrder, payments, qualifiedOrEntitlements = []) {
		const { Order, Membership } = this.app.models,
			{ membershipId } = mOrder,
			membership = membershipId ? await Membership.findById(membershipId)
				.catch(err => {
					appNotify('[orderCreate] findMembership', { err, membershipId }, 'error')
				}) : undefined,
			data = mOrder.buildOrder(payments, membership)

		return Order.createOrder(data)
	}

	/**
	 * Fulfill (qualified) order after successful payment		(used by Buy mixin)
	 * 	- order maybe paid (orderPay) or non-paid (order)
	 * @param	{Order} order - crm
	 * @param	{Item[]} entitlements returned by orderApplyQualified()
	 * @param	{Object} options
	 * @return	{Promise<Object[]>} list of items fulfilled
	 */
	Variant.orderFulfill = async function(order, entitlements, options) {
		const { id: orderId, itemList = [], external = {}, membershipId } = order,
			{ cardId } = external[PERKD] || {},
			grouped = groupItemsByService(itemList),
			services = Object.keys(grouped),
			recipient = { membershipId, cardId },
			fulfilled = {}

		for (const service of services) {
			// only DIGITAL fulfilled synchronously, others handled in Sales svc
			if (service === DIGITAL) {
				const items = grouped[service],
					fulfilledItems = await Variant.fulfillDigital(orderId, items, recipient)

				Object.assign(fulfilled, fulfilledItems)
			}
		}

		return fulfilled
	}

	/**
	 * Transform external Order items - map external variantId/sku/gtin to CRM item, product & variant properties
	 * @param	{Object} order (items mutated)
	 * @return	{Object} complete order
	 */
	Variant.transformExternalOrder = async function(order = {}, provider) {
		const { items = [] } = order,
			variants = await Variant.findFromExternalIds(items, provider)

		external2CrmIds(order, variants)	// mutates order.items
		order.items = await Variant.completeItems(order.items)

		return order
	}

	/**
	 * Complete items with item, product & variant properties
	 * @param	{Object[]} items
	 * @return	{Object[]} completed items
	 */
	Variant.completeItems = async function(items = []) {
		const { Product } = Variant.app.models,
			complete = []

		for (const item of items) {
			const { productId, variantId } = item,
				[ product, variant ] = await Promise.all([
					productId ? Product.findById(productId) : item.product,
					variantId ? Variant.findById(variantId) : item.variant,
				]),
				data = pickObj(item, ITEM_FIELDS)

			data.product = pickObj(product, PRODUCT_FIELDS) || { id: productId }		// just in case
			data.variant = pickObj(variant, VARIANT_FIELDS) || { id: variantId }

			// substitute title of option values (to those of CRM)
			const { options = [] } = variant?.toJSON ? variant.toJSON() : {},
				{ variantOptions = [] } = data

			for (const { key, values = [] } of variantOptions) {
				const option = options.find(o => o.key === key)

				if (!option) continue

				for (const value of values) {
					const matched = option.values?.find(o => o.id === value.id)

					if (matched) {
						value.title = matched.title
					}
				}
			}

			complete.push(data)
		}

		return complete
	}

	// -----  Private Functions  -----

	/**
	 * Find Offers from discounts
	 * @param	{Object[]} discounts of mOrder
	 * @return	{Promise<Offer[]>}
	 */
	async function findOffers(discounts = []) {
		const { Offer } = Variant.app.models,
			digitalIds = []

		for (const { offerId } of discounts) {
			if (offerId) digitalIds.push(offerId)
		}

		return digitalIds.length ? Offer.findByDigitalIds(digitalIds) : []
	}

	/**
	 * Replace sku / gtin with CRM productId & variantId, throw if not found!
	 * @param	{Object} order - items mutated!! (including those in bundles)
	 * @param	{Variant[]} variants instances
	 * @return	{Object} order
	 */
	function external2CrmIds(order, variants = []) {
		const { items = [] } = order,
			all = allItems(items)	// include bundles

		for (const item of all) {
			const variant = variantOf(item, variants)

			// Order will substitute with actual Product/Variant instances
			if (variant) {
				item.productId = variant.productId.toString()
				item.variantId = variant.id.toString()
				item.variant = variant.toJSON()

				if (item.custom) {
					item.custom.productIds = [ item.productId ]
				}
			}
			else {
				appNotify('[external2CrmIds] SKU or variantId not found', { item }, 'error')
				return Variant.rejectErr(ITEM_NOT_FOUND, { items: [ item ] })
			}
		}

		return order
	}

	function variantOf(item, variants, provider = SHOPIFY) {
		const { variantId, sku, gtin } = item

		return variants.find(v => variantId && v.external?.[provider] && String(v.external[provider].variantId) === String(variantId))
			|| variants.find(v => typeof sku === 'string' && v.sku === sku && Array.isArray(v.channels) && v.channels.includes(PERKD))
			|| variants.find(v => typeof sku === 'string' && v.sku === sku)
			|| variants.find(v => typeof gtin === 'string' && (v.gtin || {}).value === gtin)
			|| variants.find(v => variantId && v.id && String(v.id) === String(variantId))
	}

	/**
	 * Replace Offer digitalId with offerId
	 * @param	{Object[]} discounts (mutated)
	 * @param	{Object[]} offers
	 * @return	{Object[]} discounts
	 */
	function replaceOfferIds(discounts = [], offers = []) {
		for (const discount of discounts) {
			const offer = offers.find(({ digital }) => digital.id === discount.offerId)
			if (offer) discount.offerId = offer.id
		}
		return discounts
	}

	/**
	 * Get aggregated pricings from variant.prices
	 * @param	{Variant[]} variants
	 * @return	{Object[]} pricings
	 */
	function getPricings(variants = []) {
		return variants.reduce((pricings, variant) => {
			const { id, prices, productId } = variant
			if (prices) {
				for (let j = 0; j < prices.length; j++) {
					const price = prices[j]
					price.productId = productId.toString()
					price.variantId = id.toString()
				}
				pricings.push(...prices)
			}
			return pricings
		}, [])
	}

	// -----  Remote methods  -----

	Variant.remoteMethod('orderCommit', {
		description: 'Commit (pending) Order',
		http: { path: '/order/commit', verb: 'post' },
		accepts: [
			{ arg: 'transaction', type: 'object', required: true },
			{ arg: 'options', type: 'object' },
		],
		returns: { type: 'object', root: true },
	})

	Variant.remoteMethod('orderCancel', {
		description: 'Cancel (pending) Order',
		http: { path: '/order/cancel', verb: 'post' },
		accepts: [
			{ arg: 'transaction', type: 'object', required: true },
			{ arg: 'options', type: 'object', required: true },
		],
		returns: { type: 'object', root: true },
	})

	Variant.remoteMethod('transformExternalOrder', {
		description: 'Transform external Order items - map external variantId/sku/gtin to CRM item, product & variant properties',
		http: { path: '/order/external/transform', verb: 'post' },
		accepts: [
			{ arg: 'order', type: 'object', required: true, description: 'Order with EXTERNAL variantId/sku/gtin in items' },
			{ arg: 'provider', type: 'string' },
		],
		returns: { type: 'object', root: true },
	})

	Variant.remoteMethod('completeItems', {
		description: 'Complete items with item, variant & product properties',
		http: { path: '/order/items/complete', verb: 'post' },
		accepts: [
			{ arg: 'items', type: 'array', required: true, description: 'List of items' },
		],
		returns: { type: 'array', root: true },
	})
}
