/**
 *  @module Model:MenuWidgetApi		used by Menu related widgets
 */
const { Products } = require('@crm/types'),
	{ App } = require('@crm/loopback'),
	{ Service, HttpErrors } = require('@perkd/errors'),
	{ getContext } = App

const { ACTIVE, HIDE, SOLDOUT } = Products.Availability,
	{ CONTINUE, DENY } = Products.Policy,
	{ STAFF: ERROR } = Service,
	{ INVALID_PLACE } = ERROR,
	STATUS = [ ACTIVE, HIDE, SOLDOUT ]

module.exports = function(Product) {

	/**
	 * Set Product availability, by variantId (for now, until menu supports productId)  (Staff Widgets API)
	 * @param	{String} variantId
	 * @param	{String} availability - active | soldout | hide
	 */
	Product.setProductAvailbilityByWidget = async function (variantId = null, availability) {
		const { Variant } = Product.app.models,
			variant = await Variant.findById(variantId)

		if (!STATUS.includes(availability)) throw 'invalid availability'
		if (!variant) throw { statusCode: 404, message: 'variant not found' }

		const { productId = null } = variant,
			product = await Product.findById(productId)

		if (!product) throw { statusCode: 404, message: 'product not found' }

		await product.updateAttributes({ availability })
	}

	/**
	 * Set Variant Status  (Staff Widgets API)
	 * @param	{String} id variant
	 * @param	{String} [availability] - active | hide | soldout
	 * @param	{Number | null} [quantity] - inventory quantity, null => unlimited
	 */
	Product.setVariantStatusByWidget = async function (id = null, availability, quantity) {
		const { Variant } = Product.app.models,
			variant = await Variant.findById(id)

		if (!variant) throw { statusCode: 404, message: 'variant not found' }

		if (availability) {
			if (!STATUS.includes(availability)) throw 'invalid availability'

			const { productId = null } = variant,
				product = await Product.findById(productId)

			product.updateAttributes({ availability })

			if (availability === SOLDOUT) {
				variant.updateAttributes({ 'inventory.quantity': 0 })
			}
		}

		if (quantity !== undefined) {
			if (typeof quantity === 'number') {
				variant.updateAttributes({
					'inventory.quantity': quantity,
					'inventory.policy': DENY
				})
			}
			else if (quantity === null) {
				variant.updateAttributes({
					'inventory.policy': CONTINUE
				})
			}
			else throw { statusCode: 400, message: 'invalid quantity' }
		}
	}

	/**
	 * Request provider (grab/ubereats) to sync menu
	 * @param	{String} provider
	 */
	Product.refreshMenu = async function(provider) {
		const { models } = Product.app,
			{ Place } = models,
			providerMapping = { grabfood: 'grab', grabmart: 'grab' },
			providerModel = models[providerMapping[provider] || provider],
			{ location } = getContext(), // if called from staff card
			{ id, placeId = id } = location ?? {}

		if (!placeId) return providerModel.menuRefresh(provider)

		const place = await Place.findById(placeId),
			{ external } = place ?? {},
			{ storeId: shop } = external[provider] ?? {}

		if (!shop) throw HttpErrors.BadRequest(INVALID_PLACE)
		return providerModel.menuRefresh(provider, shop)
	}

	// -----  Remote Methods  -----

	Product.remoteMethod('setProductAvailbilityByWidget', {
		description: 'Set Availability for Product (Staff Widgets API)',
		http: { path: '/staff/products/:variantId/availability', verb: 'post' },
		accepts: [
			{ arg: 'variantId', type: 'string', http: { source: 'path' }, required: true },
			{ arg: 'availability', type: 'string', enum: STATUS, required: true },
		],
		returns: { type: 'object', root: true },
	})

	Product.remoteMethod('setVariantStatusByWidget', {
		description: 'Set Variant Status  (Staff Widgets API)',
		http: { path: '/staff/variants/:id/status', verb: 'post' },
		accepts: [
			{ arg: 'id', type: 'string', http: { source: 'path' }, required: true },
			{ arg: 'availability', type: 'string', enum: STATUS },
			{ arg: 'quantity', type: 'any', description: 'inventory quantity, null for unlimited' },
		],
		returns: { type: 'object', root: true },
	})

	Product.remoteMethod('refreshMenu', {
		description: 'Request Provider (grab/ubereats) to sync menu (Staff Widgets API)',
		http: { path: '/staff/menu/refresh', verb: 'post' },
		accepts: [
			{ arg: 'provider', type: 'string', required: true }
		]
	})
}