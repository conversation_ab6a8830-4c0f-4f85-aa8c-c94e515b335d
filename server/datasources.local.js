const LOCALHOST = '127.0.0.1',
	{
		NODE_ENV,
		DB_HOST = NODE_ENV ? 'mongodb' : LOCALHOST,
		DB_USERNAME = '',
		DB_PASSWORD = '',
		DB_AUTH = 'admin',
		DB_SET = '',
		SERVICE_HOST = LOCALHOST,
		OFFER_HOST = SERVICE_HOST,
		BUSINESS_HOST = SERVICE_HOST,
		SALES_HOST = SERVICE_HOST,
		PERSON_HOST = SERVICE_HOST,
		MEMBERSHIP_HOST = SERVICE_HOST,
		IMAGE_HOST = SERVICE_HOST,
		CONTENT_HOST = SERVICE_HOST,
		PLACE_HOST = SERVICE_HOST,
	} = process.env

module.exports = {
	trap: {
		name: 'trap',
		connector: 'mongodb',
		url: 'mongodb://' + (DB_USERNAME ? `${DB_USERNAME}:${encodeURIComponent(DB_PASSWORD)}@` : '') + `${DB_HOST}/trap?authSource=${DB_AUTH}` + (DB_SET ? `&replicaSet=${DB_SET}&readPreference=primaryPreferred&slaveOk=true` : '') + '&useNewUrlParser=true&useUnifiedTopology=true&keepAlive=false&maxPoolSize=20&socketTimeoutMS=360000&connectTimeoutMS=10000&maxIdleTimeMS=300000',
		allowExtendedOperators: true,
		enableOptimisedfindOrCreate: true,
	},
	offerRemote: {
		name: 'offerRemote',
		connector: 'remote',
		url: `http://${OFFER_HOST}:3125/api`,
	},
	businessRemote: {
		name: 'businessRemote',
		connector: 'remote',
		url: `https://business.test.crm.waveo.com/api`,
	},
	salesRemote: {
		name: 'salesRemote',
		connector: 'remote',
		url: `https://sales.test.crm.waveo.com/api`,
	},
	personRemote: {
		name: 'personRemote',
		connector: 'remote',
		url: `https://person.test.crm.waveo.com/api`,
	},
	membershipRemote: {
		name: 'membershipRemote',
		connector: 'remote',
		url: `https://membership.test.crm.waveo.com/api`,
	},
	imageRemote: {
		name: 'imageRemote',
		connector: 'remote',
		url: `https://image.test.crm.waveo.com/api`,
	},
	contentRemote: {
		name: 'contentRemote',
		connector: 'remote',
		url: `http://${CONTENT_HOST}:3114/api`,
	},
	placeRemote: {
		name: 'placeRemote',
		connector: 'remote',
		url: `https://place.test.crm.waveo.com/api`,
	},
	reservationRemote: {
		name: 'reservationRemote',
		connector: 'rest',
		options: {
			headers: {
				accepts: 'application/json',
				'content-type': 'application/json',
			},
		},
		operations: [
		]
	},
	productLookup: {
		name: 'productLookup',
		connector: 'rest',
		options: {
			headers: {
				accepts: 'application/json',
				'content-type': 'application/json',
			},
		},
		operations: [
			{
				functions: {
					lookup: [
						{
							name: 'upc',
							source: 'path',
						},
					],
				},
				template: {
					method: 'GET',
					url: 'https://api.upcitemdb.com/prod/trial/lookup',
					query: {
						upc: '{upc}',
					},
					responsePath: '$.items[0]',
				},
			},
		],
	},
}
