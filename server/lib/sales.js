/**
 * @module Service:Sales
 */
const { Settings } = require('@crm/types'),
	{ Context } = require('@perkd/multitenant-context'),
	Service = appRequire('lib/common/Service')

const { FULFILLMENT } = Settings.Name,
	// Mapped (local) events  (see mapping in eventbus.json)
	EVENT = {
		FULFILLMENT: {	// store, pickup, deliver, vending
			REQUESTED: 'fulfillment.requested',
			PREPARE: 'fulfillment.prepare',
			PACKED: 'fulfillment.packed',
			ALLOCATED: 'fulfillment.allocated',
			COLLECTED: 'fulfillment.collected',
			DELIVERED: 'fulfillment.delivered',
		},
	}

class SalesService extends Service {

	async _ready() {
		const { app } = this,
			{ models, Event } = app,
			{ Order, Kitchen, Place, Booking } = models,
			tenants = app.allTenantCodes()

		// Handle Receipt Order events		// FIXME local events different from remote events
		app.on(Event.sales.order.paid, data => {
			Order.handleReceiptOrderEvents({ data, name: Event.sales.order.paid })
		})
		app.on(Event.sales.order.fulfilled, data => {
			Order.handleReceiptOrderEvents({ data, name: Event.sales.order.fulfilled })
		})
		app.on(Event.sales.order.cancelled, data => {
			Order.handleReceiptOrderEvents({ data, name: Event.sales.order.cancelled })
		})
		app.on(Event.sales.order.refunded, data => {
			Order.handleReceiptOrderEvents({ data, name: Event.sales.order.refunded })
		})

		// Handle Receipt Fulfillment (mapped local) events
		app.on(Event.sales.fulfillment.created, data => {
			Order.handleReceiptFulfillEvents({ data, name: Event.sales.fulfillment.created })
		})
		app.on(EVENT.FULFILLMENT.REQUESTED, evt => {
			Order.handleReceiptFulfillEvents(evt)
		})
		app.on(EVENT.FULFILLMENT.PREPARE, evt => {
			Order.handleReceiptFulfillEvents(evt)
		})
		app.on(EVENT.FULFILLMENT.PACKED, evt => {
			Order.handleReceiptFulfillEvents(evt)
		})

		// update Order progress (step) on fulfillment updates
		app.on(EVENT.FULFILLMENT.PACKED, ({ data }) => {
			Order.fulfillmentPacked(data)
				.catch(err => appNotify('[Order]fulfillmentPacked', { err, data }))
		})
		app.on(EVENT.FULFILLMENT.ALLOCATED, ({ data }) => {
			Order.fulfillmentAllocated(data)
				.catch(err => appNotify('[Order]fulfillmentAllocated', { err, data }))
		})
		app.on(EVENT.FULFILLMENT.COLLECTED, ({ data }) => {
			Order.fulfillmentCollected(data)
				.catch(err => appNotify('[Order]fulfillmentCollected', { err, data }))
		})
		app.on(EVENT.FULFILLMENT.DELIVERED, ({ data }) => {
			Order.fulfillmentDelivered(data)
				.catch(err => appNotify('[Order]fulfillmentDelivered', { err, data }))
		})

		// Handle Person Visit events
		app.on(Event.person.visit.arrive, ({ data }) => {
			Booking.customerArrived(data)
		})

		// Handle Queuing processed events
		app.on(Event.product.queuing.processed, ({ data }) => {
			Booking.queuingProcessed(data)
		})

		// Kitchen timer & autoPack
		for (const tenant of tenants) {
			const NOW = new Date()

			Context.runAsTenant(tenant, async () => {
				const { kitchen = {} } = app.getSettings(FULFILLMENT),
					{ enabled, autoPack } = kitchen

				if (enabled) {
					Kitchen.preparationTimer()

					await Promise.all([
						packPast(NOW, autoPack),
						schedulePack(NOW, autoPack),
					])
				}
			}, app.connectionManager)
		}

		/**
		 * Schedule packing of fulfillments that are ready to pack
		 * @param {Date} now
		 * @param {boolean} auto
		 */
		async function schedulePack(now, auto) {
			try {
				const fulfills = await Kitchen.findToSchedulePack(now)

				for (const fulfill of fulfills) {
					const { placeId = null } = fulfill,
						store = await Place.findById(placeId),
						{ kitchen = {} } = store?.getSettingsByName(FULFILLMENT) ?? {},
						{ autoPack = auto } = kitchen

					if (autoPack) {
						fulfill.schedulePack(now)
					}
				}
			}
			catch (err) {
				appNotify('[Sales] schedulePack', { err }, 'error', '-kitchen')
			}
		}

		/**
		 * Pack past fulfillments
		 * @param {Date} now
		 * @param {boolean} auto
		 */
		async function packPast(now, auto) {
			try {
				const fulfills = await Kitchen.findToPack(now)

				for (const fulfill of fulfills) {
					const { placeId = null } = fulfill,
						store = await Place.findById(placeId),
						{ kitchen = {} } = store?.getSettingsByName(FULFILLMENT) ?? {},
						{ autoPack = auto } = kitchen

					if (autoPack) {
						fulfill.packed()
					}
				}
			}
			catch (err) {
				appNotify('[Sales] packPast', { err }, 'error', '-kitchen')
			}
		}
	}
}

module.exports = exports = SalesService
