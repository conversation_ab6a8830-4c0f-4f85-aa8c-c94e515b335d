/**
 * @module Service:Product
 */
const { Providers, Settings } = require('@crm/types'),
	{ Context } = require('@perkd/multitenant-context'),
	Service = appRequire('lib/common/Service')

const { GRABFOOD, GRABMART } = Providers.PROVIDER,
	{ PROVIDER } = Settings.Name

class ProductService extends Service {

	async _ready() {
		const { app } = this,
			{ models, Event, connectionManager } = app,
			{ Variant, Resource, Grab, Queue, Product } = models,
			tenants = app.allTenantCodes()

		// booking events
		Product.widgetEventsHandler()

		// Handle Payment Event
		app.on(Event.payment.transaction.event, ({ data }) => Variant.handlePaymentEvent(data))

		// Handle Booking Events
		app.on(Event.sales.booking.reservation.confirmed, Resource.handleBookingConfirmedEvent) // local event
		app.on(Event.sales.booking.reservation.cancelled, ({ data }) => Resource.handleBookingCancelledEvent(data))

		app.on(Event.sales.booking.ended, ({ data }) => Queue.handleBookingStatusChanged(data))
		app.on(Event.sales.booking.noshow, ({ data }) => Queue.handleBookingStatusChanged(data))
		app.on(Event.sales.booking.deleted, ({ data }) => Queue.handleBookingDeleted(data))

		// Refresh Grab menus once for (enabled) tenants
		for (const tenant of tenants) {
			Context.runAsTenant(tenant, () => {
				const enabled = app.getSettings(PROVIDER)

				if (enabled[GRABFOOD]) {
					Grab.menuRefreshTimer(GRABFOOD, tenant)
				}
				if (enabled[GRABMART]) {
					Grab.menuRefreshTimer(GRABMART, tenant)
				}
			}, connectionManager)
		}
	}
}

module.exports = exports = ProductService
