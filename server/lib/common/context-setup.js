/**
 * @module Context Setup
 * Context debugging utilities - CONTEXT_MODE complexity removed
 * Service context is now established at startup in server.js
 */

const debug = require('debug')('multitenant:setup')
const { Context } = require('@perkd/multitenant-context')

debug('Context setup loaded - service context established in server.js')

/**
 * Add memory debugging endpoints for Context metrics and anonymous models
 * @param {Object} app - Express/LoopBack application instance
 */
function addMemoryDebuggingEndpoints(app) {
	// Context metrics and memory debugging endpoint
	app.get('/debug/context-metrics', (req, res) => {
		try {
			const currentContext = Context.getCurrentContext()
			const contextMetrics = getContextMetrics()
			const memoryUsage = process.memoryUsage()
			const anonymousModels = getAnonymousModelStats(app)
			const tokenCacheStats = getTokenCacheStats()

			res.json({
				timestamp: new Date().toISOString(),
				currentContext: {
					exists: !!currentContext,
					tenant: Context.tenant || null,
					user: Context.user || null,
					timezone: Context.timezone || null,
					accessToken: Context.accessToken ? '[REDACTED]' : null
				},
				contextMetrics,
				memoryUsage: {
					...memoryUsage,
					heapUsedMB: Math.round(memoryUsage.heapUsed / 1024 / 1024 * 100) / 100,
					heapTotalMB: Math.round(memoryUsage.heapTotal / 1024 / 1024 * 100) / 100,
					externalMB: Math.round(memoryUsage.external / 1024 / 1024 * 100) / 100,
					rssMB: Math.round(memoryUsage.rss / 1024 / 1024 * 100) / 100
				},
				anonymousModels,
				tokenCacheStats,
				registryStats: getRegistryStats(app),
				uptime: process.uptime()
			})
		}
		catch (error) {
			res.status(500).json({
				error: 'Failed to get context metrics',
				message: error.message,
				timestamp: new Date().toISOString()
			})
		}
	})

	// Detailed context debugging endpoint
	app.get('/debug/context-detailed', (req, res) => {
		try {
			const currentContext = Context.getCurrentContext()
			const connectionManagerStats = getConnectionManagerStats(app)
			const datasourceStats = getDatasourceStats(app)

			res.json({
				timestamp: new Date().toISOString(),
				context: {
					current: currentContext ? sanitizeContext(currentContext) : null,
					tenant: Context.tenant,
					user: Context.user,
					timezone: Context.timezone
				},
				connectionManager: connectionManagerStats,
				datasources: datasourceStats,
				memoryDetails: {
					process: process.memoryUsage(),
					gc: getGCStats(),
					eventLoop: getEventLoopStats()
				}
			})
		}
		catch (error) {
			res.status(500).json({
				error: 'Failed to get detailed context info',
				message: error.message,
				timestamp: new Date().toISOString()
			})
		}
	})

	// Memory cleanup endpoint (for testing)
	app.post('/debug/context-cleanup', (req, res) => {
		try {
			const beforeMemory = process.memoryUsage()

			// Force garbage collection if available
			if (global.gc) {
				global.gc()
			}

			// Clear token cache if accessible
			const tokenCacheResult = clearTokenCacheIfPossible()

			// Clean up all connections if available
			let connectionCleanupResult = { attempted: false }
			if (app.cleanupAllConnections && typeof app.cleanupAllConnections === 'function') {
				try {
					app.cleanupAllConnections()
					connectionCleanupResult = { attempted: true, success: true }
				}
				catch (cleanupErr) {
					connectionCleanupResult = {
						attempted: true,
						success: false,
						error: cleanupErr.message
					}
				}
			}

			// Force another GC after cleanup
			if (global.gc) {
				global.gc()
			}

			const afterMemory = process.memoryUsage()

			res.json({
				timestamp: new Date().toISOString(),
				message: 'Memory cleanup attempted',
				memoryBefore: beforeMemory,
				memoryAfter: afterMemory,
				heapFreed: beforeMemory.heapUsed - afterMemory.heapUsed,
				gcAvailable: !!global.gc,
				tokenCache: tokenCacheResult,
				connectionCleanup: connectionCleanupResult
			})
		}
		catch (error) {
			res.status(500).json({
				error: 'Failed to perform cleanup',
				message: error.message,
				timestamp: new Date().toISOString()
			})
		}
	})

	// 🔒 SECURITY: Real-time context monitoring endpoint for debugging tenant context issues
	app.get('/debug/context-realtime', (req, res) => {
		try {
			const currentContext = Context.getCurrentContext()
			const tenant = Context.tenant

			res.json({
				timestamp: new Date().toISOString(),
				nodeEnv: process.env.NODE_ENV,
				context: {
					exists: !!currentContext,
					tenant: tenant,
					isTrap: tenant === 'trap',
					user: Context.user?.id || null,
					timezone: Context.timezone
				},
				warning: tenant === 'trap' ? 'TRAP CONTEXT DETECTED - INVESTIGATE IMMEDIATELY' : null,
				security: {
					isProduction: process.env.NODE_ENV === 'production',
					serviceContextEstablished: true
				},
				requestInfo: {
					path: req.path,
					method: req.method,
					headers: {
						authorization: req.headers.authorization ? '[PRESENT]' : '[MISSING]',
						'tenant-code': req.headers['tenant-code'] || '[MISSING]'
					}
				}
			})
		}
		catch (error) {
			res.status(500).json({
				error: 'Failed to get context realtime info',
				message: error.message,
				timestamp: new Date().toISOString()
			})
		}
	})

	debug('Context monitoring endpoints registered')
}

/**
 * Helper functions for memory debugging
 */

/**
 * Get Context metrics if available
 */
function getContextMetrics() {
	try {
		const currentContext = Context.getCurrentContext()

		// Try to access context metrics if they exist
		const metrics = currentContext?.metrics || {}

		return {
			available: !!currentContext,
			metricsExists: !!currentContext?.metrics,
			connectionCount: metrics.connectionCount || 0,
			requestCount: metrics.requestCount || 0,
			errorCount: metrics.errorCount || 0,
			lastActivity: metrics.lastActivity || null,
			contextProperties: currentContext ? Object.keys(currentContext).filter(key => !key.startsWith('_')) : []
		}
	}
	catch (error) {
		return {
			available: false,
			error: error.message
		}
	}
}

/**
 * Get ModelRegistry statistics
 */
function getRegistryStats(app) {
	try {
		// Try multiple approaches to access ModelRegistry.getStats()

		// Approach 1: Access the ModelRegistry directly from the module
		const modelRegistryModule = require('loopback-datasource-juggler/lib/model-registry')

		// Debug: Log what we found in the module
		debug('ModelRegistry module keys:', Object.keys(modelRegistryModule || {}))

		// The module exports an object with ModelRegistry property
		if (modelRegistryModule && modelRegistryModule.ModelRegistry && typeof modelRegistryModule.ModelRegistry.getStats === 'function') {
			const stats = modelRegistryModule.ModelRegistry.getStats()
			return {
				available: true,
				stats,
				source: 'ModelRegistry Module'
			}
		}

		// Approach 2: Try to access via datasource modelBuilder
		if (app.dataSources && app.dataSources.db && app.dataSources.db.modelBuilder) {
			const modelBuilder = app.dataSources.db.modelBuilder
			if (typeof modelBuilder.getStats === 'function') {
				const stats = modelBuilder.getStats()
				return {
					available: true,
					stats,
					source: 'DataSource ModelBuilder'
				}
			}
		}

		// Approach 3: Try to find registry through app.registry.modelBuilder
		if (app.registry && app.registry.modelBuilder && typeof app.registry.modelBuilder.getStats === 'function') {
			const stats = app.registry.modelBuilder.getStats()
			return {
				available: true,
				stats,
				source: 'App Registry ModelBuilder'
			}
		}

		// Fallback: create our own stats from available registry data
		const models = app.registry?.modelBuilder?.models || {}
		const modelNames = Object.keys(models)

		// Get additional info from datasources
		const datasources = app.dataSources ? Object.keys(app.dataSources) : []
		const appModels = app.models ? Object.keys(app.models) : []

		return {
			available: true,
			stats: {
				totalModels: modelNames.length,
				modelNames: modelNames.slice(0, 20), // Limit for readability
				totalModelNames: modelNames.length,
				appModels: appModels.length,
				datasources: datasources.length,
				datasourceNames: datasources,
				registryType: 'LoopBack Registry (Custom Stats)',
				note: 'ModelRegistry.getStats() not accessible - using custom implementation'
			},
			source: 'Custom Implementation'
		}
	}
	catch (error) {
		// Final fallback: create basic stats from what we can access
		try {
			const appModels = app.models ? Object.keys(app.models) : []
			const datasources = app.dataSources ? Object.keys(app.dataSources) : []

			return {
				available: true,
				stats: {
					appModels: appModels.length,
					appModelNames: appModels.slice(0, 10),
					datasources: datasources.length,
					datasourceNames: datasources,
					registryType: 'Basic App Stats',
					error: error.message
				},
				source: 'Final Fallback'
			}
		}
		catch (fallbackError) {
			return {
				available: false,
				error: `Failed to get registry stats: ${error.message}. Fallback also failed: ${fallbackError.message}`,
				source: 'Error'
			}
		}
	}
}

/**
 * Get anonymous model statistics
 */
function getAnonymousModelStats(app) {
	try {
		const models = app.registry.modelBuilder.models || {}
		const modelNames = Object.keys(models)
		const anonymousModels = []
		const modelStats = {
			total: modelNames.length,
			anonymous: 0,
			persistent: 0,
		}

		modelNames.forEach(name => {
			const model = models[name]
			if (model) {
				// Check if model is anonymous (generated dynamically)
				const isAnonymous = !model.name || model.name.match('AnonymousModel')
				const datasourceName = model.dataSource?.name || 'unknown'

				if (isAnonymous) {
					modelStats.anonymous++
					anonymousModels.push({
						name,
						datasource: datasourceName,
						base: model.definition?.base || 'unknown'
					})
				}

				modelStats.anonymous = anonymousModels.length
				modelStats.persistent = modelStats.total - modelStats.anonymous
			}
		})

		return {
			...modelStats,
			anonymousModels: anonymousModels.slice(0, 20), // Limit to first 20 for readability
			totalAnonymousModels: anonymousModels.length
		}
	}
	catch (error) {
		return {
			error: error.message,
			total: 0,
			anonymous: 0
		}
	}
}

/**
 * Get token cache statistics from multitenant middleware
 */
function getTokenCacheStats() {
	try {
		// Import the cache stats function from multitenant middleware
		const multitenantMiddleware = require('./middleware/multitenant')
		if (typeof multitenantMiddleware.getTokenCacheStats === 'function') {
			return {
				available: true,
				...multitenantMiddleware.getTokenCacheStats()
			}
		}
		return {
			available: false,
			note: 'Token cache stats function not available',
			suggestion: 'Check multitenant middleware implementation'
		}
	}
	catch (error) {
		return {
			available: false,
			error: error.message,
			note: 'Failed to access token cache statistics'
		}
	}
}

/**
 * Get connection manager statistics
 */
function getConnectionManagerStats(app) {
	try {
		const connectionManager = app.connectionManager
		if (!connectionManager) {
			return { available: false, reason: 'No connection manager found' }
		}

		return {
			available: true,
			isInitialized: connectionManager.isInitialized || false,
			hasGetAllConnections: typeof connectionManager.getAllConnections === 'function',
			hasGetMetrics: typeof connectionManager.getMetrics === 'function',
			methods: Object.getOwnPropertyNames(Object.getPrototypeOf(connectionManager))
				.filter(name => typeof connectionManager[name] === 'function')
		}
	}
	catch (error) {
		return {
			available: false,
			error: error.message
		}
	}
}

/**
 * Get datasource statistics
 */
function getDatasourceStats(app) {
	try {
		const datasources = app.datasources || {}
		const stats = {}

		Object.keys(datasources).forEach(name => {
			const ds = datasources[name]
			stats[name] = {
				connector: ds.connector?.name || 'unknown',
				connected: ds.connected || false,
				connecting: ds.connecting || false,
				settings: {
					host: ds.settings?.host || 'N/A',
					port: ds.settings?.port || 'N/A',
					database: ds.settings?.database || ds.settings?.url ? '[URL]' : 'N/A'
				}
			}
		})

		return {
			available: true,
			count: Object.keys(stats).length,
			datasources: stats
		}
	}
	catch (error) {
		return {
			available: false,
			error: error.message
		}
	}
}

/**
 * Sanitize context object for safe JSON serialization
 */
function sanitizeContext(context) {
	try {
		const sanitized = {}
		for (const key in context) {
			if (key.startsWith('_') || typeof context[key] === 'function') {
				continue // Skip private properties and functions
			}
			if (key === 'domain' || key === '_domain') {
				sanitized[key] = '[Domain Object]'
			}
			else if (typeof context[key] === 'object' && context[key] !== null) {
				sanitized[key] = '[Object]'
			}
			else {
				sanitized[key] = context[key]
			}
		}
		return sanitized
	}
	catch (error) {
		return { error: 'Failed to sanitize context', message: error.message }
	}
}

/**
 * Get garbage collection statistics
 */
function getGCStats() {
	try {
		if (typeof global.gc === 'function') {
			return {
				available: true,
				note: 'GC function is available (--expose-gc flag used)'
			}
		}
		return {
			available: false,
			note: 'GC function not available (use --expose-gc flag to enable)'
		}
	}
	catch (error) {
		return {
			available: false,
			error: error.message
		}
	}
}

/**
 * Get event loop statistics
 */
function getEventLoopStats() {
	try {
		// Basic event loop information
		return {
			uptime: process.uptime(),
			activeHandles: process._getActiveHandles ? process._getActiveHandles().length : 'N/A',
			activeRequests: process._getActiveRequests ? process._getActiveRequests().length : 'N/A',
			note: 'Limited event loop stats available without additional modules'
		}
	}
	catch (error) {
		return {
			error: error.message,
			available: false
		}
	}
}

/**
 * Clear token cache if possible
 */
function clearTokenCacheIfPossible() {
	try {
		// Access the token cache from the multitenant middleware
		const multitenantMiddleware = require('./middleware/multitenant')
		if (typeof multitenantMiddleware.clearTokenCache === 'function') {
			const cleared = multitenantMiddleware.clearTokenCache()
			debug(`Cleared ${cleared} token cache entries`)
			return {
				attempted: true,
				success: true,
				entriesCleared: cleared,
				message: `Successfully cleared ${cleared} token cache entries`
			}
		}
		else {
			debug('Token cache clear function not available')
			return {
				attempted: true,
				success: false,
				message: 'Token cache clear function not available'
			}
		}
	}
	catch (error) {
		debug('Failed to clear token cache:', error.message)
		return {
			attempted: true,
			success: false,
			message: `Failed to clear token cache: ${error.message}`
		}
	}
}

/**
 * Add advanced memory profiling endpoints for deep memory leak investigation
 * @param {Object} app - Express/LoopBack application instance
 */
function addAdvancedMemoryProfiling(app) {
	// Memory snapshot endpoint for heap analysis
	app.get('/debug/memory-snapshot', (req, res) => {
		try {
			const memoryUsage = process.memoryUsage()
			const eventBusStats = getEventBusMemoryStats(app)
			const mongoConnectionStats = getMongoConnectionStats(app)
			const contextMetricsStats = getContextMetricsStats()
			const promiseStats = getPromiseStats()

			res.json({
				timestamp: new Date().toISOString(),
				memoryUsage: {
					...memoryUsage,
					heapUsedMB: Math.round(memoryUsage.heapUsed / 1024 / 1024 * 100) / 100,
					heapTotalMB: Math.round(memoryUsage.heapTotal / 1024 / 1024 * 100) / 100,
					externalMB: Math.round(memoryUsage.external / 1024 / 1024 * 100) / 100,
					rssMB: Math.round(memoryUsage.rss / 1024 / 1024 * 100) / 100
				},
				eventBus: eventBusStats,
				mongoConnections: mongoConnectionStats,
				contextMetrics: contextMetricsStats,
				promises: promiseStats,
				gcStats: getGCStats(),
				uptime: process.uptime()
			})
		}
		catch (error) {
			res.status(500).json({
				error: 'Failed to generate memory snapshot',
				message: error.message,
				timestamp: new Date().toISOString()
			})
		}
	})

	// Aggressive memory cleanup endpoint for load testing
	app.post('/debug/aggressive-cleanup', async (req, res) => {
		try {
			const beforeMemory = process.memoryUsage()
			const cleanupResults = {}

			// 1. Clear token cache
			cleanupResults.tokenCache = clearTokenCacheIfPossible()

			// 2. Clean up EventBus listeners
			cleanupResults.eventBus = cleanupEventBusListeners(app)

			// 3. Clean up MongoDB connections and cursors
			cleanupResults.mongoConnections = await cleanupMongoConnections(app)

			// 4. Clean up context metrics
			cleanupResults.contextMetrics = cleanupContextMetrics()

			// 4.5. Clean up context metrics via app method
			if (app.cleanupContextMetrics && typeof app.cleanupContextMetrics === 'function') {
				try {
					app.cleanupContextMetrics()
					cleanupResults.appContextMetrics = { attempted: true, success: true }
				}
				catch (err) {
					cleanupResults.appContextMetrics = { attempted: true, success: false, error: err.message }
				}
			}

			// 5. Clean up all connections
			if (app.cleanupAllConnections && typeof app.cleanupAllConnections === 'function') {
				try {
					await app.cleanupAllConnections()
					cleanupResults.connections = { attempted: true, success: true }
				}
				catch (cleanupErr) {
					cleanupResults.connections = {
						attempted: true,
						success: false,
						error: cleanupErr.message
					}
				}
			}

			// 6. Force multiple garbage collections
			if (global.gc) {
				global.gc()
				// Wait a bit and force another GC
				setTimeout(() => global.gc(), 100)
				cleanupResults.gcForced = true
			}

			const afterMemory = process.memoryUsage()

			res.json({
				timestamp: new Date().toISOString(),
				message: 'Aggressive memory cleanup completed',
				memoryBefore: beforeMemory,
				memoryAfter: afterMemory,
				heapFreed: beforeMemory.heapUsed - afterMemory.heapUsed,
				cleanupResults
			})
		}
		catch (error) {
			res.status(500).json({
				error: 'Failed to perform aggressive cleanup',
				message: error.message,
				timestamp: new Date().toISOString()
			})
		}
	})
}

/**
 * Get EventBus memory statistics
 */
function getEventBusMemoryStats(app) {
	try {
		const eventBus = app.Service?.eventbus
		if (!eventBus || !eventBus.subEventsListeners) {
			return { available: false, reason: 'EventBus not found or no listeners' }
		}

		const listeners = eventBus.subEventsListeners
		let totalListeners = 0
		let totalTenants = 0
		const eventStats = {}

		for (const eventName in listeners) {
			const tenantListeners = listeners[eventName]
			let eventListenerCount = 0
			let eventTenantCount = 0

			for (const tenant in tenantListeners) {
				eventTenantCount++
				totalTenants++
				const listenerArray = tenantListeners[tenant]
				if (Array.isArray(listenerArray)) {
					eventListenerCount += listenerArray.length
					totalListeners += listenerArray.length
				}
			}

			eventStats[eventName] = {
				tenants: eventTenantCount,
				listeners: eventListenerCount
			}
		}

		return {
			available: true,
			totalEvents: Object.keys(listeners).length,
			totalListeners,
			totalTenants,
			eventBreakdown: eventStats
		}
	}
	catch (error) {
		return {
			available: false,
			error: error.message
		}
	}
}

/**
 * Get MongoDB connection statistics
 */
function getMongoConnectionStats(app) {
	try {
		const connectionManager = app.connectionManager
		if (!connectionManager) {
			return { available: false, reason: 'No connection manager found' }
		}

		const stats = {
			available: true,
			connections: [],
			totalConnections: 0,
			activeSessions: 0,
			openCursors: 0
		}

		// Try to get connections using different methods
		let connections = []
		if (typeof connectionManager.getAllConnections === 'function') {
			connections = connectionManager.getAllConnections() || []
		}
		else if (connectionManager._connections || connectionManager.connections) {
			const connectionsObj = connectionManager._connections || connectionManager.connections || {}
			connections = Object.values(connectionsObj).filter(Boolean)
		}

		stats.totalConnections = connections.length

		for (const connection of connections) {
			if (connection && connection.name !== 'TRAP') {
				const client = connection?.connector?.dataSource?.connector?.client
				let sessionCount = 0
				let cursorCount = 0

				if (client) {
					// Count active sessions
					if (client.topology && client.topology.sessions) {
						sessionCount = client.topology.sessions.size || 0
					}

					// Count open cursors (if accessible)
					if (client.topology && client.topology.s && client.topology.s.servers) {
						// This is an approximation - actual cursor counting is complex
						cursorCount = 0 // Would need deeper MongoDB driver inspection
					}
				}

				stats.connections.push({
					name: connection.name,
					connected: connection.connected || false,
					sessions: sessionCount,
					cursors: cursorCount
				})

				stats.activeSessions += sessionCount
				stats.openCursors += cursorCount
			}
		}

		return stats
	}
	catch (error) {
		return {
			available: false,
			error: error.message
		}
	}
}

/**
 * Get context metrics statistics
 */
function getContextMetricsStats() {
	try {
		const currentContext = Context.getCurrentContext()

		return {
			available: !!currentContext,
			hasMetrics: !!(currentContext && currentContext.metrics),
			metricsSize: currentContext && currentContext.metrics ?
				Object.keys(currentContext.metrics).length : 0,
			contextProperties: currentContext ?
				Object.keys(currentContext).filter(key => !key.startsWith('_')).length : 0
		}
	}
	catch (error) {
		return {
			available: false,
			error: error.message
		}
	}
}

/**
 * Get Promise statistics (approximation)
 */
function getPromiseStats() {
	try {
		// This is a rough approximation - exact promise counting is not easily accessible
		return {
			available: true,
			note: 'Promise statistics are approximations',
			processTicksAndRejections: process._getActiveHandles().length,
			activeHandles: process._getActiveHandles().length,
			activeRequests: process._getActiveRequests().length
		}
	}
	catch (error) {
		return {
			available: false,
			error: error.message
		}
	}
}

/**
 * Clean up EventBus listeners
 */
function cleanupEventBusListeners(app) {
	try {
		const eventBus = app.Service?.eventbus
		if (!eventBus || !eventBus.subEventsListeners) {
			return { attempted: false, reason: 'EventBus not found' }
		}

		const beforeCount = Object.keys(eventBus.subEventsListeners).length

		// Clear all event listeners
		eventBus.subEventsListeners = {}

		return {
			attempted: true,
			success: true,
			eventsCleared: beforeCount
		}
	}
	catch (error) {
		return {
			attempted: true,
			success: false,
			error: error.message
		}
	}
}

/**
 * Clean up MongoDB connections and cursors
 */
async function cleanupMongoConnections(app) {
	try {
		const connectionManager = app.connectionManager
		if (!connectionManager) {
			return { attempted: false, reason: 'No connection manager found' }
		}

		let connections = []
		if (typeof connectionManager.getAllConnections === 'function') {
			connections = connectionManager.getAllConnections() || []
		}
		else if (connectionManager._connections || connectionManager.connections) {
			const connectionsObj = connectionManager._connections || connectionManager.connections || {}
			connections = Object.values(connectionsObj).filter(Boolean)
		}

		let sessionsEnded = 0
		let cursorsCleared = 0

		for (const connection of connections) {
			if (connection && connection.name !== 'TRAP') {
				const client = connection?.connector?.dataSource?.connector?.client

				if (client && client.topology && client.topology.sessions) {
					const sessions = Array.from(client.topology.sessions.values())
					for (const session of sessions) {
						try {
							if (session && session.endSession) {
								await session.endSession()
								sessionsEnded++
							}
						}
						catch (sessionErr) {
							debug('Error ending session:', sessionErr)
						}
					}
				}
			}
		}

		return {
			attempted: true,
			success: true,
			connectionsProcessed: connections.length,
			sessionsEnded,
			cursorsCleared
		}
	}
	catch (error) {
		return {
			attempted: true,
			success: false,
			error: error.message
		}
	}
}

/**
 * Clean up context metrics
 */
function cleanupContextMetrics() {
	try {
		const currentContext = Context.getCurrentContext()

		if (currentContext && currentContext.metrics) {
			const beforeSize = Object.keys(currentContext.metrics).length
			currentContext.metrics = {}

			return {
				attempted: true,
				success: true,
				metricsCleared: beforeSize
			}
		}

		return {
			attempted: true,
			success: true,
			metricsCleared: 0,
			note: 'No metrics found to clear'
		}
	}
	catch (error) {
		return {
			attempted: true,
			success: false,
			error: error.message
		}
	}
}

// Export a setup function for application startup
module.exports = function setupContext(app) {
	// Log the context mode at startup
	app.on('started', () => {
		debug('Application started with STRICT context mode')
	})

	// Add a health check route to verify service context
	app.get('/health/context-mode', (req, res) => {
		res.json({
			serviceContextEstablished: true,
			currentTenant: 'service', // Static value since we know service context is established
			time: new Date().toISOString()
		})
	})

	// Add memory debugging endpoints for Context metrics and anonymous models
	addMemoryDebuggingEndpoints(app)

	// Add advanced memory profiling endpoints
	addAdvancedMemoryProfiling(app)

	return app
}

// No longer needed - context mode complexity removed