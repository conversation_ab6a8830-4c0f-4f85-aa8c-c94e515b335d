/*
 * Perkd Pte Ltd
 */

//=============================================================================
// IMPORTS AND CONSTANTS
//=============================================================================

require('dotenv').config()

// 🔒 CRITICAL FIX: Establish service context IMMEDIATELY at startup
// This prevents ANY trap contexts from being created during initialization
const { Context, TENANT, SERVICE } = require('@perkd/multitenant-context')

// Create and set service context as the baseline for entire application
const serviceContext = Context.createContext({ [TENANT]: SERVICE })
Context.runInContext(serviceContext, () => {
	// Set service context values immediately
	Context.setValues(SERVICE, { id: 'service-bootstrap' }, 'UTC')
	console.log('🔒 Service context established at startup:', Context.tenant)

	// Now load all other modules within the service context
	const fs = require('node:fs')
	const https = require('node:https')
	const path = require('node:path')
	const debug = require('debug')('waveo:server')
	const loopback = require('loopback')
	const boot = require('loopback-boot')
const { merge } = require('@perkd/utils')
const { MESSAGE } = require('@perkd/tenants')
const { PERKD, CRM } = require('@crm/types').Touchpoints.Type
const TenantCleanupOrchestrator = require('./tenant/cleanup-orchestrator')

// Server timeout constants
const TIMEOUT = 90000    // 90 seconds
const KEEPALIVE = 65000
const HEADERS = 70000
const _start = Date.now()

const CRM_REGION = 'ap-southeast-1'

// Create the application

// Apply context setup before creating the app
const contextSetup = require('./context-setup')
const app = module.exports = loopback()

// Apply context setup to the app (this registers the health check routes)
contextSetup(app)

// Load colors for console output
require('colors')

// Load global functions
require('./global')(app)

//=============================================================================
// EVENT DEFINITIONS
//=============================================================================

const Event = { // IMPORTANT: Event is extended by various modules (in their constructor) incrementally
	service: {
		REGISTER: 'service.register',
		UP: 'service.up',
		READY: 'service.ready',
		STARTED: 'service.started',
		PAUSED: 'service.paused',
		STOPPED: 'service.stopped',
		DOWN: 'service.down',
	},
	eventbus: {
		STARTED: 'eventbus.started',
		STOPPED: 'eventbus.stopped',
	}
}

//=============================================================================
// CONFIGURATION MANAGEMENT
//=============================================================================

/**
 * Loads and validates application configuration
 * @returns {Object} The application configuration
 */
function getAppConfig() {
	try {
		const { loadAppConfig } = appRequire('lib/common/config-loader')
		const config = merge(loadAppConfig(__dirname), loadAppConfig(appPath()))
		const { service, modules } = config
		const settings = {}

		// Validate required configuration
		if (!service || !service.name) {
			throw new Error('Missing required service configuration (name)')
		}

		// Load service settings
		if (service.settings?.length > 0) {
			Object.assign(settings, appSettings(service.settings[0]))

			for (let i = 1; i < service.settings.length; i++) {
				settings[service.settings[i]] = appSettings(service.settings[i])
			}
		}
		service.settings = settings

		// Load module settings
		if (modules) {
			for (const name in modules) {
				if ({}.hasOwnProperty.call(modules, name)) {
					modules[name] = merge(modules[name], appSettings(name))
				}
			}
		}
		else {
			config.modules = {}
		}

		// Apply environment-specific overrides
		const env = process.env.NODE_ENV || 'development'
		const envConfig = appSettings(`${env}-config`)

		if (envConfig) {
			merge(config, envConfig)
		}

		debug('appConfig %j', config)
		return config
	}
	catch (err) {
		console.error('Failed to load application configuration:', err)
		throw err
	}
}

//=============================================================================
// APPLICATION LIFECYCLE MANAGEMENT
//=============================================================================

let mute = false, inited = false

/**
 * Initialize the application
 * @param {Object} appConfig - Application configuration
 * @param {boolean} silent - Whether to suppress console output
 * @returns {Object} The service object
 */
app.init = async function(appConfig, silent = false) {
	mute = silent

	if (inited) return app.service
	if (!appConfig) appConfig = getAppConfig()

	// --  Initialize Common Services  ----
	const { definitions } = appSettings('eventbus'),
		{ REGISTRY } = require(definitions),
		{ name: svcName, domain } = appConfig.service

	app.EventRegistry = REGISTRY
	app.Event = { ...app.EventRegistry[domain], ...Event }
	inited = true

	// --- setup Service lifecycle event handlers
	app.on(Event.service.UP, e => {
		app.start(e.service).then(async () => {
			// Handle service dependencies and transition to READY state
			// This replaces the functionality that was previously handled by CheckIn module
			await handleServiceDependencies(e.service)
		}).catch(err => {
			appEcho('Error in service UP handler:', err)
			appNotify('Service startup failed', err, 'error')
		})
	})

	// --- load Common Modules ---
	try {
		const { Service } = app.models,
			service = await Service.init(svcName, appConfig),
			{ config = {} } = service,
			{ modules = {} } = config,
			moduleNames = Object.keys(modules)

		debug('service inited %j', service)

		// 🔒 CRITICAL FIX: Assign service to app.service for middleware access
		app.service = service
		app.Service = {}

		// Initialize modules
		await initializeModules(moduleNames, modules, svcName, appConfig)

		// 🔥 CRITICAL: Initialize cleanup orchestrator for memory leak prevention
		const cleanupOrchestrator = new TenantCleanupOrchestrator(app)
		app.set('cleanupOrchestrator', cleanupOrchestrator)

		// Note: Graceful shutdown is handled by app.exit() and existing signal handlers
		appEcho('✅ TenantCleanupOrchestrator initialized')

		// Set up multi-tenancy event handlers if needed
		if (service.multitenancy) {
			setupMultiTenancyHandlers(moduleNames, modules)
		}

		debug('app.Event %j', app.Event)

		return service.setState(Service.State.UP)
	}
	catch (err) {
		appEcho('\n*** Critical Error: unable to initialize service: %s ***\n\n', svcName, err)
		appNotify('Unable to initialize service', err, 'error')
	}
}

/**
 * Handle service dependencies and transition to READY state
 * Replaces the functionality previously handled by CheckIn module
 * @param {Object} service - Service instance
 */
async function handleServiceDependencies(service) {
	const { dependencies = {} } = service
	const numDependencies = Object.keys(dependencies).length

	if (numDependencies === 0) {
		// No dependencies, can transition to READY immediately
		await service.ready()
	}
	else {
		// For future use: implement proper dependency checking logic
		appEcho('Service has %d dependencies, implementing dependency checking...', numDependencies)
		// TODO: Implement dependency checking logic when needed
		// For now, assume dependencies are satisfied and transition to READY
		await service.ready()
	}
}

/**
 * Initialize service modules
 * @param {string[]} moduleNames - Names of modules to initialize
 * @param {Object} modules - Module configurations
 * @param {string} svcName - Service name
 * @param {Object} appConfig - Application configuration
 */
async function initializeModules(moduleNames, modules, svcName, appConfig) {
	// Initialize common modules
	for (const name of moduleNames) {
		const settings = modules[name]

		if (typeof settings?.enabled === 'boolean') {
			debug('load module [%s], settings: %j', name, settings)
			const Module = appRequire('lib/common/modules/' + name)

			app.Service[name] = new Module(app, settings)

			if (typeof app.Service[name].init === 'function') {
				await app.Service[name].init()
			}

			app.Event = merge(app.Event, Module.Event || {})

			appEcho('[%s] initialized %s', name, settings.enabled ? '' : '(disabled)')
		}
	}

	// Initialize main service module
	const Main = appRequire('lib/' + svcName.toLowerCase())
	app.Service.main = new Main(app, app.service)
	app.Event = merge(app.Event, Main.Event || {})

	// Set up service ready handler
	app.on(Event.service.READY, async e => {
		// Use the passed appConfig instead of getting it again
		await handleServiceReady(e, moduleNames, modules, appConfig, svcName)
	})

	// Set up service started handler
	app.on(Event.service.STARTED, async () => {
		for (const name in modules) {
			if (modules[name].enabled) {
				if (typeof app.Service[name].start === 'function') {
					await app.Service[name].start()
				}
			}
		}
	})
}

/**
 * Handle service ready event
 * @param {Object} e - Event object
 * @param {string[]} moduleNames - Names of modules
 * @param {Object} modules - Module configurations
 * @param {Object} appConfig - Application configuration
 * @param {string} svcName - Service name
 */
async function handleServiceReady(e, moduleNames, modules, appConfig, svcName) {
	const NOW = new Date()
	debug('handleServiceReady for modules: %j', moduleNames)

	for (const name of moduleNames) {
		const settings = modules[name],
			module = app.Service[name]

		if (settings.enabled) {
			if (typeof module.ready === 'function') {
				await module.ready()
			}
			if (typeof module.eventNames === 'function') {
				const tenants = app.allTenantCodes(),
					events = module.eventNames(),
					emitter = module
				// ✅ PHASE 2: Use batchSubscribe with improved error handling
				if (events.length > 0) {
					const subscribePromises = tenants.map(tenant =>
						app.Service.eventbus.batchSubscribe(events, tenant, emitter)
					)

					// Use Promise.allSettled for better error handling
					const results = await Promise.allSettled(subscribePromises)

					// Log individual failures without stopping the service
					results.forEach((result, index) => {
						if (result.status === 'rejected') {
							const tenant = tenants[index]
							appEcho('⚠️ Failed to subscribe %s events for tenant %s: %s', name, tenant, result.reason?.message || result.reason)
							appNotify(`Event subscription failed for ${name}`, {
								module: name,
								tenant,
								events: events.length,
								error: result.reason?.message || result.reason
							}, 'warn')
						}
					})

					// Check if any subscriptions succeeded
					const successCount = results.filter(r => r.status === 'fulfilled').length
					const failureCount = results.filter(r => r.status === 'rejected').length

					if (successCount > 0) {
						appEcho('[%s] Event subscription: %d/%d tenants successful', name, successCount, tenants.length)
					}

					if (failureCount > 0) {
						appLog('ModuleSubscriptionPartialFailure', {
							module: name,
							successCount,
							failureCount,
							totalTenants: tenants.length
						}, 'warn')
					}
				}
			}
		}
	}

	// -- auto start --
	if (e.service.autoStart) {
		process.nextTick(() => {
			e.service.start().then(() => {
				process.on('SIGINT', app.exit)
				process.on('SIGTERM', app.exit)

				appEcho('[%s] STARTED (auto-start) at ', e.service.name, NOW, `\n\n*** 👍 Service running (${Date.now() - _start}ms) ***\n`)
				appNotify(`${svcName} service started - ${appConfig.port} (${process.env.NODE_ENV || 'development'})`, null, 'done')
			})
		})
	}
	else {
		appEcho('[%s] READY at ', e.service.name, NOW)
		appLog('ServiceReady', { config: appConfig, event: app.Event, when: NOW, NODE_ENV: process.env.NODE_ENV || 'development' })
	}
}

/**
 * Set up multi-tenancy event handlers
 * @param {string[]} moduleNames - Names of modules
 * @param {Object} modules - Module configurations
 */
function setupMultiTenancyHandlers(moduleNames, modules) {
	app.on(MESSAGE.tenant.ADDED, async ({ code }) => {
		for (const name of moduleNames) {
			const settings = modules[name],
				module = app.Service[name]

			if (settings.enabled) {
				if (name === 'eventbus') {
					await module.addTenant(code)
				}
				else if (typeof module.eventNames === 'function') {
					const events = module.eventNames(),
						emitter = module

					// Use batchSubscribe with improved error handling
					if (events.length > 0) {
						try {
							await app.Service.eventbus.batchSubscribe(events, code, emitter)
							appEcho('✅ Successfully subscribed %s events for new tenant %s', name, code)
						}
						catch (err) {
							appEcho('⚠️ Failed to subscribe %s events for new tenant %s: %s', name, code, err.message)
							appNotify('Event subscription failed for new tenant', {
								module: name,
								tenant: code,
								events: events.length,
								error: err.message
							}, 'warn')
						}
					}
				}
			}
		}
	})

	app.on(MESSAGE.tenant.REMOVED, async ({ code }) => {
		// 🔥 CRITICAL: Use orchestrator for complete cleanup to prevent memory leaks
		const cleanupOrchestrator = app.get('cleanupOrchestrator')

		if (cleanupOrchestrator) {
			try {
				appEcho('🧹 Starting orchestrated cleanup for tenant: %s', code)

				// Use monitored cleanup for comprehensive tenant destruction
				const effectiveness = await cleanupOrchestrator.monitoredCleanup(code, {
					force: true,
					timeout: 15000,
					continueOnError: false
				})

				appEcho('✅ Tenant %s cleanup completed with rating: %s', code, effectiveness.rating)

				// Verify cleanup was successful
				if (effectiveness.rating === 'FAILED') {
					appEcho('⚠️ Cleanup effectiveness was FAILED for tenant %s', code)
					appNotify('Tenant cleanup failed', {
						tenant: code,
						effectiveness,
						warning: 'Memory leak may persist'
					}, 'warn')
				}

			}
			catch (err) {
				appEcho('❌ Orchestrated cleanup failed for tenant %s: %s', code, err.message)
				appNotify('Tenant cleanup error', {
					tenant: code,
					error: err.message,
					fallback: 'Attempting module-level cleanup'
				}, 'error')

				// Fallback to original module-level cleanup
				await performModuleLevelCleanup(code, moduleNames, modules)
			}
		}
		else {
			appEcho('⚠️ CleanupOrchestrator not available, using module-level cleanup for tenant: %s', code)
			await performModuleLevelCleanup(code, moduleNames, modules)
		}
	})

	// Helper function for module-level cleanup (original logic)
	async function performModuleLevelCleanup(code, moduleNames, modules) {
		for (const name of moduleNames) {
			const settings = modules[name],
				module = app.Service[name]

			if (settings.enabled) {
				if (name === 'eventbus') {
					await module.removeTenant(code)
				}
				else if (typeof module.eventNames === 'function') {
					const events = module.eventNames(),
						emitter = module
					for (const event of events) {
						await app.Service.eventbus.unsubscribe(event, code, emitter)
					}
				}
			}
		}
	}
}

/**
 * Start the server
 * @param {Object} service - Service object
 */
app.start = async function(service) {
	const { name: svcName } = getAppConfig().service
	const isAccount = svcName === 'Account'

	// Use SSL ONLY for Account service in dev environment
	const useSSL = isAccount && !(process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'test')

	let sslConfig

	if (useSSL) {
		try {
			// Use environment variables for cert paths or fallback to default paths
			const certPath = process.env.CERT_PATH || path.join(__dirname, '..', '..', 'certs')
			const keyPath = process.env.SSL_KEY_PATH || path.join(certPath, 'server.key')
			const certFilePath = process.env.SSL_CERT_PATH || path.join(certPath, 'server.crt')

			// Use async file operations
			const [ key, cert ] = await Promise.all([
				fs.promises.readFile(keyPath),
				fs.promises.readFile(certFilePath)
			])

			sslConfig = { key, cert }
		}
		catch (err) {
			appNotify('SSL Configuration Error', err, 'error')
			throw new Error(`Failed to load SSL certificates: ${err.message}`)
		}
	}

	try {
		await new Promise((resolve => {
			const port = service.config?.port || app.get('port') || 8150
			if (sslConfig) {
				// Create HTTPS server
				app.server = https.createServer(sslConfig, app)
				app.server.listen(port, resolve)
			}
			else {
				// Create HTTP server
				app.server = app.listen(resolve)
			}
			app.server.setTimeout(TIMEOUT)
			app.server.keepAliveTimeout = KEEPALIVE
			app.server.headersTimeout = HEADERS
		}))

		const protocol = sslConfig ? 'https' : 'http'
		const host = service.host || '127.0.0.1'
		const port = service.config?.port || app.get('port') || 8150
		const baseUrl = `${protocol}://${host}:${port}`

		appEcho('[%s] Deployment: [%s]', service.name, app.get('env'))
		appEcho('[%s] Environment: [%s]', service.name, process.env.NODE_ENV)

		if (service.multitenancy) {
			if (service.tenants && Object.keys(service.tenants).length > 0) {
				appEcho('[%s] Tenants: ', service.name, service.tenants)
			}
		}

		if (app.get('loopback-component-explorer')) {
			const { mountPath } = app.get('loopback-component-explorer')
			appEcho('\nBrowse your REST API at %s%s', baseUrl, mountPath)
		}
		app.emit('started')
	}
	catch (err) {
		appNotify('Cannot start service', err, 'error')
		throw new Error(' ❌  START ERROR: ' + err)
	}
}

/**
 * Reboot the server
 */
app.reboot = async function() {
	appEcho('[%s] Rebooting service...', app.service?.name || 'Server')

	try {
		// Stop the server
		if (app.server) {
			await new Promise((resolve, reject) => {
				app.server.close(err => {
					if (err) reject(err)
					else resolve()
				})
			})
		}

		// Terminate modules
		if (app.service) {
			await app.exit()
		}

		// Restart the server
		const config = getAppConfig()
		await app.init(config)

		appEcho('[%s] Service rebooted successfully', config.service.name)
		return true
	}
	catch (err) {
		appNotify('Reboot failed', err, 'error')
		throw new Error(`Reboot failed: ${err.message}`)
	}
}

/**
 * Gracefully shut down the server
 */
app.exit = async function() {
	if (!app.service) return

	const { name: svcName, config } = app.service
	const { modules } = config

	appEcho('[%s] Shutting down service...', svcName)

	// 🔥 CRITICAL: Perform comprehensive tenant cleanup before shutdown
	const cleanupOrchestrator = app.get('cleanupOrchestrator')
	if (cleanupOrchestrator) {
		try {
			appEcho('[%s] Performing comprehensive tenant cleanup before shutdown...', svcName)

			// Get all active tenant registries for cleanup using proper API
			const { ModelRegistry } = require('loopback-datasource-juggler')
			const stats = ModelRegistry.getStats ? ModelRegistry.getStats() : {}
			if (stats.tenantStats && Array.isArray(stats.tenantStats)) {
				const tenantCodes = stats.tenantStats.map(stat => stat.tenantCode).filter(Boolean)
				appEcho('[%s] Found %d tenant registries to cleanup', svcName, tenantCodes.length)

				// Cleanup all tenants with force mode and shorter timeout for shutdown
				for (const tenantCode of tenantCodes) {
					try {
						await cleanupOrchestrator.cleanupTenant(tenantCode, {
							force: true,
							timeout: 5000,
							continueOnError: true
						})
					}
					catch (err) {
						appEcho('⚠️ Shutdown cleanup failed for tenant %s: %s', tenantCode, err.message)
					}
				}
			}
			else {
				appEcho('[%s] No tenant registry stats available for shutdown cleanup', svcName)
			}

			appEcho('[%s] ✅ Tenant cleanup completed during shutdown', svcName)

		}
		catch (err) {
			appEcho('[%s] ⚠️ Error during shutdown cleanup: %s', svcName, err.message)
		}
	}
	else {
		appEcho('[%s] ⚠️ CleanupOrchestrator not available during shutdown', svcName)
	}

	// Track modules that failed to terminate
	const terminationErrors = []

	// Terminate Modules
	for (const name of Object.keys(modules)) {
		try {
			const moduleConfig = modules[name]
			const module = app.Service[name]

			if (moduleConfig?.enabled !== true || typeof module.terminate !== 'function') continue

			await module.terminate()
			appEcho('[%s] terminated', name)
		}
		catch (err) {
			const errorMessage = `Failed to terminate module: ${name}`
			terminationErrors.push({ module: name, error: err.message })
			appEcho('\n*** Error: %s ***\n', errorMessage)
			appNotify(errorMessage, err, 'error')
		}
	}

	try {
		await app.service.terminate()
		appEcho('[%s] Service shutdown complete', svcName)

		if (terminationErrors.length > 0) {
			appLog('ServiceShutdownWithErrors', {
				service: svcName,
				errors: terminationErrors
			}, 'warn')
		}
		else {
			appLog('ServiceShutdown', { service: svcName }, 'info')
		}

		return true
	}
	catch (err) {
		appNotify('Service termination failed', err, 'error')
		throw new Error(`Service termination failed: ${err.message}`)
	}
}

//=============================================================================
// UTILITY METHODS
//=============================================================================

/**
 * Get all tenant codes
 * @returns {string[]} Array of tenant codes
 */
app.allTenantCodes = function() {
	const { multitenancy, tenantCode } = app.service
	return multitenancy ? app.models.Service.allTenantCodes() : [ tenantCode ]
}

/**
 * Get settings for a key
 * @param {string} key - Settings key
 * @param {string} place - Settings place
 * @returns {Object} Settings object
 */
app.getSettings = function(key, place) {
	return app.models.Service.getSettings(key, place)
}

/**
 * Get tenant-specific settings
 * @param {string} code - Tenant code
 * @param {string} key - Settings key
 * @returns {Object} Tenant settings
 */
app.getTenantSettings = function(code, key) {
	return app.models.Service.getTenantSettings(code, key)
}

/**
 * Echo to console if not muted
 * @param  {...any} params - Parameters to log
 */
app.echo = function(...params) {
	if (!mute) console.log(...params)
}

//=============================================================================
// ERROR HANDLING
//=============================================================================

// Handle uncaught exceptions
process.on('uncaughtException', async err => {
	console.error('🆘 Unhandled Exception', err)
	try {
		// Slack notification
		const env = process.env.NODE_ENV || 'dev',
			region = process.env.ENV_REGION,
			reg = region === CRM_REGION ? CRM : PERKD,
			channel = `${reg}-${env}-svc-crash`
		await appNotify('🆘 Unhandled Exception', { error: err.message, stack: err.stack }, 'error', channel )
		// Attempt graceful shutdown
		await app.exit()
		// Ensure process exits after cleanup
		setTimeout(() => process.exit(1), 1000)
	}
	catch (shutdownErr) {
		console.error('Failed to perform graceful shutdown:', shutdownErr)
		process.exit(1)
	}
})

//=============================================================================
// APPLICATION BOOTSTRAP
//=============================================================================

// Bootstrap the application, configure models, datasources and middleware.
// Sub-apps like REST API are mounted via boot scripts.
// Note: Already running within service context established at startup
boot(app, appPath(), err => {
		if (err) {
			console.error(' ❌  BOOT ERROR:', err.message || err, err.stack)
			return
		}

		console.log('🔒 Application bootstrap completed')

		// Add health check endpoint
		setupHealthCheck()

		// Add administrative endpoints for cleanup monitoring and manual operations
		setupAdministrativeEndpoints()

		// Initialize the application
		app.init(getAppConfig())
			.catch(err => {
				console.error(' ❌  INIT ERROR:', err)
			})
	})

/**
 * Set up health check endpoint
 */
function setupHealthCheck() {
	app.get('/health', (req, res) => {
		const health = {
			status: 'UP',
			timestamp: new Date().toISOString(),
			service: app.service?.name || 'Unknown',
			uptime: process.uptime(),
			memory: process.memoryUsage()
		}

		// Add service-specific health checks
		if (app.service && app.Service) {
			health.modules = {}

			for (const name in app.Service) {
				if (app.Service[name] && typeof app.Service[name].getStatus === 'function') {
					health.modules[name] = app.Service[name].getStatus()
				}
				else {
					health.modules[name] = { status: 'UNKNOWN' }
				}
			}
		}

		res.json(health)
	})
}

/**
 * Set up administrative endpoints for cleanup monitoring and manual operations
 */
function setupAdministrativeEndpoints() {
	// Manual tenant cleanup endpoint
	app.post('/admin/cleanup/:tenantCode', async (req, res) => {
		try {
			const { tenantCode } = req.params
			const { force = false, timeout = 10000 } = req.body

			const cleanupOrchestrator = app.get('cleanupOrchestrator')
			if (!cleanupOrchestrator) {
				return res.status(503).json({
					success: false,
					error: 'CleanupOrchestrator not available'
				})
			}

			appEcho('🧹 Manual cleanup requested for tenant: %s', tenantCode)

			const result = await cleanupOrchestrator.monitoredCleanup(tenantCode, {
				force,
				timeout,
				continueOnError: true
			})

			appEcho('✅ Manual cleanup completed for tenant %s with rating: %s', tenantCode, result.rating)

			res.json({
				success: true,
				tenantCode,
				result,
				timestamp: new Date().toISOString()
			})

		}
		catch (err) {
			appEcho('❌ Manual cleanup failed for tenant %s: %s', req.params.tenantCode, err.message)
			res.status(500).json({
				success: false,
				tenantCode: req.params.tenantCode,
				error: err.message,
				timestamp: new Date().toISOString()
			})
		}
	})

	// Cleanup statistics endpoint
	app.get('/admin/cleanup/stats', (req, res) => {
		try {
			const cleanupOrchestrator = app.get('cleanupOrchestrator')
			if (!cleanupOrchestrator) {
				return res.status(503).json({
					available: false,
					error: 'CleanupOrchestrator not available'
				})
			}

			const stats = cleanupOrchestrator.getCleanupStats()
			res.json({
				available: true,
				...stats
			})

		}
		catch (err) {
			res.status(500).json({
				available: false,
				error: err.message,
				timestamp: new Date().toISOString()
			})
		}
	})

	// Bulk cleanup endpoint for load testing scenarios
	app.post('/admin/cleanup/bulk', async (req, res) => {
		try {
			const { tenantCodes = [], force = false, timeout = 10000 } = req.body

			if (!Array.isArray(tenantCodes) || tenantCodes.length === 0) {
				return res.status(400).json({
					success: false,
					error: 'tenantCodes array is required and must not be empty'
				})
			}

			const cleanupOrchestrator = app.get('cleanupOrchestrator')
			if (!cleanupOrchestrator) {
				return res.status(503).json({
					success: false,
					error: 'CleanupOrchestrator not available'
				})
			}

			appEcho('🧹 Bulk cleanup requested for %d tenants', tenantCodes.length)

			const results = []
			let successCount = 0
			let failureCount = 0

			for (const tenantCode of tenantCodes) {
				try {
					const result = await cleanupOrchestrator.monitoredCleanup(tenantCode, {
						force,
						timeout,
						continueOnError: true
					})

					results.push({
						tenantCode,
						success: true,
						result
					})

					if (result.rating !== 'FAILED') {
						successCount++
					}
					else {
						failureCount++
					}

				}
				catch (err) {
					results.push({
						tenantCode,
						success: false,
						error: err.message
					})
					failureCount++
				}
			}

			appEcho('✅ Bulk cleanup completed: %d successful, %d failed', successCount, failureCount)

			res.json({
				success: true,
				summary: {
					total: tenantCodes.length,
					successful: successCount,
					failed: failureCount
				},
				results,
				timestamp: new Date().toISOString()
			})

		}
		catch (err) {
			appEcho('❌ Bulk cleanup failed: %s', err.message)
			res.status(500).json({
				success: false,
				error: err.message,
				timestamp: new Date().toISOString()
			})
		}
	})

	appEcho('✅ Administrative cleanup endpoints registered')
}

// Close the service context established at startup
})
