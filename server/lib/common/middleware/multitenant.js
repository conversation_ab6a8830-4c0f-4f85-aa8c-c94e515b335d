/**
 *  @module Multitenant	middleware    (alternative to auth.js)
 *
 *  Setup tenant-code in LB Context from request Header or query params
 *
 *	Token could be from:
 *		queryString: access_token=jwt_token or tenant_code=demo
 *		header: {
 *			'authorization': 'Bearer JWT token'
 *			'x-access-token': 'JWT token',
 *			'tenant-code': 'demo',
 *		}
 */

const { LRUCache } = require('lru-cache'),
	debug = require('debug')('multitenant:multitenant-ds'),
	{ Apis } = require('@crm/types'),
	{ SHARED } = require('@crm/loopback'),
	{ Context, TENANT: TENANT_KEY } = require('@perkd/multitenant-context'),
	{ Security } = require('@perkd/utils')

const { Jwt } = Security,
	{ Headers, Parameters } = Apis,
	{ AUTHORIZATION, X_ACCESS_TOKEN, TENANT, USER, TIMEZONE } = Headers,
	{ ACCESS_TOKEN, TENANT: TENANTCODE } = Parameters,
	// Reduce TTL for load testing scenarios to prevent memory accumulation
	TOKENS_TTL = process.env.NODE_ENV === 'test' ? 1000 * 60 * 2 : 1000 * 60 * 5,		// 2 minutes for testing, 5 minutes for production
	BEARER = 'Bearer '

// Cache for tenant tokens with TTL
const tokenCache = new LRUCache({
	max: process.env.NODE_ENV === 'test' ? 100 : 1000,  // Reduce cache size for testing
	ttl: TOKENS_TTL,
	// Add automatic pruning to avoid stale entries
	updateAgeOnGet: true,
	// Add TTL jitter to avoid cache stampedes
	ttlAutopurge: true,
	// Add disposal callback to track cache evictions
	dispose: (_value, key) => {
		debug(`Token cache evicted: ${key.substring(0, 20)}...`)
	}
})

function parseAuthorization(string) {
	return string.replace(BEARER, '').replace(`${X_ACCESS_TOKEN} `, '') //TODO: compat for both Bearer and x-access-token, remove x-access-token after UI deploy
}

// 🔒 FIXED: Parse JWT token without setting context
// Context setting is done by the caller INSIDE runInContext
function parseJWTToken(token, secretKey) {
	const cacheKey = `${token}:${secretKey}`
	let tenant = tokenCache.get(cacheKey)

	if (!tenant) {
		try {
			// Parse JWT token using consistent @perkd/utils Security.Jwt approach
			const jwt = new Jwt(secretKey)

			// Verify token first
			if (!jwt.verify(token)) {
				debug('❌ JWT token signature verification failed')
				return null
			}

			// Decode the token
			const decodedJWT = jwt.decode(token)
			const decoded = (typeof decodedJWT.payload === 'string')
				? JSON.parse(decodedJWT.payload)
				: decodedJWT.payload

			if (decoded && decoded.tenant) {
				// Handle both string and object formats for tenant
				let tenantCode
				if (typeof decoded.tenant === 'string') {
					tenantCode = decoded.tenant
				}
				else if (decoded.tenant && decoded.tenant.code) {
					tenantCode = decoded.tenant.code
				}
				else {
					debug('❌ JWT token tenant format invalid:', decoded.tenant)
					return null
				}

				tenant = {
					tenantCode,
					user: decoded.user || null,
					timezone: decoded.timezone || 'UTC'
				}

				// Cache the parsed result
				tokenCache.set(cacheKey, tenant)
				debug('✅ JWT token parsed successfully for tenant:', tenant.tenantCode)
			}
			else {
				debug('❌ JWT token missing tenant information')
				return null
			}
		}
		catch (error) {
			debug('❌ JWT token validation failed:', error.message)
			return null
		}
	}
	return tenant
}

// Add function to clear token cache for testing/cleanup
function clearTokenCache() {
	const sizeBefore = tokenCache.size
	tokenCache.clear()
	debug(`Token cache cleared: ${sizeBefore} entries removed`)
	return sizeBefore
}

// Export for testing and cleanup endpoints
module.exports = function(options = {}) {
	// Extract default tenant-code from middleware configuration
	const defaultTenantCode = options['tenant-code'] || null
	debug('Multitenant middleware initialized with default tenant-code:', defaultTenantCode)

	return async function injectTenant(req, res, next) {
		const { app, headers = {}, query = {} } = req

		// 🔒 CRITICAL FIX: Handle case where app.service is not yet initialized
		// This can happen during startup when middleware runs before service initialization
		let secretKey
		if (!app.service || !app.service.secretKey) {
			// Try to get secret key from environment as fallback
			secretKey = process.env.PERKD_SECRET_KEY || process.env.SECRET_KEY || null
			if (secretKey) {
				debug('✅ Using PERKD_SECRET_KEY from environment for JWT validation')
			}
			else {
				debug('⚠️  No secret key available - JWT validation unavailable, direct tenant-code only')
			}
		}
		else {
			secretKey = app.service.secretKey
		}
		// Create a fresh context for this request
		const requestContext = Context.createContext({})

		// Run the entire request handling in the isolated context
		try {
			await Context.runInContext(requestContext, async () => {
				if (!Context.getCurrentContext()) {
					const error = new Error('Failed to get context')
					debug('❌ Multitenant middleware: failed to get context')
					throw error
				}
				// Bypass tenant validation for shared datasource
				if (req.model?.dataSource?.name === SHARED) {
					return next()
				}

				// Handle token-based authentication
				if (headers[AUTHORIZATION] || headers[X_ACCESS_TOKEN] || query[ACCESS_TOKEN]) {
					// 🔒 CRITICAL FIX: Check if service is ready for JWT validation
					if (!secretKey) {
						debug('⚠️  JWT token provided but service not ready for validation')

						// 🔒 PRODUCTION FIX: Use default tenant-code instead of returning error
						if (defaultTenantCode) {
							debug('✅ Using default tenant-code as fallback:', defaultTenantCode)
							Context.setValues(defaultTenantCode, null, Context.timezone)
							req.accessToken = { userId: null }
							return next()
						}
						const error = new Error('Service not ready for JWT token validation - use tenant-code header instead')
						error.statusCode = 503  // Service Unavailable
						debug('❌ No default tenant-code configured')
						return next(error)

					}

					const accessToken = headers[AUTHORIZATION]
						? parseAuthorization(headers[AUTHORIZATION])
						: (headers[X_ACCESS_TOKEN] || query[ACCESS_TOKEN])

					// 🔒 FIXED: Parse JWT token but set context INSIDE runInContext
					const tenantData = parseJWTToken(accessToken, secretKey)

					if (!tenantData) {
						const error = new Error('Invalid token')
						error.status = 401
						return next(error)
					}

					// Set context values INSIDE the runInContext boundary
					const { tenantCode, user, timezone } = tenantData
					Context.setValues(tenantCode, user, timezone)
					Context.accessToken = accessToken

					// 🔒 VALIDATION: Ensure context was set correctly
					const validationTenant = Context.tenant
					if (validationTenant !== tenantCode) {
						const error = new Error(
							`Context validation failed: expected ${tenantCode}, got ${validationTenant}`
						)
						error.statusCode = 500
						debug('❌ Context validation failed:', { expected: tenantCode, actual: validationTenant })
						return next(error)
					}

					debug('✅ Context validation passed:', { tenant: validationTenant })
					req.accessToken = { userId: user?.id || null }
					return next()
				}

				// Handle direct tenant code
				if (headers[TENANT] || query[TENANTCODE]) {
					const tenantCode = headers[TENANT] || query[TENANTCODE],
						timeZone = headers[TIMEZONE] || Context.timezone

					let user = headers[USER] || Context.user || null

					if (typeof user === 'string') {
						try {
							user = JSON.parse(user)
						}
						catch (error) {
							debug('❌ Error parsing user header:', error.message, 'user:', user)
							user = undefined
						}
					}

					// Set tenant context values
					Context.setValues(tenantCode, user, timeZone)
					req.accessToken = { userId: user?.id || null }
					debug('✅ Set tenant context values:', tenantCode, 'for request:', req.method, req.url)

					return next()
				}

				// No tenant information provided
				const error = new Error('Tenant information required')
				error.statusCode = 401
				debug('❌ No tenant information provided and no default tenant-code configured')
				return next(error)

			})
		}
		catch (error) {
			error.statusCode = error.statusCode || 500
			next(error)
		}
	}
}

// Export cleanup function for external access
module.exports.clearTokenCache = clearTokenCache
