/**
 *  @module Model:Config
 */
const Promise = require('bluebird'),
	{ Context } = require('@perkd/multitenant-context')

module.exports = function(Config) {

	const CONFIG = {}

	// -----  Static Methods  -----

	Config.init = function(cb) {
		const App = Config.app

		const serviceName = App.service.name
		// Now that we have proper service context, this should work correctly
		const { tenant } = Context

		return Config.findById(serviceName).then(svcConfig => {
			const
				tenants = [],
				valueByT = svcConfig && svcConfig.value || {}
			for (const t in valueByT) {
				CONFIG[t] = valueByT[t]	// add tenant value to CONFIG (cache)
				tenants.push(t)
			}
			if (tenants.indexOf(tenant) <= -1 && CONFIG[tenant]) delete CONFIG[tenant]
			console.log('>> Config inited!')
			return valueByT
		})
	}

	Config.get = function(key, defaultValue, cb) {
		if (cb === undefined && typeof defaultValue === 'function') {
			cb = defaultValue;	defaultValue = undefined
		}
		const { tenant } = Context
		const value = CONFIG[tenant] && CONFIG[tenant][key] || defaultValue
		if (cb) cb(null, value)
		else return value
	}

	Config.set = function(key, value, persist, cb) {
		if (!persist) persist = true

		// save to memory
		const serviceName = Config.app.service.name
		const { tenant } = Context
		if (!CONFIG[tenant]) CONFIG[tenant] = {}
		CONFIG[tenant][key] = value

		if (persist) {
			return Config.findById(serviceName).then(svcConfig => {
				if (svcConfig) {
					const valueByT = svcConfig.value
					if (!valueByT[tenant]) valueByT[tenant] = {}
					valueByT[tenant][key] = value

					return svcConfig.updateAttributes({ value: valueByT })
				}
				const config = {
					service: serviceName,
					value: {},
				}
				config.value[tenant] = {}
				config.value[tenant][key] = value
				return Config.create(config)
			})
		} return Promise.resolve()
	}

	// -----  Instance Methods  -----

	// -----  Remote & Operation hooks  -----
	//	doc:  https://docs.strongloop.com/display/public/LB/Remote+hooks

	Config.disableRemoteMethodByName('create')
	Config.disableRemoteMethodByName('upsert')
	Config.disableRemoteMethodByName('updateAll')

	Config.disableRemoteMethodByName('patchOrCreate') // PATCH /Config/

	Config.disableRemoteMethodByName('findOne')

	Config.disableRemoteMethodByName('createChangeStream')

	Config.disableRemoteMethodByName('deleteById')

	Config.disableRemoteMethodByName('confirm')
	Config.disableRemoteMethodByName('count')
	Config.disableRemoteMethodByName('exists')

	Config.disableRemoteMethodByName('replaceById') // POST /Config/{id}/replace
	Config.disableRemoteMethodByName('replaceOrCreate') // PUT  /Config
	Config.disableRemoteMethodByName('prototype.patchAttributes') // PATCH /Config/{id}
	Config.disableRemoteMethodByName('upsertWithWhere')

	// -----  Remote Methods  -----

	Config.remoteMethod('init', {
		description: 'Init config in cache',
		http: { path: '/init', verb: 'post' },
		accepts: [],
		returns: { type: 'object', root: true },
	})

	Config.remoteMethod('get', {
		description: 'Get config by key',
		http: { path: '/get', verb: 'get' },
		accepts: [
			{ arg: 'key', type: 'string', required: true },
			{ arg: 'defaultValue', type: 'any' },
		],
		returns: { type: 'any', root: true },
	})

	Config.remoteMethod('set', {
		description: 'Update config by key',
		http: { path: '/set', verb: 'post' },
		accepts: [
			{ arg: 'key', type: 'string', required: true },
			{ arg: 'value', type: 'any', required: true },
			{ arg: 'persist', type: 'boolean', default: true },
		],
		returns: { type: 'object', root: true },
	})
}
