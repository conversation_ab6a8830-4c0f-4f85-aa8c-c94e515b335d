/**
 *  @module Sync - preload objects into in-memory cache
 *   - for resource type objects (eg. <PERSON><PERSON><PERSON>, Place, PlaceList, AppSettings)
 */
const { Sync } = require('@perkd/sync')

const LIMIT = 20000		// max instances loaded into cache

class SyncModule {
	/**
	 * @param	{Object} app - emitter
	 * @param	{Object} settings
	 * 			{Boolean} enabled
	 * 			{String} model
	 */
	constructor(app, settings) {
		const { models = [], enabled } = settings,
			[ model ] = models		// default

		if (!models.length) throw new Error("[Sync] module - 'models' missing in config.json")

		this.app = app
		this.syncs = {}
		this.enabled = enabled
		this.model = model
		this.models = models
	}

	get sync() {
		const { model, syncs } = this
		return syncs[model.name]
	}

	get(modelName) {
		const { syncs } = this
		return syncs[modelName]
	}

	settings(modelName = this.model.name) {
		const { name, ...settings } = this.models.find(m => m.name === modelName)
		return settings
	}

	async init() {
		const { app, enabled, syncs, models } = this,
			filter = {
				where: { or: [
					{ visible: true },
					{ visible: { exists: false } }
				] },
				limit: LIMIT
			}

		if (enabled) {
			for (const model of models) {
				const { name, cached } = model,
					sync = new Sync(name)

				await sync.init()
				syncs[name] = sync

				if (cached) {
					// 🔒 FIX: Wrap model access in context to prevent TRAP database access
					const { Context } = require('@perkd/multitenant-context')
					await Context.runInContext({}, async () => {
						// Set system context for sync module initialization
						Context.setValues('system', { id: 'sync-module' }, 'UTC')

						const Model = app.models[name],
							instances = await Model.find(filter),
							data = instances.map(i => ({ ...i.toJSON(), id: String(i.id) }))

						await sync.load(data)
					})
				}
			}
		}
	}

	async terminate() {
		const { enabled, syncs, models } = this

		if (!enabled) return

		for (const { name } of models) {
			await syncs[name].end()
		}
	}
}

module.exports = exports = SyncModule
