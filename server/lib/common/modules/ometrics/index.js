/**
 *  @module Metrics
 */

const { inherits } = require('node:util'),
	{ PassThrough } = require('node:stream'),
	EventEmitter = require('node:events'),
	debug = require('debug')('waveo:metrics'),
	applyFilter = require('loopback-filters'),
	{ Context } = require('@perkd/multitenant-context'),
	{ isEmptyObj, satisfyCondition, cloneDeep, pickDeep, shortId, flatten } = require('@perkd/utils'),
	Aggregate = require('./aggregate'),
	TimeSeries = require('./timeseries'),
	errors = appRequire('lib/common/errorResponse')

const DATASOURCE = 'trap',				// default datasource for createModel()
	SERVICE = 'service',
	DEFAULT = 'default',
	AGGREGATE_MODEL = 'Aggregate',
	TIMESERIES_MODEL = 'Timeseries',
	COLLECTION_PREFIX = { Aggregate: '_m_ag_', Timeseries: '_m_ts_' },
	DEFAULT_PREFIX = '_m_'

const Event = {
	metrics: {
		ADDED: 'metrics.added',
		SAVED: 'metrics.saved',
		REMOVED: 'metrics.removed',
		CLOSED: 'metrics.closed',
		REMOVE: 'metrics.remove',
	},
}

const SECOND = TimeSeries.SECOND,
	MINUTE = TimeSeries.MINUTE,
	HOUR = TimeSeries.HOUR,
	DAY = TimeSeries.DAY,
	MONTH = TimeSeries.MONTH,
	YEAR = TimeSeries.YEAR

const GRANULARITY = {
	year: [ MONTH ],
	month: [ DAY ],
	day: [ HOUR, MINUTE ],
	hour: [ MINUTE, SECOND ],
}

const UNKNOWN_DIMENSION = 'unknown'

function Metrics(app, settings = {}) {
	this.app = app

	// private
	this._aggregates = {}
	this._timeseries = {}

	// public
	this.prefix = settings.prefix
	this.options = settings.options || {}
	this.presets = getMetricsSettings(cloneDeep(settings.presets || {}))
	this.definitions = this.options.definitions ? appRequire(this.options.definitions) || {} : {}
	this.names = Object.keys(this.definitions)
	this.metricModelNames = [] // metrics models - internal created model
	this.tenantPaused = {}

	if (this.names.length === 0) throw new Error('Metrics definitions missing')

	this.aggregates = {
		add: this.addAggregate.bind(this),
		remove: this.removeAggregate.bind(this),
	}
	this.timeseries = {
		add: this.addTimeseries.bind(this),
		remove: this.removeTimeseries.bind(this),
	}

	if (this.options.maxListeners !== undefined) this.setMaxListeners(this.options.maxListeners)

	debug('definitions %j', this.definitions)
	debug('options %j', this.options)
}

inherits(Metrics, EventEmitter)
module.exports = exports = Metrics
module.exports.Event = Event

// --- Accumulator functions ---
module.exports.sum = 'sum'
module.exports.count = 'count'
module.exports.max = 'max'
module.exports.min = 'min'
module.exports.mean = 'mean'
module.exports.distinct = 'distinct'
module.exports.getMetricsSettings = getMetricsSettings

// ---  Instance methods  --------

Metrics.prototype.metricKey = function(name, accumulator, dValues) {
	if (!dValues) return
	dValues = dValues.map(v => v || UNKNOWN_DIMENSION)
	return (name + '.' + accumulator + (dValues.length > 0 ? '.' + dValues.join('.') : '')).toLowerCase()
}

Metrics.prototype.key2MetricName = function(key) {
	return key.split('.')[0]
}

Metrics.prototype.init = function() {
	const { app } = this,
		ds = app.dataSources.db,		// memory connector, we are not storing any data
		model = app.registry.createModel({ name: 'Metrics', base: 'Model' })

	model.stream = this.createChangeStream
	model.aggregate = this.getAggregateValues.bind(this)
	model.timeseries = this.getTimeseriesValues.bind(this)
	model.handle = this.handleEvent.bind(this)
	model.pause = this.pause.bind(this)
	model.resume = this.resume.bind(this)
	model.close = this.close.bind(this)
	model.reset = this.reset.bind(this)

	appRequire('lib/common/mixins/Metrics')(model)
	app.model(model, { dataSource: ds, public: true })		// must be 'public' to be accessible via API - change stream
}

Metrics.prototype.ready = function () {
	return this.addFromPresets()
}

Metrics.prototype.addDefinitions = function(definitions) {
	this.definitions = Object.assign(this.definitions, definitions)
}

Metrics.prototype.getDefinition = function(name) {
	return this.definitions[name]
}

Metrics.prototype.getDefinition = function (name) {
	return this.definitions[name]
}

Metrics.prototype.addFromPresets = function(presets) {
	debug('addFromPresets %j', presets)
	presets = presets ? getMetricsSettings(presets) : this.presets
	if (!presets) return

	debug('presets %j', presets)
	// Aggregates
	let metric = null
	for (const name in presets.aggregates) {
		if ({}.hasOwnProperty.call(presets.aggregates, name)) {
			metric = presets.aggregates[name]
			metric.forEach(m => {
				this.addAggregate(name, m.method, m.dimensions, m.filter, m.options)
			})
		}
	}

	// Timeseries
	for (const name in presets.timeseries) {
		if ({}.hasOwnProperty.call(presets.timeseries, name)) {
			metric = presets.timeseries[name]
			metric.forEach(m => {
				this.addTimeseries(name, m.scales, m.method, m.dimensions, m.filter, m.options)
			})
		}
	}
}

Metrics.prototype.remove = function(name) {
	const { definitions, app } = this,
		metric = definitions[name]

	this.removeAllListeners(metric.event)
	app.emit(Event.metrics.REMOVED, { metric: name })
}

Metrics.prototype.reset = async function() {
	const { metricModelNames, app } = this,
		R = {}

	await this.close()

	for (const name of metricModelNames) {
		if (name.indexOf(DEFAULT_PREFIX) === 0) {
			const count = await app.models[name].count()

			R[name] = count
			await app.models[name].destroyAll()
		}
	}

	return R
}

Metrics.prototype.close = async function() {
	const { _aggregates, _timeseries } = this,
		{ tenant } = Context

	await Promise.all([
		_aggregates[tenant]?.close(),
		_timeseries[tenant]?.close(),
	])

	delete _aggregates[tenant]
	delete _timeseries[tenant]
	return { [tenant]: 'closed' }
}

Metrics.prototype.pause = async function() {
	this.tenantPaused[Context.tenant] = true
	console.log('>>> ⏸️ metrics paused!!!')
}

Metrics.prototype.resume = async function() {
	delete this.tenantPaused[Context.tenant]
	console.log('>>> ✅ metrics resumed!!!')
}

/**
 * Get model for given metric (key) of Aggregate/Timeseries type (tenant-aware)
 * @param  {String} baseModel 	name of base Loopback model to use
 * @param  {String} key 		key of metric
 * @return {Model}           	Loopback Model object
 */
Metrics.prototype.getModel = function(baseModel, key) {
	const { definitions, app } = this,
		metricName = this.key2MetricName(key),
		metric = definitions[metricName]

	if (!metric) return

	const { model, collection } = metric,
		name = model || this.collectionName(baseModel, collection)

	if (!model && !app.models[name]) {
		this.createModel(baseModel, name)
	}

	return app.models[name]
}

Metrics.prototype.createModel = function(baseModel, name, ds, isPublic = false) {
	if (this.app.models[name]) return // if model existing, skip creation - ZJ

	const { app, metricModelNames } = this,
		{ definition } = app.registry.getModel(baseModel),
		{ properties, settings } = definition

	ds = ds || app.dataSources[DATASOURCE] || app.dataSources[DEFAULT]
	const model = app.registry.createModel(name, properties, settings)

	app.model(model, { dataSource: ds, public: isPublic })
	metricModelNames.push(name)
}

Metrics.prototype.dValues = function(metric, dimensions, data) {
	let dValues = []

	dimensions.forEach(dim => {
		if (dValues) {
			let value
			if (Array.isArray(metric.dimensions[dim].property)) {
				for (let i = 0; i < metric.dimensions[dim].property.length; i++) {
					value = pickDeep(data, metric.dimensions[dim].property[i])
					if (typeof value !== 'undefined') break // found dim value
				}
			}
			else value = pickDeep(data, metric.dimensions[dim].property)

			if (metric.dimensions[dim].asBoolean) {
				dValues.push(value ? metric.dimensions[dim].asBoolean[1] : metric.dimensions[dim].asBoolean[0])
			}
			else if (value === undefined && metric.dimensions[dim].default !== undefined) {
				dValues.push(metric.dimensions[dim].default)
			}
			else if (value === undefined) {
				dValues = false
			}
			else {
				dValues.push(value)
			}
		}
	})
	return dValues
}

Metrics.prototype.collectionName = function(type, name) {
	return (COLLECTION_PREFIX[type] || DEFAULT_PREFIX) + (this.prefix ? (this.prefix + '_' + name) : name)
}

Metrics.prototype.events = function() {
	// remove own/local events, in case these are being listened to as well
	const ownEvents = Object.keys(Event.metrics).map(k => Event.metrics[k])
	return _.difference(this.eventNames(), ownEvents)		// nodejs class 'EventEmitter' method
}

Metrics.prototype.tenants = function() {
	return _.union(Object.keys(this._aggregates), Object.keys(this._timeseries))
}

Metrics.prototype.getStats = function() {
	const { _aggregates, _timeseries } = this,
		init = { hits: 0, misses: 0, keys: 0, ksize: 0, vsize: 0 },
		stats = {}

	stats.overall = { ...init }
	stats.tenants = {}

	this.tenants().forEach(tenant => {
		stats.tenants[tenant] = { ...init }
	})

	stats.aggregates = { ...init }
	for (const tenant in _aggregates) {
		if ({}.hasOwnProperty.call(_aggregates, tenant)) {
			const s = _aggregates[tenant].getStats()

			stats.aggregates = sum(stats.aggregates, s)
			stats.tenants[tenant] = sum(stats.tenants[tenant], s)
			stats.overall = sum(stats.overall, s)
		}
	}

	stats.timeseries = { ...init }
	for (const tenant in _timeseries) {
		if ({}.hasOwnProperty.call(_timeseries, tenant)) {
			const s = _timeseries[tenant].getStats()

			stats.timeseries = sum(stats.timeseries, s)
			stats.tenants[tenant] = sum(stats.tenants[tenant], s)
			stats.overall = sum(stats.overall, s)
		}
	}

	return stats

	function sum(s1, s2) {
		s1.hits += s2.hits
		s1.misses += s2.misses
		s1.keys += s2.keys
		s1.ksize += s2.ksize
		s1.vsize += s2.vsize
		return s1
	}
}

/**
 * @param  {[type]} metrics 	=> sample 	[ "smssent.*", "smsdelivered.*", "smsfailed.*", "smssent.*" ]
 * @param  {[type]} filter  	=> sample 	{ and : [ { dimensions: { like: 'campaign' } }, { dimensions: { like: 'store' } } ] }
 * @param  {[type]} options 	=> samples  { type: "aggregate | timeseries" }
 * @return {[type]}
 */
Metrics.prototype.createChangeStream = async function(metrics, filter, options) {
	const { app } = this,
		{ tenant } = Context,
		changes = new PassThrough({ objectMode: true, allowHalfOpen: false })

	changes._destroy = function() {
		changes.end()
		changes.emit('end')
		changes.emit('close')
	}
	changes.destroy = changes.destroy || changes._destroy // node 8 compability

	changes.on('error', removeHandlers)
	changes.on('close', removeHandlers)
	changes.on('finish', removeHandlers)
	changes.on('end', removeHandlers)

	// ---------- @@ for testing only ----------

	const metricFilter = []
	metrics.map(m => metricFilter.push({ key: { like: m, options: 'i' } }))

	filter = Object.assign(filter || {}, { tenantCode: tenant })
	const composedFilter = {
		where: {
			and: [
				{ or: metricFilter },
				Object.assign(filter || {}, { tenantCode: tenant }),
			],
		},
	}

	app.on(Event.metrics.SAVED, handleChange)
	return changes

	function handleChange(evt) {
		debug('\nSAVED event: ', evt)

		if (options && options.type && evt.type !== options.type) return

		const change = getChange(evt)
		if (change) changes.write(change)
	}

	function removeHandlers() {
		debug('>> listeners removed')
		app.removeListener(Event.metrics.SAVED, handleChange)
	}

	function getChange(evt) {
		//  Filter by:
		//  1. tenantCode
		//  2. metric type & name  (specified in Params)
		//  3. evt type: aggregate / timeseries

		const filtered = applyFilter([ evt ], composedFilter)

		if (filtered.length > 0) {
			const change = {
				event: evt.metric,
				data: {
					type: evt.type,
					data: { key: evt.key, dimensions: evt.dimensions, dValues: evt.dValues, value: evt.value },
				},
			}
			if (evt.type === 'timeseries') {
				Object.assign(change.data.data, {
					scale: evt.scale,
					ts: evt.timestamp / 1000,
					values: evt.values,
				})
			}
			return change
		}
		return null
	}
}

Metrics.prototype.handleEvent = async function(event) {
	this.emit(event.name, event)
}

// ----  Aggregates  --------------

Metrics.prototype.addAggregate = function(name, accumulator, dimensions, filter = {}, options = {}) {
	debug('addAggregate %j', arguments)
	debug('metric %j', this.definitions[name])
	name = name.toLowerCase()

	const self = this,
		{ definitions, app, tenantPaused, _aggregates } = this,
		{ ttl } = options,
		metric = definitions[name],
		{ collection } = metric,
		collectionName = collection
			? this.collectionName(AGGREGATE_MODEL, collection)
			: null,
		{ tenant } = Context,
		metricTenant = ([ SERVICE, DATASOURCE, DEFAULT ].includes(tenant)) ? false : tenant, // if presets for all tenants or service context, metricTenant is false; otherwise use actual tenant
		metricId = shortId()
	dimensions = validate(metric, dimensions)

	if (collectionName) {
		this.createModel(AGGREGATE_MODEL, collectionName)
	}
	if (metric.event) {
		this.on(metric.event, handleEvent.bind(this, { filter: metric.filter }))
	}
	if (metric.events) {
		Object.keys(metric.events).forEach(evtname => this.on(evtname, handleEvent.bind(this, metric.events[evtname])))
	}

	this.on(Event.metrics.REMOVE, removeListener)

	app.emit(Event.metrics.ADDED, { type: 'aggregate', metric: name, dimensions })

	return { id: metricId, metric, type: 'aggregate' }

	async function handleEvent(param, evt) { 	// handleEvent is (must be) a Closure!  (don't move out of this method)
		const tenant = evt.tenantCode,
			{ multiples } = param

		if (tenantPaused[tenant] && !evt.recalc) {
			console.log('⚠️ [%s] metrics handler paused!!!', tenant)
			return
		}

		if (metricTenant && metricTenant !== tenant) return
		if (param.filter && !satisfyCondition(evt.data, param.filter)) return

		debug('Aggregates handleEvent %j', evt)

		//  eventHook() for DIRECTLY manipulating event BEFORE passing on to Aggregate
		//  eg. injecting custom data for dimension
		if (typeof options.eventHook === 'function') {
			const res = await options.eventHook(cloneDeep(evt))	// must clone, because hook may change event data
			_handleEvent(res)
		}
		else {
			_handleEvent(evt)
		}

		function _handleEvent(e) {
			if (!e) return

			const dValues = self.dValues(metric, dimensions, e.data),
				key = self.metricKey(name, accumulator, dValues)

			debug('dValues %j', { dValues })
			debug('key %s', key)
			if (dValues === false) {
				return // dimension missing TODO - stats
			}

			if (!_aggregates[tenant]) {
				_aggregates[tenant] = new Aggregate(self.options.aggregates)
				_aggregates[tenant].save = self.saveAggregate.bind(self)
				_aggregates[tenant].fetch = self.fetchAggregate.bind(self)
			}

			const aggregate = self._aggregates[tenant]

			collectionName && aggregate.setValue(key, {
				evtData: e.data,
				dimensions,
				dValues,
				multiples,
			}, filter, Aggregate[accumulator], metric.property, ttl)

			metric.model && self.app.models[metric.model].saveAggregateValue(key, {
				evtData: e.data,
				dimensions,
				dValues,
			}, filter, Aggregate[accumulator], metric.property, ttl)
		}
	}

	function removeListener(e) {
		if (e.id === metricId && e.type === 'aggregate') {
			self.removeListener(metric.event, handleEvent)
			self.removeListener(Event.metrics.REMOVE, removeListener)
			debug('removeAggregate id %s, metric %j', metricId, metric)
		}
		else {
			debug('removeAggregate id %s, my id %s, ignore.', e.id, metricId)
		}
	}
}

Metrics.prototype.removeAggregate = function(metricId) {
	this.emit(Event.metrics.REMOVE, { id: metricId, type: 'aggregate' })
}

Metrics.prototype.fetchAggregate = async function(key) {
	try {
		const model = this.getModel(AGGREGATE_MODEL, key),
			stored = await model.findOne({ where: { key } })

		return stored ? new Aggregate.Value(stored.value) : undefined
	}
	catch (err) {
		console.log('error [Metrics.prototype.fetchAggregate]', err)
		return null
	}
}

Metrics.prototype.saveAggregate = async function(key, value = {}) {
	const { tenant: tenantCode } = Context,
		model = this.getModel(AGGREGATE_MODEL, key),
		{ campaignId, dimensions, dValues } = value,
		metric = this.key2MetricName(key),
		data = {
			key,
			campaignId,
			dimensions,
			dValues,
			value: value.value,
		},
		[ instance, isNew ] = await model.findOrCreate({ where: { key } }, data)
			.catch(err => {
				console.error('error [Metrics.prototype.saveAggregate]', err)
				return []
			})

	if (!isNew) {	// found existing
		await instance.updateAttributes(data)
	}

	data.id = instance.id
	this.app.emit(Event.metrics.SAVED, Object.assign({ tenantCode, type: 'aggregate', metric }, data))

	return instance
}

Metrics.prototype.deleteAggregate = async function(key) {
	const model = this.getModel(AGGREGATE_MODEL, key),
		filter = {
			where: { key: { regexp: `^${key}` } }
		}

	return model.destroyAll(filter)
}

// ----  Timeseries  --------------

Metrics.prototype.addTimeseries = function(name, scales, accumulator, dimensions, filter = {}, options = {}) {
	debug('addTimeseries %j', { name, scales, accumulator, dimensions, filter, options })
	debug('metric %j', this.definitions[name])

	name = name.toLowerCase()

	const self = this,
		{ definitions, app, tenantPaused, _timeseries } = this,
		{ ttl } = options,
		metric = definitions[name],
		{ collection, event, events, property } = metric,
		collectionName = collection
			? this.collectionName(TIMESERIES_MODEL, collection)
			: null,
		{ tenant } = Context,
		metricTenant = ([ SERVICE, DATASOURCE, DEFAULT ].includes(tenant)) ? false : tenant, // if presets for all tenants or service context, metricTenant is false; otherwise use actual tenant
		metricId = shortId()
	dimensions = validate(metric, dimensions)

	if (collectionName) {
		this.createModel(TIMESERIES_MODEL, collectionName)
	}
	if (event) {
		this.on(event, handleEvent.bind(this, { filter: metric.filter }))
	}
	if (events) {
		Object.keys(events)
			.forEach(evtname => this.on(evtname, handleEvent.bind(this, events[evtname])))
	}

	this.on(Event.metrics.REMOVE, removeListener)

	app.emit(Event.metrics.ADDED, { type: 'timeseries', metric: name, scales, dimensions })

	return { id: metricId, metric, type: 'timeseries' }

	async function handleEvent(param, evt) { 	// handleEvent is (must be) a Closure!  (don't move out of this method)
		const tenant = evt.tenantCode,
			{ multiples } = param

		if (tenantPaused[tenant] && !evt.recalc) {
			console.log('⚠️ [%s] metrics handler paused!!!', tenant)
			return
		}

		if (metricTenant && metricTenant !== tenant) return
		if (param.filter && !satisfyCondition(evt.data, param.filter)) return

		debug('Timeseries handleEvent %j', evt)

		//  eventHook() for DIRECTLY manipulating event BEFORE passing on to Timeseries
		//  eg. injecting custom data for dimension
		if (typeof options.eventHook === 'function') {
			const res = await options.eventHook(cloneDeep(evt))	// must clone, because hook may change event data
			_handleEvent(res)
		}
		else {
			_handleEvent(evt)
		}

		function _handleEvent(e) {
			if (!e) return

			const dValues = self.dValues(metric, dimensions, e.data),
				key = self.metricKey(name, accumulator, dValues)

			debug('dValues %j', { dValues, metric, dimensions })
			debug('key %s', key)

			if (dValues === false) {
				return // dimension missing TODO - stats
			}

			if (!_timeseries[tenant]) {
				_timeseries[tenant] = new TimeSeries(self.options.timeseries)
				_timeseries[tenant].save = self.saveTimeseries.bind(self)
				_timeseries[tenant].fetch = self.fetchTimeseries.bind(self)
			}

			const timeseries = _timeseries[tenant]

			let timestamp
			if (metric.timestamp) {
				timestamp = new Date(pickDeep(e.data, metric.timestamp) || new Date()).getTime()
			}
			else {
				timestamp = e.data.timestamp ? new Date(e.data.timestamp).getTime() : e.published // default is event published time
			}

			collectionName && timeseries.setValue(key, timestamp, {
				evtData: e.data,
				dimensions,
				dValues,
				multiples,
			}, filter, scales, TimeSeries[accumulator], property, ttl)

			metric.model && app.models[metric.model].saveMetricValues(key, timestamp, {
				evtData: e.data,
				dimensions,
				dValues,
			}, filter, scales, TimeSeries[accumulator], property, ttl)
		}
	}

	function removeListener(e) {
		if (e.id === metricId && e.type === 'timeseries') {
			self.removeListener(metric.event, handleEvent)
			self.removeListener(Event.metrics.REMOVE, removeListener)
			debug('removeTimeseries id %s, metric %j', metricId, metric)
		}
		else {
			debug('removeTimeseries id %s, my id %s, ignore.', e.id, metricId)
		}
	}
}

Metrics.prototype.removeTimeseries = function(metricId) {
	this.emit(Event.metrics.REMOVE, { id: metricId, type: 'timeseries' })
}

Metrics.prototype.fetchTimeseries = async function(key, seriesKey, scale, timestamp, data = {}) {
	debug('fetchTimeseries %j', { key, seriesKey, scale, timestamp, data })

	const { dimensions, dValues } = data,
		model = this.getModel(TIMESERIES_MODEL, key),
		dbScale = _findDbScale(scale)

	if (!dbScale) return	// TODO - warning, scale not defined in GRANULARITY

	if (dbScale !== scale) {
		const scaleIndex = TimeSeries.SCALE.indexOf(scale),
			leftScale = TimeSeries.SCALE[scaleIndex - 1] || scale,
			dbScaleIndex = TimeSeries.SCALE.indexOf(dbScale),
			slots = TimeSeries.timeslots(timestamp),
			condition = {
				key,
				scale: dbScaleIndex,
				startTS: Math.floor(slots[dbScale].ts / 1000),
			},
			timeseries = TimeSeries.initSeriesData(dbScale, GRANULARITY, timestamp),
			newData = {
				...condition,
				dimensions,
				dValues,
				timeseries,
			}

		try {
			const [ instance, isNew ] = await model.findOrCreate({ where: condition }, newData)
				.catch(err => {
					console.error('error [Metrics.prototype.fetchTimeseries-1]', err)
					return []
				})

			if (!isNew) { // found existing
				const values = instance.timeseries[leftScale][slots[dbScale].ndx],
					series = new TimeSeries.Series(scale, timestamp, values, dimensions, dValues)

				debug('fetchedSeries %j', series)
				return series
			}
		}
		catch (err) {
			appLog('fetchTimeseriesFindOrCreate1', { condition, newData, scale, dbScale, err })
			return null
		}
	}

	// dbScale == scale
	const scaleIndex = TimeSeries.SCALE.indexOf(scale),
		leftScale = TimeSeries.SCALE[scaleIndex - 1] || scale,
		condition = {
			key,
			scale: scaleIndex,
			startTS: Math.floor(timestamp / 1000),
		},
		timeseries = TimeSeries.initSeriesData(scale, GRANULARITY),
		newData = { ...condition,
			dimensions: data.dimensions,
			dValues: data.dValues,
			timeseries,
		}

	try {
		const [ instance, isNew ] = await model.findOrCreate({ where: condition }, newData)
			.catch(err => {
				console.error('error [Metrics.prototype.fetchTimeseries-2]', err)
				return []
			})

		if (!isNew) { // found existing
			const values = instance.timeseries[leftScale],
				series = new TimeSeries.Series(scale, timestamp, values, dimensions, dValues)

			debug('fetchedSeries %j', series)
			return series
		}
	}
	catch (err) {
		appLog('fetchTimeseriesFindOrCreate2', { condition, newData, scale, dbScale, err })
		return null
	}

	function _findDbScale(scale) {
		if (GRANULARITY[scale]) return scale

		const scaleIndex = TimeSeries.SCALE.indexOf(scale)
		return TimeSeries.SCALE.find((scale, index) => (index > scaleIndex) && GRANULARITY[scale])
	}
}

Metrics.prototype.saveTimeseries = async function(key, seriesKey, series) {
	debug('saveTimeseries %j', { key, seriesKey, series })
	const { app, _timeseries } = this,
		{ tenant } = Context,
		model = this.getModel(TIMESERIES_MODEL, key),
		NOW = new Date(series.timestamp).getTime(),
		allSeries1 = _timeseries[tenant].getAllSeries(key, NOW - 60000),
		seriesData1 = TimeSeries.getSeriesData(allSeries1, GRANULARITY, false),
		allSeries2 = _timeseries[tenant].getAllSeries(key, NOW),
		seriesData2 = TimeSeries.getSeriesData(allSeries2, GRANULARITY, false),
		scaleName = TimeSeries.key2ScaleName(seriesKey),
		savedEvts = [],
		updates = []

	if (allSeries1[scaleName] && allSeries2[scaleName]
		&& (allSeries1[scaleName].timestamp === allSeries2[scaleName].timestamp)) {
		savedEvts.push({
			key,
			seriesKey,
			timestamp: series.timestamp,
			dimensions: series.dimensions,
			dValues: series.dValues,
			value: series.value,
			values: { ...series.changes },
		})
	}
	else {
		[ allSeries1, allSeries2 ].forEach(allSeries => {
			if (allSeries[scaleName] && !isEmptyObj(allSeries[scaleName].changes)) {
				savedEvts.push({
					key,
					seriesKey: TimeSeries.seriesKey(scaleName, allSeries[scaleName].timestamp),
					timestamp: allSeries[scaleName].timestamp,
					dimensions: allSeries[scaleName].dimensions,
					dValues: allSeries[scaleName].dValues,
					value: allSeries[scaleName].value,
					values: { ...allSeries[scaleName].changes },
				})
			}
		})
	}

	for (const scale in GRANULARITY) {
		if (seriesData1[scale] && seriesData2[scale]
			&& seriesData1[scale].timestamp === seriesData2[scale].timestamp) {
			updates.push({
				where: {
					key,
					scale: seriesData1[scale].scale,
					startTS: Math.floor(seriesData1[scale].timestamp / 1000),
				},
				setData: _.extend(flatten({ timeseries: seriesData1[scale].values }),
					flatten({ timeseries: seriesData2[scale].values })),
			})
		}
		else {
			[ seriesData1, seriesData2 ].forEach(seriesData => {
				if (seriesData[scale]) {
					updates.push({
						where: {
							key,
							scale: seriesData[scale].scale,
							startTS: Math.floor(seriesData[scale].timestamp / 1000),
						},
						setData: flatten({ timeseries: seriesData[scale].values }),
					})
				}
			})
		}
	}

	for (const update of updates) {
		debug('update %j', update)
		try {
			const result = await model.update(update.where, { $set: update.setData }, { allowExtendedOperators: true })

			if (result.count === 0) {
				appLog('saveTimeseriesNoUpdate', { update, result })
			}
		}
		catch (err) {
			appLog('saveTimeseriesUpdate', { update, err })
		}
	}

	for (const evt of savedEvts) {
		app.emit(Event.metrics.SAVED, Object.assign({
			type: 'timeseries',
			metric: this.key2MetricName(key),
			scale: scaleName,
			tenantCode: tenant,
		}, evt))
	}

	return true
}

Metrics.prototype.deleteTimeseries = async function(key) {
	const model = this.getModel(TIMESERIES_MODEL, key),
		filter = {
			where: { key: { regexp: `^${key}` } }
		}

	return model.destroyAll(filter)
}

/**
 * Fetch aggregate metrics data
 * @param {String} name - e.g. totalrevenue
 * @param {String} type - e.g. sum
 * @param {Array} dimensions - []
 * @param {Array} dValues - []
 * @param {Object} filter
 * @return {Object}
 */
Metrics.prototype.getAggregateValues = async function(name, type, dimensions, dValues, filter) {
	const key = this.metricKey(name, type, dValues || []),
		Model = this.getModel(AGGREGATE_MODEL, key)

	if (!Model) throw errors.serverError('Model not found (metric: ' + name + ')')

	const query = {
			find: {
				key: { $regex: `^${key}` },
				dimensions: { $eq: dimensions },
			}
		},
		instances = await Model.query(query, { instance: true }),
		result = instances.map(instance => ({ name, type, dimensions, dValues, ...instance.toJSON() }))

	return result
}

/**
 * Fetch timeseries metrics data
 * @param {String} name - e.g. totalrevenue
 * @param {String} type - e.g. sum
 * @param {Array} dimensions - []
 * @param {Array} dValues - []
 * @param {Number} start - unix_timestamp (second)
 * @param {Number} end - unix_timestamp (second)
 * @param {String} scale - "month / day / hour / minute / second",
 * @return {Object}
 */
Metrics.prototype.getTimeseriesValues = async function(name, type, dimensions, dValues, start, end, scale) {
	const key = this.metricKey(name, type, dValues || []),
		Model = this.getModel(TIMESERIES_MODEL, key)

	if (!Model) throw errors.serverError('Model not found (metric: ' + name + ')')

	const metricValues = await Model.getMetricValues(key, dimensions, dValues, start * 1000, end * 1000, scale, GRANULARITY),
		result = Object
			.values(metricValues)
			.map(value => ({ name, type, dimensions, dValues, start, end, scale, ...value }))

	return result
}

// ----  Private functions  ---------

function validate(metric, dimensions) {
	dimensions = dimensions || []
	dimensions = Array.isArray(dimensions) ? dimensions : [ dimensions ]
	if (metric === undefined) throw new Error('validate: metric undefined')

	const diff = _.difference(dimensions, Object.keys(metric.dimensions || {}))
	if (diff.length > 0) throw new Error('Dimensions `' + diff.toString() + '` not defined')

	return dimensions
}

function getMetricsSettings(settings) {
	const metricsSettings = {}

	Object.keys(settings).forEach(type => { // type: aggregates | timeseries
		metricsSettings[type] = {}
		Object.keys(settings[type]).forEach(metrics => {
			metrics.split(',').forEach(metric => {
				metricsSettings[type][metric] = metricsSettings[type][metric] || []
				metricsSettings[type][metric] = metricsSettings[type][metric].concat(cloneDeep(settings[type][metrics]))
			})
		})
	})

	debug('metricsSettings %j', metricsSettings)
	return metricsSettings
}
