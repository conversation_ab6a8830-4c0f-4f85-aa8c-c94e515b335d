/**
 *  @module Aggregate
 */

const { inherits } = require('util'),
	EventEmitter = require('events'),
	Cache = require('node-cache'),
	{ pickDeep, satisfyCondition } = require('@perkd/utils'),
	debug = require('debug')('waveo:metrics:aggregate')

/* ----------  In-memory cache:
mCache = nodeCache;

object stored in nodeCache: {
	value: <result>		value of aggregate (can be any type)
	values: [],			past values, new value pushed into values BEFORE values passed to compute function(fn)
}

settings = {
<key>: {
	value: <result>		initial value
	fn: function()		compute function = function(values, value, newValue), value and newValue are optional
	values: [],			initial values
	ttl: <seconds>		time-to-live in seconds, afterwhich data will be deleted from cache
	}
}

Events
	'added'			new key added		{ key }
	'removed'		key removed			{ key }
	'expired'		key expired & removed from memory cache 	{ key, value }
	'closed'		memory cache flushed and emptied, settings cleared
------------- */

const TIMER = '__timer',
	DEFAULTS = {
		ttl: 5 * 60,			// 5 minutes (seconds)
		persist: true,
		persistInterval: 5,		// seconds
	},
	EVENT_PROPERTIES = []		// properties (from event data) to be stored with aggregate

function Aggregate(options) {
	this.settings = {}			// settings of each key

	this.options = { ...DEFAULTS, ...options }
	this.mCache = new Cache({ useClones: false, checkperiod: 3 })
	this.setExpiryHandler()
	this.setMaxListeners(Infinity)
}

class Value {
	constructor(value, values, dimensions, dValues) {
		this.value = typeof value === 'number' ? value : 0
		this.dimensions = dimensions || []
		this.dValues = dValues || []
		this.values = values || []
		this.changed = false
	}
}

inherits(Aggregate, EventEmitter)
module.exports = exports = Aggregate
module.exports.Value = Value

// --- Accumulator functions ---
module.exports.sum = sum
module.exports.count = count
module.exports.max = max
module.exports.min = min
module.exports.mean = mean
module.exports.distinct = distinct

// ---  Instance methods  --------

Aggregate.prototype.add = async function(key, computeFn, property, filter, ttl) {
	debug('add aggregate %j', { key, computeFn, property, filter, ttl })

	this.settings[key] = {			// store a copy of initial settings
		fn: computeFn,
		property,
		filter,
		ttl: parseInt(ttl || this.options.ttl),
	}
	this.emit('added', { key })
}

Aggregate.prototype.remove = async function(key) {
	const { mCache: cache } = this

	this.settings[key] = undefined
	this.emit('removed', { key })
	cache.del(key)
}

/**
 * Set Value for Key in cache
 * @param {String} key metric key
 * @param {Object} data { evtData: event data, dimensions: array of dimension names, dValues: array of dimension values }
 * @param {Object} filter
 * @param {Object} computeFn
 * @param {Object} property
 * @param {Object} ttl
 * @return {undefined}
 */
Aggregate.prototype.setValue = function(key, data, filter, computeFn, property, ttl) {
	debug('setValue %j', { key, data, filter, computeFn, property, ttl })

	const { settings, options } = this,
		{ evtData, dimensions, dValues, multiples } = data

	if (!filter || satisfyCondition(evtData, filter)) {
		debug('✅  satisfiedCondition %j', { evtData, filter })

		if (settings[key] === undefined) {
			this.add(key, computeFn, property, filter, ttl)
		}
		this.setPersistTimer(options.persistInterval)

		const cached = this.initValue(key)
		cached.dimensions = dimensions
		cached.dValues = dValues

		// additional event properties to store
		EVENT_PROPERTIES.forEach(prop => {
			cached[prop] = pickDeep(evtData, prop)
		})

		// use evtData as value, or extract value of property from evtData
		const newValue = (property === undefined)
				? evtData
				: pickDeep(evtData, property),
			value = new Aggregate.Value(newValue)

		cached.calculate(multiples, cached, value)
		cached.changed = true
		// cache.set(key, cached, settings.ttl);	// skipped for speed, modify cached values DIRECTLY, useClones MUST be false
	}
	else {
		debug('❌  not satisfyCondition %j', { evtData, filter })
	}
}

Aggregate.prototype.initValue = function(key) {
	const { mCache: cache } = this,
		settings = this.settings[key],
		{ ttl, fn } = settings

	let cached = cache.get(key)

	if (cached === undefined) {
		// return an initial (zero) value
		cached = new Aggregate.Value()
		cached.calculate = fn
		cache.set(key, cached, ttl)

		settings.value = cached.value
		settings.values = cached.values

		process.nextTick(() => {
			// async fetch previous stored value (if any) and apply to cached asynchronously
			this.fetch(key).then(stored => {
				if (stored) {
					cached.calculate(null, cached, stored, true)
					cached.changed = true
				}
			})
		})
	}
	else {
		cache.ttl(key, ttl)
	}
	return cached
}

Aggregate.prototype.persist = async function(key, value) {
	debug('persist %j', { key, value })

	const { mCache: cache, options } = this

	if (!options.persist) return

	try {
		if (key) return saveChanges(this, key, value)

		const saves = []

		for (const key of this.keys()) {
			saves.push(
				saveChanges(this, key, cache.get(key))
			)
		}

		const res = await Promise.all(saves)
		return res
	}
	catch (err) {
		console.error('error [Aggregate.prototype.persist]', err)
	}

	async function saveChanges(self, key, value) {
		if (value && value.changed) {
			value.changed = false
			return self.save(key, value)
		}
	}
}

Aggregate.prototype.close = async function() {
	const { mCache: cache } = this

	await this.persist().catch(() => null)
	cache.close()
	cache.flushAll()

	this.mCache = undefined
	this.settings = {}
	this.emit('closed')
}

Aggregate.prototype.setPersistTimer = function(interval) {
	const { mCache: cache } = this

	if (interval === 0) cache.del(TIMER)		// 'cancel' timer, no timed persistance of cache
	else if (cache.get(TIMER) === undefined) {
		cache.set(TIMER, true, interval)
	}
}

Aggregate.prototype.setExpiryHandler = function() {
	const { mCache: cache } = this

	cache.on('expired', (key, value) => {
		if (key === TIMER) {
			this.persist()
		}
		else if (value.changed) {
			cache.set(key, value, DEFAULTS.ttl.minute) // has changes not save, put it back to cache for TIMER persist in next round.
		}
		else {
			this.emit('expired', { key })
		}
	})
}

Aggregate.prototype.getValue = function(key) {
	return this.mCache.get(key)	// returns Aggregate.Value object
}

Aggregate.prototype.getSettings = function() {
	return this.settings
}

Aggregate.prototype.keys = function() {
	return this.mCache.keys().filter(e => e !== TIMER)
}

Aggregate.prototype.getStats = function() {
	return this.mCache.getStats()
}

//  Caller to implement these methods where necessary

Aggregate.prototype.fetch = function(key) {
	// return Aggregate.Value object in callback
	throw new Error('Must implement fetch()')
}

Aggregate.prototype.save = function(key, value) {
	// 'value' is an Aggregate.Value object;  return stored object in OPTIONAL callback
	throw new Error('Must implement save()')
}

// ----  Accumulators (exported)

// @this is the Value object
function sum(multiples, oldVal, newVal, restored) {
	if (newVal && typeof newVal.value === 'number') this.value += multiples ? multiples * newVal.value : newVal.value
}

// @this is the Value object
function count(multiples, oldVal, newVal, restored) {
	if (newVal && typeof newVal.value === 'number') restored ? this.value += newVal.value : this.value += multiples || 1
}

function min(multiples, oldVal, newVal, restored) {
	throw new Error('not implemented')
}

function max(multiples, oldVal, newVal, restored) {
	throw new Error('not implemented')
}

function mean(multiples, oldVal, newVal, restored) {
	// TODO:  need past values
	throw new Error('not implemented')
}

function distinct(multiples, oldVal, newVal, restored) {
	// TODO:  need past values/keys
	throw new Error('not implemented')
}
