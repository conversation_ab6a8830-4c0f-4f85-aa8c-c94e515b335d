const config = {
	connection: {
		minPoolSize: 0,
		maxPoolSize: process.env.NODE_ENV === 'test' ? 5 : 10,  // Reduce pool size for testing
		maxIdleTime: process.env.NODE_ENV === 'test' ? 60000 : 300000,  // 1 minute for testing, 5 minutes for production
		acquireTimeout: 30000,       // 30 seconds
		cleanupInterval: process.env.NODE_ENV === 'test' ? 30000 : 60000,  // More frequent cleanup for testing
		monitorInterval: process.env.NODE_ENV === 'test' ? 30000 : 60000,  // More frequent monitoring for testing
		initTimeout: 30000,         // 30 seconds
		maxRetries: 3,
		retryDelay: 1000,           // 1 second
	},

	session: {
		validationInterval: 30000,   // 30 seconds
		maxTransactionRetries: 3,
		transactionTimeout: 30000,   // 30 seconds
		mode: 'disabled'             // 'auto', 'enabled', or 'disabled' // turn off before we upgrade mongodb to 8.0
	},

	validation: {
		healthCheckInterval: 30000,  // 30 seconds
		maxErrorThreshold: 5,
		maxActiveRequests: 20
	},

	cleanup: {
		idleTimeout: process.env.NODE_ENV === 'test' ? 60000 : 300000,    // 1 minute for testing, 5 minutes for production
		sessionTimeout: process.env.NODE_ENV === 'test' ? 120000 : 600000, // 2 minutes for testing, 10 minutes for production
		maxSessionAge: process.env.NODE_ENV === 'test' ? 300000 : 3600000  // 5 minutes for testing, 1 hour for production
	}
}

module.exports = config