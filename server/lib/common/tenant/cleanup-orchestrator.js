/**
 * @module minimal-cleanup-orchestrator
 * Minimal tenant cleanup focusing only on essential operations
 *
 * HYPOTHESIS: Only ModelRegistry cleanup is essential - framework handles everything else
 */

const debug = require('debug')('cleanup:minimal')
const { ModelRegistry } = require('loopback-datasource-juggler')

class TenantCleanupOrchestrator {
	constructor(app) {
		this.app = app
		this.ModelRegistry = ModelRegistry
		this.metrics = {
			totalCleanups: 0,
			successfulCleanups: 0,
			failedCleanups: 0
		}

		debug('MinimalCleanupOrchestrator initialized - testing essential-only cleanup')
	}

	/**
	 * Minimal cleanup - only essential operations
	 */
	async cleanupTenant(tenantCode, options = {}) {
		const { timeout = 10000 } = options
		const startTime = Date.now()

		debug(`🧹 Starting minimal cleanup for tenant: ${tenantCode}`)

		// 🔒 CRITICAL FIX: Skip cleanup for service tenant
		// The "service" tenant is used for bootstrap and system operations
		// It should not be cleaned up as it's not a regular tenant
		if (tenantCode === 'service') {
			debug('⚠️ Skipping cleanup for service tenant - system tenant should not be cleaned up')
			return {
				skipped: true,
				reason: 'Service tenant cleanup not allowed',
				tenantCode
			}
		}

		try {
			const result = await Promise.race([
				this.performEssentialCleanup(tenantCode),
				new Promise((_, reject) =>
					setTimeout(() => reject(new Error('Cleanup timeout')), timeout)
				)
			])

			this.updateMetrics(true, Date.now() - startTime)
			debug(`✅ Minimal cleanup completed for tenant ${tenantCode} in ${Date.now() - startTime}ms`)
			return result

		}
		catch (err) {
			this.updateMetrics(false, Date.now() - startTime)
			debug(`❌ Minimal cleanup failed for tenant ${tenantCode}: ${err.message}`)
			throw err
		}
	}

	/**
	 * Essential cleanup - only what framework doesn't handle automatically
	 */
	async performEssentialCleanup(tenantCode) {
		const results = { essential: true }

		// HYPOTHESIS: Only ModelRegistry cleanup is essential
		// Everything else should be handled by framework:
		// - Context sessions: multitenant-context auto-cleanup
		// - Business resources: framework lifecycle
		// - Circular references: V8 garbage collection
		// - Connection cleanup: DataSource disconnect

		try {
			// Essential Operation 1: ModelRegistry cleanup
			if (typeof this.ModelRegistry.cleanupTenant === 'function') {
				const beforeStats = this.ModelRegistry.getStats?.() || {}
				const beforeRegistries = beforeStats.tenantRegistries || 0

				// Try direct cleanup first
				let cleaned = 0
				try {
					cleaned = this.ModelRegistry.cleanupTenant(tenantCode) || 0
				}
				catch (directErr) {
					debug(`Direct cleanup failed, trying DataSource-based: ${directErr.message}`)

					// Fallback: DataSource-based cleanup
					const dataSource = this.findDataSourceForTenant(tenantCode)
					if (dataSource && dataSource._dsId) {
						const registryKey = `ds_${dataSource._dsId}`
						cleaned = this.ModelRegistry.cleanupTenant(registryKey, dataSource) || 0
					}
				}

				const afterStats = this.ModelRegistry.getStats?.() || {}
				const afterRegistries = afterStats.tenantRegistries || 0

				results.modelRegistry = {
					modelsRemoved: cleaned,
					registriesRemoved: beforeRegistries - afterRegistries,
					success: cleaned > 0 || beforeRegistries === afterRegistries
				}

				debug(`🗂️ ModelRegistry cleanup: ${cleaned} models, ${beforeRegistries - afterRegistries} registries removed`)
			}
			else {
				results.modelRegistry = { error: 'ModelRegistry.cleanupTenant not available' }
			}

			// Essential Operation 2: Force garbage collection (if available)
			if (global.gc) {
				global.gc()
				results.gcForced = true
				debug('🗑️ Garbage collection forced')
			}

			// DELIBERATELY NOT INCLUDED (testing if framework handles):
			// - Context session cleanup
			// - Business resource cleanup
			// - Manual circular reference breaking
			// - EventBus listener cleanup
			// - Timer/interval cleanup

			return results

		}
		catch (err) {
			debug(`❌ Essential cleanup failed: ${err.message}`)
			throw err
		}
	}

	/**
	 * Find DataSource for tenant (minimal implementation)
	 */
	findDataSourceForTenant(tenantCode) {
		try {
			// Check connection manager
			if (this.app.connectionManager?.getExistingConnection) {
				const connection = this.app.connectionManager.getExistingConnection(tenantCode)
				if (connection) return connection
			}

			// Search app.dataSources
			if (this.app.dataSources) {
				for (const ds of Object.values(this.app.dataSources)) {
					if (ds && (ds.name === tenantCode || `ds_${ds._dsId}` === tenantCode)) {
						return ds
					}
				}
			}

			return null
		}
		catch (err) {
			debug(`⚠️ Error finding DataSource for ${tenantCode}: ${err.message}`)
			return null
		}
	}

	/**
	 * Update cleanup metrics
	 */
	updateMetrics(success, duration) {
		this.metrics.totalCleanups++
		if (success) {
			this.metrics.successfulCleanups++
		}
		else {
			this.metrics.failedCleanups++
		}
	}

	/**
	 * Get current system statistics
	 */
	getCleanupStats() {
		const memUsage = process.memoryUsage()
		const modelRegistryStats = this.ModelRegistry.getStats ? this.ModelRegistry.getStats() : {}

		return {
			memory: {
				heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
				heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
				external: Math.round(memUsage.external / 1024 / 1024)
			},
			modelRegistry: {
				tenantRegistries: modelRegistryStats.tenantRegistries || 0,
				totalModels: modelRegistryStats.totalModels || 0
			},
			metrics: this.metrics
		}
	}

	/**
	 * Calculate cleanup effectiveness
	 */
	calculateEffectiveness(before, after, cleanupResult) {
		const memoryFreed = before.memory.heapUsed - after.memory.heapUsed
		const registriesRemoved = before.modelRegistry.tenantRegistries - after.modelRegistry.tenantRegistries

		// Simple effectiveness rating
		let rating = 'FAILED'
		if (cleanupResult && cleanupResult.modelRegistry?.success) {
			if (memoryFreed >= 0 && registriesRemoved >= 0) {
				rating = 'EXCELLENT'
			}
			else if (memoryFreed >= -5) { // Allow small memory increase
				rating = 'GOOD'
			}
			else {
				rating = 'FAIR'
			}
		}

		return {
			memoryFreed,
			registriesRemoved,
			cleanupResult,
			rating,
			success: rating !== 'FAILED',
			approach: 'minimal-essential-only'
		}
	}

	/**
	 * Monitored cleanup with effectiveness tracking
	 */
	async monitoredCleanup(tenantCode, options = {}) {
		const before = this.getCleanupStats()
		debug(`📊 State before minimal cleanup [${tenantCode}]:`, before)

		try {
			const cleanupResult = await this.cleanupTenant(tenantCode, options)

			const after = this.getCleanupStats()
			debug(`📊 State after minimal cleanup [${tenantCode}]:`, after)

			const effectiveness = this.calculateEffectiveness(before, after, cleanupResult)
			debug(`📈 Minimal cleanup effectiveness [${tenantCode}]:`, effectiveness)

			return effectiveness

		}
		catch (err) {
			debug(`❌ Monitored minimal cleanup failed [${tenantCode}]: ${err.message}`)
			throw err
		}
	}
}

module.exports = TenantCleanupOrchestrator
