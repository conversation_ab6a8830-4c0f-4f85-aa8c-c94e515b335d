/**
 *  @module Mixin:MultitenantRemote   (Remote model multi-tenancy)
 */

const { EventEmitter } = require('node:events'),
	debug = require('debug')('multitenant:remote'),
	{ Apis } = require('@crm/types'),
	{ Context } = require('@perkd/multitenant-context'),
	{ Security } = require('@perkd/utils')

const { Jwt } = Security,
	{ Headers } = Apis,
	{ X_ACCESS_TOKEN } = Headers

module.exports = function(Model) {
	const { remotes } =  Model.dataSource?.connector || {}

	if (remotes) {
		injectToken(remotes, Model.modelName, Model)
	}
	else {
		Model.on('dataSourceAttached', model => {
			const ds = model.getDataSource(),
				{ connector } = ds ?? {},
				{ remotes } = connector ?? {}

			if (remotes) {
				injectToken(remotes, Model.modelName, Model)
			}
		})
	}
}

function injectToken(remotes, modelName, Model) {
	remotes.before('**', async (ctx, next, method) => {
		// Use context utilities to ensure proper context isolation
		await runWithContext(async () => {
			const { accessToken } = Context,
				{ secretKey } = Model.app?.service || {}
			// Check if we might be using the wrong context
			// const existingToken = ctx.req?.headers?.[X_ACCESS_TOKEN]

			ctx.req.headers = ctx.req.headers || {}
			ctx.req.headers[X_ACCESS_TOKEN] = (accessToken && validateToken(accessToken, secretKey)) ? accessToken : Context.generateAccessToken()
		}, { operation: `${modelName}.${method.name}` })
	})
}

/**
 * Runs an async function with the current context preserved
 * This ensures the tenant context isn't lost during async operations
 *
 * @param {Function} fn Async function to run with preserved context
 * @param {Object} options Options for the operation
 * @param {String} options.operation Name of operation being performed (for logging)
 * @returns {Promise} Result of the function
 */
async function runWithContext(fn, { operation = 'unknown' } = {}) {
	const currentContext = Context.getCurrentContext()
	if (!currentContext) {
		debug(`No context available for operation: ${operation}`)
		return fn() // Just run the function normally if no context exists
	}

	const beforeTenant = Context.tenant
	let result
	try {
		// Safe context cloning that preserves data types and handles circular references
		const contextToUse = safeCloneContext(currentContext)
		result = await Context.runInContext(contextToUse, fn)
	}
	catch (error) {
		throw error
	}
	finally {
		// Verify context wasn't changed
		const afterTenant = Context.tenant
		if (beforeTenant !== afterTenant) {
			debug(`WARNING: Context changed after ${operation}, before: ${beforeTenant || 'none'}, after: ${afterTenant || 'none'}`)
		}
	}

	return result
}

/**
 * Safely clone context object while preserving data types and handling circular references
 * This approach is much safer than JSON serialization as it preserves:
 * - Date objects (as Date objects, not strings)
 * - Functions (if any exist in context)
 * - All other data types
 * @param {Object} context - The context object to clone
 * @returns {Object} - Safely cloned context
 */
function safeCloneContext(context) {
	const cloned = {}
	const seen = new WeakSet() // Track circular references

	function cloneValue(value, key) {
		// Handle null and undefined
		if (value === null || value === undefined) {
			return value
		}

		// Handle primitives (string, number, boolean, symbol)
		if (typeof value !== 'object' && typeof value !== 'function') {
			return value
		}

		// Handle functions - preserve them (context shouldn't have business logic functions)
		if (typeof value === 'function') {
			debug(`Preserving function in context: ${key}`)
			return value
		}

		// Handle circular references
		if (seen.has(value)) {
			debug(`Circular reference detected for key '${key}', skipping`)
			return undefined
		}

		// Handle Date objects - preserve as Date objects
		if (value instanceof Date) {
			return new Date(value.getTime())
		}

		// Handle RegExp objects - preserve as RegExp objects
		if (value instanceof RegExp) {
			return new RegExp(value.source, value.flags)
		}

		// Handle Error objects - create new Error with same message
		if (value instanceof Error) {
			const newError = new Error(value.message)
			newError.name = value.name
			newError.stack = value.stack
			return newError
		}

		// Handle Arrays
		if (Array.isArray(value)) {
			seen.add(value)
			const clonedArray = value.map((item, index) => cloneValue(item, `${key}[${index}]`))
			seen.delete(value)
			return clonedArray
		}

		// Handle plain objects
		if (typeof value === 'object') {
			seen.add(value)
			const clonedObj = {}

			for (const prop in value) {
				if (value.hasOwnProperty(prop)) {
					// Skip private properties that might cause issues
					if (prop.startsWith('_') && (prop === '_domain' || prop === '_circular')) {
						debug(`Skipping private property '${prop}' to avoid circular references`)
						continue
					}

					clonedObj[prop] = cloneValue(value[prop], `${key}.${prop}`)
				}
			}

			seen.delete(value)
			return clonedObj
		}

		return value
	}

	// Clone all context properties
	for (const key in context) {
		if (context.hasOwnProperty(key)) {
			// Skip known problematic properties at the top level
			if (key === '_domain' || key === '_circular') {
				debug(`Skipping problematic top-level property '${key}'`)
				continue
			}
			cloned[key] = cloneValue(context[key], key)
		}
	}

	return cloned
}

function validateToken(accessToken, secretKey) {
	if (!accessToken) return true
	const jwt = new Jwt(secretKey)
	let decodedJWT

	if (jwt.verify(accessToken)) {
		decodedJWT = jwt.decode(accessToken)
	}
	else return false

	const payload = (typeof decodedJWT.payload === 'string')
			? JSON.parse(decodedJWT.payload)
			: decodedJWT.payload,
		{ exp } = payload,
		now = Math.floor(Date.now() / 1000)
	if (exp && exp < now) console.log('[debug] Token expired', accessToken)
	return !exp || exp > now
}