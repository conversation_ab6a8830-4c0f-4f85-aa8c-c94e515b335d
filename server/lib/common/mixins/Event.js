/**
 * @module Mixin:Event
 */
const debug = require('debug')('multitenant:event'),
	{ touchpoint } = require('@perkd/touchpoints'),
	{ JSONPatch } = require('@perkd/sync'),
	{ compare } = JSONPatch

module.exports = function(Model, options = {}) {

	const { hook = {}, doUpsertOnly } = options
	let eventPrefix

	// -----  Instance Methods  -----

	if (!Model.pub) {
		Model.pub = function(action, data) {
			Model.app.emit(getName(Model, action), data)
		}
	}

	if (!Model.prototype.pub) {
		Model.prototype.pub = function(action, data) {
			const id = String(this.id)

			Model.app.emit(getName(Model, action), { ...this.toJSON(), ...data, id })
		}
	}

	// ---  Specific methods  ---

	function getPrefix(Model) {
		const { app, modelName } = Model

		if (!eventPrefix) {
			// 🔒 CRITICAL FIX: Handle case where service isn't ready - graceful fallback
			if (!app.service || !app.Service || !app.Service.eventbus) {
				// Use a default prefix during testing or when service isn't ready
				eventPrefix = 'crm.' + modelName.toLowerCase()
				debug('⚠️  Service not ready, using default event prefix:', eventPrefix)
			}
			else {
				eventPrefix = app.Service.eventbus.domain + '.' + modelName.toLowerCase()
			}
		}
		return eventPrefix
	}

	function getName(Model, action) {
		return getPrefix(Model) + '.' + action
	}

	// ---  Remote & Operation Hooks  ---

	if (hook.create || hook.update) {
		Model.observe('after doUpsert', async ({ hookState, isNewInstance, instance }) => {
			const { through: hookStateThrough, imageId } = hookState,
				through = hookStateThrough || touchpoint()

			if (isNewInstance && hook.create) {
				instance.pub('created', { through })
			}
			else if (hook.update) {
				// emit updated event when image is uploaded
				if (imageId) {
					const filter = Model.name === 'place' ? { include: [ 'photos' ] } : undefined, // only include photos for place model
						inst = await Model.findById(instance.id, filter)
					inst.pub('updated', { through, context: { delta: hookState.delta || [] } })
				}
				else instance.pub('updated', { through, context: { delta: hookState.delta || [] } })
			}
		})

		if (doUpsertOnly) return

		Model.observe('before save', async ({ instance, data, hookState, isNewInstance, currentInstance, options: opt }) => {
			if (!opt?.doUpsert){
				const { through = touchpoint(), deletedAt, hiddenAt } = instance || data

				hookState.through = through
				hookState.softDelete = hookState.softDelete || !!(currentInstance && !currentInstance.deletedAt && deletedAt)
				hookState.hide = hookState.hide || !!(currentInstance && !currentInstance.hiddenAt && hiddenAt)

				if (instance) {
					instance.unsetAttribute('through')
				}
				else {
					delete data.through
				}

				if (!isNewInstance && currentInstance && hook.update?.delta) {
					hookState.original = currentInstance.toJSON()
				}
			}
		})

		Model.observe('after save', async ({ hookState, instance, isNewInstance, options: opt }) => {
			if (!opt?.doUpsert){
				if (!instance) {
					console.log('debug [events] %j', { hookState, instance, isNewInstance })
					return
				}
				const through = hookState.through || touchpoint(),
					softDelete = !!(hookState.softDelete && hook.delete),
					hide = !!(hookState.hide && hook.update)

				if (softDelete) {
					const context = { softDelete }
					instance.pub('deleted', { through, context })
				}
				else if (hide) {
					const context = { hide }
					instance.pub('deleted', { through, context })
				}
				else if (isNewInstance && hook.create) {
					instance.pub('created', { through })
				}
				else if (!isNewInstance && hook.update) {
					if (hookState?.original && hook.update.delta) {
						const { original } = hookState,
							aft = instance.toJSON(),
							before = JSON.parse(JSON.stringify(original)),
							after = JSON.parse(JSON.stringify(aft)),
							delta = compare(before, after)

						return instance.pub('updated', { through, context: { delta } })
					}
					instance.pub('updated', { through })
				}
			}
		})
	}

	if (hook.delete) {
		Model.observe('before delete', async ({ where }) => {
			const instance = await Model.findById(where.id)
			instance?.pub('deleted')
		})
	}
}
