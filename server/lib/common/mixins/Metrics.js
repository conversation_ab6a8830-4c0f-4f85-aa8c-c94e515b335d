/**
 *  @module Mixin:Metrics
 *  <AUTHOR>
 */

//  Module Dependencies

module.exports = function(Model, options = {}) {
	// -----  Static Methods  -----

	const isMetrics = Model.name === 'Metrics'

	if (!isMetrics) {
		Model._metricsHandle = function(...args) {
			return Model.app.models.Metrics.handle(...args)
		}
		Model._metricsStream = function(...args) {
			return Model.app.models.Metrics.stream(...args)
		}
		Model._metricsAggregate = function(...args) {
			return Model.app.models.Metrics.aggregate(...args)
		}
		Model._metricsTimeseries = function(...args) {
			return Model.app.models.Metrics.timeseries(...args)
		}
		Model._metricsPause = function(...args) {
			return Model.app.models.Metrics.pause(...args)
		}
		Model._metricsResume = function(...args) {
			return Model.app.models.Metrics.resume(...args)
		}
		Model._metricsClose = function(...args) {
			return Model.app.models.Metrics.close(...args)
		}
		Model._metricsReset = function(...args) {
			return Model.app.models.Metrics.reset(...args)
		}
	}
	// -----  Instance Methods  -----

	// ---  Remote & Operation Hooks  ---

	Model.remoteMethod(isMetrics ? 'handle' : '_metricsHandle', {
		description: 'Handle event.',
		accessType: 'WRITE',
		http: { verb: 'post', path: isMetrics ? '/handle' : '/metrics/handle' },
		accepts: [
			{ arg: 'event', type: 'object' },
		],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod(isMetrics ? 'stream' : '_metricsStream', {
		description: 'Create a change stream.',
		accessType: 'READ',
		http: { verb: 'get', path: isMetrics ? '/stream' : '/metrics/stream' },
		accepts: [
			{ arg: 'metrics', type: 'array' },
			{ arg: 'filter', type: 'object' },
			{ arg: 'options', type: 'object' },
		],
		returns: { arg: 'changes', type: 'ReadableStream', json: true },
	})

	Model.remoteMethod(isMetrics ? 'aggregate' : '_metricsAggregate', {
		description: 'Fetch aggregate metrics data.',
		accessType: 'READ',
		http: { verb: 'get', path: isMetrics ? '/aggregate' : '/metrics/aggregate' },
		accepts: [
			{ arg: 'name', type: 'string', required: true },
			{ arg: 'type', type: 'string', enum: [ 'sum', 'count' ], required: true },
			{ arg: 'dimensions', type: 'array', required: true },
			{ arg: 'dValues', type: 'array', required: false },
			{ arg: 'filter', type: 'object', required: false },
		],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod(isMetrics ? 'timeseries' : '_metricsTimeseries', {
		description: 'Fetch timeseries metrics data.',
		accessType: 'READ',
		http: { verb: 'get', path: isMetrics ? '/timeseries' : '/metrics/timeseries' },
		accepts: [
			{ arg: 'name', type: 'string', required: true },
			{ arg: 'type', type: 'string', enum: [ 'sum', 'count', 'distinct' ], required: true },
			{ arg: 'dimensions', type: 'array', required: true },
			{ arg: 'dValues', type: 'array', required: false },
			{ arg: 'start', type: 'number', required: true },
			{ arg: 'end', type: 'number', required: true },
			{ arg: 'scale', type: 'string', enum: [ 'second', 'minute', 'hour', 'day', 'month', 'year' ], required: true },
		],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod(isMetrics ? 'pause' : '_metricsPause', {
		description: 'Pause tenant metrics handler.',
		accessType: 'READ',
		http: { verb: 'post', path: isMetrics ? '/pause' : '/metrics/pause' },
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod(isMetrics ? 'resume' : '_metricsResume', {
		description: 'Resume tenant metrics handler.',
		accessType: 'READ',
		http: { verb: 'post', path: isMetrics ? '/resume' : '/metrics/resume' },
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod(isMetrics ? 'close' : '_metricsClose', {
		description: 'Persist metrics data form cache and close cache.',
		accessType: 'READ',
		http: { verb: 'post', path: isMetrics ? '/close' : '/metrics/close' },
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod(isMetrics ? 'reset' : '_metricsReset', {
		description: 'Delete all metrics data from DB and reset metrics cache.',
		accessType: 'READ',
		http: { verb: 'post', path: isMetrics ? '/reset' : '/metrics/reset' },
		returns: { type: 'object', root: true },
	})
}
