/**
 *  @module Mixin:Image
 */
const http = require('node:http'),
	https = require('node:https'),
	{ gunzip, brotliDecompress } = require('node:zlib'),
	{ promisify } = require('node:util'),
	loopback = require('loopback'),
	{ Apis } = require('@crm/types'),
	{ Context } = require('@perkd/multitenant-context'),
	gunzipAsync = promisify(gunzip),
	brotliDecompressAsync = promisify(brotliDecompress)

const { TENANT } = Apis.Headers

module.exports = function(Model, options = {}) {

	const { model: modelName, relation: ImageRelation, description: methodDescription } = options,
		ImageModel = loopback.getModel(modelName),
		uploadMethod = ImageRelation + 'Upload',
		upsertMethod = 'upsert' + ImageRelation.charAt(0).toUpperCase() + ImageRelation.slice(1),
		deleteMethod = ImageRelation + 'Delete',
		cloneMethod = ImageRelation + 'Clone'

	// -----  Instance Methods  -----
	// Avoid use this function, use image service api directly, more efficiently
	Model.prototype[uploadMethod] = async function(imageId, req, res) {
		const { headers } = req,
			{ pluralModelName } = ImageModel,
			{ connector } = ImageModel.getDataSource(),
			{ url: apiBase } = connector,
			isHttps = apiBase.startsWith('https'),
			{ request: fetch } = isHttps ? https : http,
			url = new URL(`${apiBase}/${pluralModelName}/${imageId}/upload`),
			{ hostname, pathname, port } = url,
			requestOptions = {
				method: 'post',
				host: hostname,
				port,
				path: pathname,
				headers,
			}

		return new Promise((resolve, reject) => {
			const request = fetch(requestOptions)

			request.on('error', err => reject(err))
			request.on('response', response => {
				const { statusCode } = response
				let body = []

				response.on('data', chunk => {
					body.push(chunk)
				})
					.on('end', async () => {
						try {
							const buffer = Buffer.concat(body)
							let str

							// Handle different compression types
							switch (response.headers['content-encoding']) {
							case 'br': {
								const decompressed = await brotliDecompressAsync(buffer)
								str = decompressed.toString('utf8')
								break
							}
							case 'gzip': {
								const unzipped = await gunzipAsync(buffer)
								str = unzipped.toString('utf8')
								break
							}
							default:
								str = buffer.toString('utf8')
							}

							body = JSON.parse(str)

							if (statusCode === 200) {
								const ctx = {
									Model,
									isNewInstance: false,
									instance: this,
									data: {
										[ImageRelation]: [ body ]
									},
									hookState: { imageId }, // for place model to emit event with images
								}

								if (Model.doUpsert) {
									Model.notifyObserversOf('after doUpsert', ctx)
								}
								else {
									Model.notifyObserversOf('after save', ctx)
								}
								Model.notifyObserversOf('after imageUploaded', ctx)
								resolve(body)
							}
							else {
								const { error = {} } = body,
									{ message, statusCode: errorStatusCode, stack } = error,
									err = new Error(message)

								err.statusCode = errorStatusCode
								err.stack = stack
								reject(err)
							}
						}
						catch (err) {
							reject(err)
						}
					})
			})

			req.pipe(request)
		})
	}

	Model.prototype[upsertMethod] = async function(imageList, upsertOptions = {}) {
		const { id } = this,
			images = await new Promise((resolve, reject) => {
				this[ImageRelation]((err, imageResults) => {
					if (err) reject(err)
					else resolve(imageResults)
				})
			}),
			updates = []

		if (images.length) return images

		for (const image of imageList) {
			if (!image.id) continue

			const img = await ImageModel.findById(image.id).catch(() => undefined)

			if (img) {
				const instance = await img.updateAttributes({ ownerId: id }),
					context = {
						Model,
						isNewInstance: false,
						data: { id },
						hookState: {},
					}

				context.data[ImageRelation] = [ instance ]
				Model.notifyObserversOf('after save', context)

				updates.push(instance)
			}
		}

		return updates
	}

	Model.prototype[deleteMethod] = async function(imageId) {
		const ctx = {
			Model,
			isNewInstance: false,
			currentInstance: this,
			instance: this,
			where: {},
			hookState: {},
		}

		await ImageModel.destroyById(imageId).catch(() => null)

		Model.doUpsert ? Model.notifyObserversOf('after doUpsert', ctx) : Model.notifyObserversOf('after save', ctx)
		Model.notifyObserversOf('after imageDeleted', ctx)
	}

	Model.prototype[cloneMethod] = async function(imageId, ownerId) {
		const image = await ImageModel.findById(imageId)
		return image.clone(ownerId)
	}

	// -----  Remote hooks  -----

	ImageModel.on('dataSourceAttached', model => {
		const remotes = model.getDataSource().connector.remotes

		if (remotes) {
			remotes.before('**', (ctx, next) => {
				const { tenant } = Context,
					{ req } = ctx

				req.headers = req.headers || {}
				req.headers[TENANT] = tenant	// Multitenancy: inject tenantCode
				next()
			})
		}
	})

	// -----  Operation hooks  -----

	Model.observe('before delete', async ({ hookState }) => {
		const before = hookState._before

		if (before) {
			const images = await before[ImageRelation].find(),
				res = []

			for (const image of images) {
				res.push(
					await before[deleteMethod](image.id)
				)
			}

			return res
		}

		return undefined
	})

	// -----  Remote Methods  -----

	Model.remoteMethod('prototype.' + uploadMethod, {
		description: methodDescription,
		http: { path: '/' + ImageRelation + '/:fk/upload', verb: 'post' },
		accepts: [
			{ arg: 'fk', type: 'string', required: true, description: 'Foreign key for ' + ImageRelation },
			{ arg: 'req', type: 'object', http: { source: 'req' } },
			{ arg: 'res', type: 'object', http: { source: 'res' } },
		],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod('prototype.' + upsertMethod, {
		description: methodDescription,
		http: { path: '/' + upsertMethod, verb: 'post' },
		accepts: [ { arg: 'imageList', type: 'array', required: true }, { arg: 'options', type: 'object' } ],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod('prototype.' + deleteMethod, {
		description: 'Delete a related item by id for ' + ImageRelation + '.',
		http: { path: '/' + ImageRelation + '/:fk/delete', verb: 'delete' },
		accepts: [ { arg: 'fk', type: 'string', required: true, description: 'Foreign key for ' + ImageRelation } ],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod('prototype.' + cloneMethod, {
		description: 'Clone a related item by id for ' + ImageRelation + '.',
		http: { path: '/' + ImageRelation + '/:fk/clone', verb: 'post' },
		accepts: [ { arg: 'fk', type: 'string', required: true, description: 'Foreign key for ' + ImageRelation }, { arg: 'ownerId', type: 'string' } ],
		returns: { type: 'object', root: true },
	})
}
