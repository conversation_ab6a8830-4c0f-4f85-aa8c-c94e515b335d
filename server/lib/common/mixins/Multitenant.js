/**
 * @module Mixin:Multitenant
 * Handles multi-tenancy with dynamic MongoDB connection management
 *
 * TENANT ISOLATION: This mixin provides the getDataSource() method that returns
 * the correct tenant-specific datasource. Tenant isolation is ensured by
 * loopback-datasource-juggler v5.2.8+ which properly calls getDataSource()
 * for all database operations instead of using model.dataSource directly.
 */

const debug = require('debug')('multitenant'),
	{ ConnectionManager, TRAP } = require('@crm/loopback'),
	{ Context } = require('@perkd/multitenant-context'),
	{ TenantIsolationError } = require('@perkd/errors')

const config = require('../tenant/config/tenant.config')

const DEFAULT = 'default'

/**
 * Creates a proxy that delegates connector calls to the original connector
 * while maintaining tenant-specific connection properties
 * @param {Object} originalConnector - The original datasource connector
 * @param {Object} tenantDataSource - The tenant-specific datasource
 * @returns {Proxy} Proxy that handles connector method delegation
 */
function createConnectorProxy(originalConnector, tenantDataSource) {
	// Store reference to the original tenant connector before proxy replacement
	const originalTenantConnector = tenantDataSource.connector

	// Properties that should use tenant-specific values
	const TENANT_SPECIFIC_PROPS = new Set([
		'dataSource', 'client', 'db', 'settings'
	])

	const proxy = new Proxy(originalConnector, {
		get(target, prop) {
			// Simple delegation - no complex fallback logic needed
			if (prop === 'dataSource') {
				return tenantDataSource
			}

			// For other tenant-specific properties, use the original tenant connector
			// (before proxy replacement) to avoid infinite recursion
			if (TENANT_SPECIFIC_PROPS.has(prop)) {
				if (originalTenantConnector && originalTenantConnector[prop] !== undefined) {
					return originalTenantConnector[prop]
				}
			}

			// Special handling for collection method - needs to use tenant client
			if (prop === 'collection' && typeof target[prop] === 'function') {
				return function(modelName) {
					// Use tenant-specific db directly (the db object has the collection method)
					const db = originalTenantConnector?.db

					if (db && typeof db.collection === 'function') {
						return db.collection(modelName)
					}

					// Fallback to original implementation
					return target[prop].call(target, modelName)
				}
			}

			// For all other properties and methods, delegate to original connector
			const value = target[prop]

			// If it's a function, bind it to the original connector to maintain context
			if (typeof value === 'function') {
				return value.bind(target)
			}

			return value
		},

		set(target, prop, value) {
			// Allow setting properties on the original connector
			target[prop] = value
			return true
		},

		has(target, prop) {
			// Check both original connector and tenant-specific properties
			return prop in target || TENANT_SPECIFIC_PROPS.has(prop)
		},

		ownKeys(target) {
			// Return all keys from original connector plus tenant-specific ones
			const originalKeys = Reflect.ownKeys(target)
			const tenantKeys = Array.from(TENANT_SPECIFIC_PROPS)
			return [ ...new Set([ ...originalKeys, ...tenantKeys ]) ]
		}
	})

	// Mark the proxy so cleanup can identify it
	proxy._isProxy = true

	return proxy
}

/**
 * Connection cleanup
 * @param {DataSource} connection - The datasource connection to clean up
 */
const cleanupConnection = async connection => {
	debug('Cleaning up connection')
	try {
		// Skip if this is the base trap connection
		if (connection && connection.name === 'TRAP') {
			debug('Skipping cleanup for base TRAP connection')
			return
		}

		// First, clean up MongoDB client with aggressive cleanup
		const client = connection?.connector?.dataSource?.connector?.client
		if (client) {
			try {
				// 1. Close all active cursors and connection pools with proper callback handling
				if (client.topology && client.topology.s && client.topology.s.servers) {
					const servers = client.topology.s.servers
					for (const [ , server ] of servers) {
						if (server.s && server.s.pool && server.s.pool.close) {
							try {
								// Check if pool.close expects a callback
								if (server.s.pool.close.length > 0) {
									// Expects callback
									await new Promise(resolve => {
										server.s.pool.close(err => {
											if (err) {
												debug('Error in pool.close callback:', err)
											}
											resolve() // Always resolve, don't let pool errors crash cleanup
										})
									})
								}
								else {
									// Returns promise or is synchronous
									const result = server.s.pool.close()
									if (result && typeof result.then === 'function') {
										await result.catch(err => {
											debug('Error in pool.close promise:', err)
										})
									}
								}
							}
							catch (poolErr) {
								debug('Error closing connection pool:', poolErr)
							}
						}
					}
				}

				// 2. End all active sessions
				if (client.topology && client.topology.sessions) {
					const sessions = Array.from(client.topology.sessions.values())
					debug(`Ending ${sessions.length} active sessions for ${connection.name}`)

					for (const session of sessions) {
						try {
							if (session && session.endSession) {
								await session.endSession()
							}
						}
						catch (sessionErr) {
							debug('Error ending session:', sessionErr)
						}
					}

					// Clear the sessions map
					client.topology.sessions.clear()
				}

				// 3. Close any active change streams
				if (client.topology && client.topology.s && client.topology.s.options) {
					// Force close topology
					try {
						if (client.topology.close) {
							await client.topology.close(true)
						}
					}
					catch (topologyErr) {
						debug('Error closing topology:', topologyErr)
					}
				}

				// 4. Close the client with proper callback handling
				await new Promise(resolve => {
					// Check if close method expects a callback
					if (client.close.length > 1) {
						// Older MongoDB driver version that expects callback
						client.close(true, err => {
							if (err) {
								debug('Error in client.close callback:', err)
								resolve() // Don't reject, just log and continue
							}
							else {
								resolve()
							}
						})
					}
					else {
						// Newer version that returns a promise
						try {
							const result = client.close(true)
							if (result && typeof result.then === 'function') {
								result.then(resolve).catch(err => {
									debug('Error in client.close promise:', err)
									resolve() // Don't reject, just log and continue
								})
							}
							else {
								resolve()
							}
						}
						catch (err) {
							debug('Error calling client.close:', err)
							resolve() // Don't reject, just log and continue
						}
					}
				})
				debug(`MongoDB client closed for connection ${connection.name}`)
			}
			catch (closeErr) {
				debug('Error closing MongoDB client:', closeErr)
			}

			// 5. Aggressively null out all references
			if (client.topology) {
				if (client.topology.s) {
					client.topology.s.servers = null
					client.topology.s.sessionPool = null
					client.topology.s = null
				}
				client.topology.sessions = null
				client.topology = null
			}

			// 6. Clear client references
			if (connection.connector && connection.connector.dataSource && connection.connector.dataSource.connector) {
				connection.connector.dataSource.connector.client = null
			}
		}

		// Then, disconnect the connector with proper callback handling
		if (connection?.connector?.disconnect) {
			try {
				// Check if disconnect method expects a callback
				if (connection.connector.disconnect.length > 0) {
					// Expects callback
					await new Promise(resolve => {
						connection.connector.disconnect(err => {
							if (err) {
								debug('Error in connector.disconnect callback:', err)
							}
							resolve() // Always resolve, don't let disconnect errors crash the cleanup
						})
					})
				}
				else {
					// Returns promise or is synchronous
					const result = connection.connector.disconnect()
					if (result && typeof result.then === 'function') {
						await result.catch(err => {
							debug('Error in connector.disconnect promise:', err)
						})
					}
				}
				debug(`Connector disconnected for ${connection.name}`)
			}
			catch (disconnectErr) {
				debug('Error disconnecting connector:', disconnectErr)
			}
		}

		if (connection.app && connection.name) {
			// Extract tenant ID from connection name (the name is the tenant ID)
			// We don't need to do anything with it here, but it's useful for debugging
			debug(`Cleaning up connection for tenant: ${connection.name}`)
		}

		// Clean up model registry proxy references
		if (connection._modelRegistryProxy) {
			debug(`Cleaning up model registry proxy for ${connection.name}`)
			connection._modelRegistryProxy = null
		}

		// Clean up models property to break circular references
		if (connection.models && typeof connection.models === 'object') {
			// Clear model references but preserve the proxy structure
			debug(`Clearing model references for ${connection.name}`)
			try {
				// Don't directly assign to models property as it's now deprecated
				// Instead, clear the internal proxy reference if it exists
				if (connection._modelRegistryProxy) {
					connection._modelRegistryProxy = null
				}
				// Clear individual model references through the proxy if possible
				const modelNames = Object.keys(connection.models)
				for (const modelName of modelNames) {
					try {
						delete connection.models[modelName]
					}
					catch (deleteErr) {
						// Ignore errors when trying to delete from proxy
						debug(`Could not delete model ${modelName}:`, deleteErr.message)
					}
				}
			}
			catch (err) {
				debug('Error clearing models reference:', err.message)
			}
		}

		// Finally, clean up event listeners and references
		if (connection && typeof connection.removeAllListeners === 'function') {
			connection.removeAllListeners()
			debug(`Removed all listeners from connection ${connection.name}`)
		}

		// SIMPLE & SECURE CLEANUP: Remove connection from ConnectionManager
		// This ensures getExistingConnection() returns null for cleaned-up tenants
		// Model.getDataSource() will then throw TenantIsolationError (secure behavior)
		if (connection.connector) {
			connection.connector.dataSource = null
			debug(`Cleaned up connector dataSource for ${connection.name}`)
		}

		// Remove connection from ConnectionManager to prevent reuse
		if (connection.app?.connectionManager && connection.name) {
			const connectionManager = connection.app.connectionManager

			// Remove from internal connection storage
			if (connectionManager._connections && connectionManager._connections[connection.name]) {
				delete connectionManager._connections[connection.name]
				debug(`Removed connection ${connection.name} from ConnectionManager._connections`)
			}
			if (connectionManager.connections && connectionManager.connections[connection.name]) {
				delete connectionManager.connections[connection.name]
				debug(`Removed connection ${connection.name} from ConnectionManager.connections`)
			}

			// Remove from tenants array if it exists
			if (connectionManager.tenants && Array.isArray(connectionManager.tenants)) {
				const index = connectionManager.tenants.indexOf(connection.name)
				if (index > -1) {
					connectionManager.tenants.splice(index, 1)
					debug(`Removed tenant ${connection.name} from ConnectionManager.tenants`)
				}
			}
		}

		// Clean up app reference to break circular dependency
		if (connection.app) {
			connection.app = null
			debug(`Cleared app reference for ${connection.name}`)
		}

		debug(`Connection ${connection.name || 'unknown'} cleanup completed`)

		// Force garbage collection after significant cleanup
		if (global.gc) {
			global.gc()
			debug('Forced garbage collection after connection cleanup')
		}
	}
	catch (err) {
		debug('Error during connection cleanup:', err)
		// No need to return anything, this is an async function
	}
}

const validateConnection = async connection => {
	try {
		// Check if db.command expects a callback
		if (connection.db && connection.db.command) {
			if (connection.db.command.length > 1) {
				// Expects callback
				await new Promise((resolve, reject) => {
					connection.db.command({ ping: 1 }, (err, result) => {
						if (err) reject(err)
						else resolve(result)
					})
				})
			}
			else {
				// Returns promise
				await connection.db.command({ ping: 1 })
			}
		}
		return true
	}
	catch (err) {
		debug('Connection validation failed:', err)
		return false
	}
}

const isTransactionSupported = () =>
	// Simply check if DB_SET environment variable is set, only replica set support transaction
	!!process.env.DB_SET

/**
 * Ensures that built-in models are available in the registry
 * @param {Object} app - The application instance
 */
const ensureBuiltinModelsExist = app => {
	if (!app || !app.registry || !app.registry.modelBuilder || !app.registry.modelBuilder.models) {
		debug('Cannot ensure builtin models - app registry not available')
		return
	}

	// List of builtin models that should be available
	const builtinModels = [
		'AccessToken', 'User', 'Role', 'RoleMapping', 'ACL'
	]

	// Check if each builtin model exists
	let restoredCount = 0
	for (const modelName of builtinModels) {
		if (!app.registry.modelBuilder.models[modelName] && app.models[modelName]) {
			// Model exists in app.models but not in registry - restore it
			app.registry.modelBuilder.models[modelName] = app.models[modelName]
			restoredCount++
			debug(`Restored builtin model ${modelName} to registry`)
		}
	}

	if (restoredCount > 0) {
		debug(`Ensured ${restoredCount} builtin models are available in registry`)
	}
}

// Add setAppReference method to ConnectionManager.prototype if it doesn't exist
if (typeof ConnectionManager.prototype.setAppReference !== 'function') {
	ConnectionManager.prototype.setAppReference = function(app) {
		debug('Setting app reference for connection manager')
		this.app = app

		// We need to access the connections from the connection manager
		// Since getAllConnections might not be available, use getExistingConnection for each tenant
		if (this.tenants && Array.isArray(this.tenants)) {
			// If tenants array is available, use it to get connections for each tenant
			this.tenants.forEach(tenant => {
				const connection = this.getExistingConnection(tenant)
				if (connection) {
					connection.app = app
					debug(`Set app reference on connection for tenant ${tenant}`)
				}
			})
		}
		else if (this._connections || this.connections) {
			// If _connections or connections object is available, use it directly
			const connectionsObj = this._connections || this.connections || {}
			Object.values(connectionsObj).forEach(connection => {
				if (connection) {
					connection.app = app
					debug(`Set app reference on connection ${connection.name || 'unknown'}`)
				}
			})
		}
		else {
			debug('Unable to access connections directly from connection manager')
		}
	}
}

/**
 * @param {Model} Model - The model to be mixed in
 * @param {Object} mixinOptions - allowNonTenant: boolean
 */
module.exports = function(Model, mixinOptions = {}) {
	debug('🔧 Multitenant mixin applied to model:', Model.modelName)
	const originalDataSource = Model.dataSource
	debug('🔧 Original datasource for', Model.modelName, ':', originalDataSource?.name)

	// Override the instance getDataSource method to ensure it always uses current tenant context
	Model.prototype.getDataSource = function() {
		// Use the class-level method which properly checks tenant context
		// This ensures we always use the current tenant's datasource, not the one at instance creation time
		return this.constructor.getDataSource()
	}

	// Initialize connection manager if not exists
	Model.on('attached', async () => {
		const { app } = Model,
			{ maxPoolSize } = config.connection ?? {},
			ds = app.dataSources[TRAP] || app.dataSources[DEFAULT],
			options = {
				validateConnection,
				cleanupConnection,
				connectionFactory: async tenant => {
					const settings = {
							...ds.settings,
							name: tenant,
							tenant,
							url: ds.settings.url.replace(`/${TRAP}?`, `/${tenant}?`),
							maxPoolSize,
							// Add connection lifecycle hooks
							onConnect: () => {
								debug(`Connected to tenant ${tenant}`)
							},
							onDisconnect: () => {
								debug(`Disconnected from tenant ${tenant}`)
							}
						},
						pool = app.registry.createDataSource(settings)

					// Set app reference for proper model cleanup
					pool.app = app

					// ROBUST FIX: Use proxy-based connector delegation
					// automatically handles all connector methods while maintaining tenant-specific overrides
					if (originalDataSource?.connector) {
						pool.connector = createConnectorProxy(originalDataSource.connector, pool)

						// Ensure datasource models reference is maintained
						if (originalDataSource._models) {
							pool._models = originalDataSource._models
						}
					}

					// Wait for connection to be established
					await new Promise((resolve, reject) => {
						// Add timeout to prevent hanging
						const timeout = setTimeout(() => {
							reject(new Error(`Connection timeout for tenant ${tenant}`))
						}, 30000) // 30 seconds timeout

						const cleanup = () => {
							clearTimeout(timeout)
							pool.removeListener('connected', onConnected)
							pool.removeListener('error', onError)
						}

						const onConnected = () => {
							cleanup()
							resolve()
						}

						const onError = err => {
							cleanup()
							reject(err)
						}

						if (pool.connected) {
							cleanup()
							resolve()
						}
						else {
							pool.once('connected', onConnected)
							pool.once('error', onError)
						}
					})

					return pool
				}
			}

		// Track model for later attachment to tenant datasources
		if (!app.tenantModels) {
			app.tenantModels = new Set()
		}
		app.tenantModels.add(Model)

		// Initialize connection manager immediately
		if (!app.connectionManager) {
			app.connectionManager = new ConnectionManager(config, options)
			await app.connectionManager.initialize()

			// Ensure connections have reference to app for proper model cleanup
			app.connectionManager.setAppReference(app)

			// Add global cleanup capability
			app.cleanupAllConnections = () => cleanupAllConnections(app)

			// Add context metrics cleanup capability
			app.cleanupContextMetrics = cleanupContextMetrics

			// Add EventBus cleanup capability
			if (app.Service && app.Service.eventbus) {
				app.cleanupEventBusListeners = () => {
					if (typeof app.Service.eventbus.cleanupAllListeners === 'function') {
						return app.Service.eventbus.cleanupAllListeners()
					}
					return 0
				}
			}

			// Set up periodic cleanup for load testing environments
			if (process.env.NODE_ENV === 'test') {
				const cleanupInterval = setInterval(() => {
					debug('Performing periodic cleanup for load testing')
					cleanupContextMetrics()

					// Clean up EventBus listeners periodically
					if (app.cleanupEventBusListeners) {
						const cleaned = app.cleanupEventBusListeners()
						debug(`Periodic cleanup: ${cleaned} event listeners cleared`)
					}

					// Force garbage collection if available
					if (global.gc) {
						global.gc()
					}
				}, 30000) // Every 30 seconds during testing

				// Store interval reference for cleanup
				app._loadTestCleanupInterval = cleanupInterval
			}

			// Ensure builtin models are properly registered
			ensureBuiltinModelsExist(app)

			debug('Connection manager initialized')
		}
	})

	/**
	 * Get datasource for the current tenant
	 * Uses loopback-datasource-juggler v5.2.8+ which properly calls this method
	 * for all database operations, ensuring correct tenant isolation.
	 * @returns {DataSource}
	 * @throws {TenantIsolationError} When connection is not available
	 */
	Model.getDataSource = function() {
		const { app } = Model,
			{ connectionManager } = app,
			{ tenant } = Context

		// Handle fallback to original datasource when no tenant context
		if (!tenant || tenant === 'trap') {
			debug('No tenant context available, using original datasource')
			debug('Tenant:', tenant)
			return originalDataSource
		}

		// 🔒 CRITICAL FIX: Handle service tenant specially
		// The "service" tenant is used for bootstrap and system operations
		// It should use the original datasource, not go through connection manager
		if (tenant === 'service') {
			debug('Service tenant detected, using original datasource for system operations')
			return originalDataSource
		}

		debug('Using tenant datasource:', tenant)

		if (!connectionManager) {
			throw new TenantIsolationError('Connection manager not initialized')
		}

		// Check initialization state from connection manager
		if (connectionManager.isInitializing) {
			debug('Waiting for connection manager initialization')
			throw new TenantIsolationError('Connection manager still initializing')
		}

		const { isError } = connectionManager
		if (isError) {
			debug('Connection manager failed:', isError)
			throw isError
		}

		// Try to get existing connection
		const pool = connectionManager.getExistingConnection(tenant)
		if (!pool) {
			debug('No existing connection for tenant:', tenant)
			throw new TenantIsolationError(`No connection available for tenant: ${tenant}`)
		}

		return pool
	}

	// Operation hooks with transaction support
	Model.observe('before save', async ({ options = {} }) => {
		const { connectionManager } = Model.app,
			{ tenant } = Context,
			{ mode } = config.session

		if (!tenant || options.session) return

		if (!connectionManager) {
			throw new TenantIsolationError('Connection manager not available')
		}

		const pool = await getConnectionPool()
		if (!pool.connector?.dataSource?.connector?.client) {
			debug('No MongoDB client available')
			// We need to modify the instance or data directly instead of returning
			return
		}

		// Check if transactions are supported based on DB_SET environment variable
		if (!isTransactionSupported() || mode === 'disabled') {
			debug('Transactions not supported - running in standalone mode')
			// We need to modify the instance or data directly instead of returning
			return
		}

		const client = pool.connector.dataSource.connector.client
		const session = client.startSession({
			defaultTransactionOptions: Context.get('txOptions')
		})
		options.session = session

		if (session) {
			await session.startTransaction()
		}

		// No need to return anything, we've modified options directly
	})

	Model.observe('after save', async ({ options = {} }) => {
		const { connectionManager } = Model.app,
			{ tenant } = Context,
			{ session, parentSession } = options

		if (!tenant || !session) return

		try {
			if (!parentSession && session && session.inTransaction()) {
				await session.commitTransaction()
			}
		}
		catch (err) {
			debug('Error ending session:', err)
			if (session && session.inTransaction()) {
				try {
					await session.abortTransaction()
				}
				catch (abortErr) {
					debug('Error aborting transaction:', abortErr)
				}
			}
			throw err
		}
		finally {
			try {
				await session.endSession()
			}
			catch (endErr) {
				debug('Error ending session:', endErr)
			}
			delete options.session
			connectionManager.recordCompletion(tenant)
		}
	})

	// Add transaction helper methods
	Model.withTransaction = async function(operation) {
		const { connectionManager } = Model.app,
			{ tenant } = Context

		if (!tenant || tenant === 'service') {
			if (!mixinOptions.allowNonTenant) {
				throw new TenantIsolationError('No tenant context for transaction')
			}
			return operation()
		}

		return connectionManager.withTransaction(tenant, operation)
	}

	Model.ensureConnection = async function() {
		const { connectionManager } = Model.app,
			{ tenant } = Context

		if (!tenant || tenant === 'service') {
			if (!mixinOptions.allowNonTenant) {
				throw new TenantIsolationError('No tenant context for connection')
			}
			return originalDataSource
		}

		return connectionManager.ensureConnection(tenant)
	}

	async function getConnectionPool() {
		const { tenant } = Context

		if (!tenant) {
			throw new TenantIsolationError('No tenant in context')
		}

		// Handle service tenant specially - return original datasource
		if (tenant === 'service') {
			debug('Service tenant detected in getConnectionPool, returning original datasource')
			return originalDataSource
		}

		trackConnectionUsage()
		assertTenantContext()

		const { app } = Model
		if (!app.connectionManager) {
			throw new TenantIsolationError('Connection manager not initialized')
		}

		return app.connectionManager.ensureConnection(tenant)
	}

	function assertTenantContext() {
		if (process.env.CONTEXT_MODE === 'strict') {
			const { tenant } = Context
			if (!tenant || tenant === 'service') {
				// Allow service tenant in strict mode as it's used for system operations
				if (tenant === 'service') {
					debug('Service tenant allowed in strict context mode')
					return
				}
				throw new TenantIsolationError(
					'Strict context mode requires explicit tenant - '
					+ 'Add context middleware earlier in the chain'
				)
			}
		}
	}

	function trackConnectionUsage() {
		// 🔒 CRITICAL FIX: Don't call getCurrentContext() during startup
		// This was causing trap contexts to be created during model initialization
		try {
			// Only track if we're in an active request context (not during startup)
			if (Context.tenant && Context.tenant !== 'service') {
				const ctx = Context.getCurrentContext()
				if (ctx && ctx.metrics) {
					ctx.metrics.connectionCount = (ctx.metrics.connectionCount || 0) + 1

					// Prevent metrics from growing indefinitely during load testing
					if (process.env.NODE_ENV === 'test' && ctx.metrics.connectionCount > 1000) {
						debug('Resetting connection count metrics to prevent memory accumulation')
						ctx.metrics.connectionCount = 1
					}
				}
			}
		}
		catch (error) {
			// Silently ignore context errors during startup
			debug('Context tracking skipped during startup:', error.message)
		}
	}

	/**
	 * Clean up context metrics for all active contexts
	 */
	function cleanupContextMetrics() {
		try {
			const currentContext = Context.getCurrentContext()
			if (currentContext && currentContext.metrics) {
				const beforeSize = Object.keys(currentContext.metrics).length
				// Reset metrics instead of deleting to maintain structure
				currentContext.metrics = {
					connectionCount: 0,
					requestCount: 0,
					errorCount: 0,
					lastActivity: null
				}
				debug(`Reset context metrics (${beforeSize} properties)`)
			}
		}
		catch (err) {
			debug('Error cleaning up context metrics:', err)
		}
	}

	// Add global cleanup function for all connections
	const cleanupAllConnections = async app => {
		if (!app || !app.connectionManager) return

		try {
			// Get connections from the connection manager using available methods
			const connectionManager = app.connectionManager
			let connections = []

			// Try different ways to access connections
			if (typeof connectionManager.getAllConnections === 'function') {
				// If getAllConnections is available, use it
				connections = connectionManager.getAllConnections() || []
				debug(`Found ${connections.length} connections using getAllConnections`)
			}
			else if (connectionManager.tenants && Array.isArray(connectionManager.tenants)) {
				// If tenants array is available, get connections for each tenant
				for (const tenant of connectionManager.tenants) {
					const connection = connectionManager.getExistingConnection(tenant)
					if (connection) {
						connections.push(connection)
					}
				}
				debug(`Found ${connections.length} connections by iterating tenants`)
			}
			else if (connectionManager._connections || connectionManager.connections) {
				// If _connections or connections object is available, use it directly
				const connectionsObj = connectionManager._connections || connectionManager.connections || {}
				connections = Object.values(connectionsObj).filter(Boolean)
				debug(`Found ${connections.length} connections from internal connection store`)
			}
			else {
				debug('Unable to access connections from connection manager')
				return
			}

			let totalCleaned = 0

			for (const connection of connections) {
				if (connection && connection.name !== 'TRAP') {
					const cleaned = await cleanupConnection(connection)
					totalCleaned += cleaned || 0
				}
			}

			// Clean up model registry entries for closed connections
			try {
				// Try to access ModelRegistry for cleanup
				const { ModelRegistry } = require('loopback-datasource-juggler')
				if (ModelRegistry && typeof ModelRegistry.getStats === 'function') {
					const statsBefore = ModelRegistry.getStats()
					debug('Model registry stats before cleanup:', statsBefore)

					// Clean up tenant registries for closed connections
					if (typeof ModelRegistry.cleanupTenant === 'function') {
						for (const connection of connections) {
							if (connection && connection.name !== 'TRAP') {
								// Use the stable DataSource ID for proper cleanup
								const tenantCode = connection._dsId ? `ds_${connection._dsId}` : `ds_${connection.name}`
								// Pass the DataSource reference for proper reference counting
								const cleanedModels = ModelRegistry.cleanupTenant(tenantCode, connection)
								debug(`Cleaned up model registry for tenant: ${tenantCode} (${cleanedModels} models)`)
							}
						}
					}

					const statsAfter = ModelRegistry.getStats()
					debug('Model registry stats after cleanup:', statsAfter)
				}
			}
			catch (registryErr) {
				debug('Error cleaning up model registry:', registryErr.message)
			}

			// Force garbage collection after global cleanup
			if (global.gc) {
				global.gc()
				debug('Forced garbage collection after global cleanup')
			}
		}
		catch (err) {
			debug('Error during global cleanup:', err)
		}
	}

	// Add cleanup function to stop periodic cleanup
	Model.stopLoadTestCleanup = function() {
		const { app } = Model
		if (app._loadTestCleanupInterval) {
			clearInterval(app._loadTestCleanupInterval)
			app._loadTestCleanupInterval = null
			debug('Stopped periodic load test cleanup')
		}
	}

	return Model
}
