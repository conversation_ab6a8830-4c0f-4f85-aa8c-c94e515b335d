/**
 *  @module Mixin:Buy
 *
 * 	IMPORTANT:	Host model must implement:
 * 		Payment mixin:
 * 							paymentRequest()
 * 							paymentAuthorize()
 * 							paymentCapture()
 * 							paymentCancel()
 * 							paymentRefund()
 *
 * 		Order mixin:
 * 							orderPrepare()
 * 							orderQualify()
 * 						 	orderApplyQualified()
 * 						 	orderCreate()
 * 						 	orderFulfill()
 * 						 	orderWitdrawQualified()
 */

const assert = require('node:assert'),
	{ Payments, Touchpoints, Orders, Contacts } = require('@crm/types'),
	{ MOrder, buildResponseToApp, updatePaymentWithTransaction, updateBillingWithTransaction, billingsToPayments } = require('@perkd/commerce'),
	{ ORDER: ORDER_ERR, PAYMENT: PAYMENT_ERR } = require('@perkd/errors/dist/service')

const { PAID, AUTHORIZED, CANCELLED, FAILED } = Payments.Status,
	{ PENDING } = Orders.Status,
	{ CRM } = Touchpoints.Type,
	{ SYSTEM } = Touchpoints.Attributed,
	{ MOBILE } = Contacts.Type,
	{ ORDER_INVALID, ORDER_FAILED, ORDER_PAY_FAILED } = ORDER_ERR,
	{ PAYMENT_CANCELED } = PAYMENT_ERR,
	Q_ORDER = 'order'

module.exports = function(Model) {

	/**
	 * Order only (no payment)
	 * @param	{String} [userId] - perkd personId, omit for anonymous (web) orders
	 * @param	{Object} order (@perkd/commerce)
	 * @param	{Object[]} pricings  (@perkd/commerce)
	 * @param	{Object} options - contains params for orderQualify
	 * 			{String} description
	 * 			{Object} metadata
	 * 			{String} masterId - where applicable
	 * 			{String} cardId	- when buying products: cardId of card used for purchase
	 * 							- when buying membership: preissued card with membership, ie. issue-pay-register flow
	 * 			{Object} reservation - { id: '', queueNumber: 123, referenceId: '' }
	 * 			{Object} through - set in 'shop' widget param
	 * 				{String} type - 'web'
	 * 				{String} format - 'menu'
	 * 				{Object} attributedTo
	 * 					{String} type - 'shop'
	 * 					{String} name - 'estninja-test.myshopify.com'
	 * 			{Object} notification - for orderFulfill
	 *			{Boolean} noNotify - for orderFulfill
	 * @return	{Object} { orderId }
	 */
	Model.order = async function(userId, order, pricings, options) {
		const mOrder = await this.orderPrepare(userId, [], order, pricings, { ...options, skipPayments: true }),
			doOrder = async () => {
				await this.orderQualify(mOrder)

				const entitlements = await this.orderApplyQualified(mOrder),
					orderCreated = await this.orderCreate(mOrder, [], entitlements),
					fulfilled = await this.orderFulfill(orderCreated, entitlements, options),
					{ id: orderId } = orderCreated

				return { orderId, fulfilled }
			}

		try {
			const res = userId
				? await this.queue(`${Q_ORDER}:${userId}`, doOrder)
				: await doOrder()

			return res
		}
		catch (error) {
			console.error(ORDER_FAILED, { error })
			throw error
		}
	}

	/**
	 * Make immediate purchase with payment modes or get payment source
	 * @param	{String} userId - perkd personId
	 * @param	{Object[]} payments
	 * 			{String} method - card, applepay, googlepay, alipay, linepay, grabpay. manual etc.
	 * 			{String} provider - stripe, mypay etc.
	 * 			{Object} [intent] - { paymentMethodId: pm_1Ic06yLFbSgjvNHiKbbWK2oj, cardId, offerId }
	 * @param	{MOrder} order (mini) order  (see commerceLib)
	 * @param	{Object[]} pricings  (see commerceLib)
	 * @param	{Object} options
	 * 			{String} description
	 * 			{Object} metadata
	 * 			{String} masterId - where applicable
	 * 			{String} cardId	- when buying products: cardId of card used for purchase
	 * 							- when buying membership: preissued card with membership, ie. issue-pay-register flow
	 * 			{Object} reservation - { id: '', queueNumber: 123, referenceId: '' }
	 * 			{Boolean} capture - used by manual payment
	 * 							true => staff already authorized payment via QR-Code
	 * 							false => need staff authorization/capture later (with widget)
	 * 			{Object} through - set in 'shop' widget param
	 * 				{String} type - 'web'
	 * 				{String} format - 'menu'
	 * 				{Object} attributedTo
	 * 					{String} type - 'shop'
	 * 					{String} name - 'estninja-test.myshopify.com'
	 * 			{Object} notification - for orderFulfill
	 *			{Boolean} noNotify - for orderFulfill
	 * @return	{Object} { payment: { intent, source, pendingId, expiresAt }, fulfilled: [] } - fulfilled omitted if payment_pending
	 */
	Model.orderPay = async function(userId, payments, order, pricings, options) {
		appNotify('[orderPay]', { order, pricings, options }, null, '-order')

		order.sourceType = Model.name
		const mOrder = await this.orderPrepare(userId, payments, order, pricings, options)

		return this.queue(`${Q_ORDER}:${userId}`, async () => {
			const qualified = await this.orderQualify(mOrder),
				parts = [ ...mOrder.payments ],
				last = parts.pop(),
				entitlements = [],
				authorized = []		// list of [ payment, transaction ]

			try {
				// 1. authorize inital parts
				for (const payment of parts) {
					const transaction = await this.paymentAuthorize(mOrder, payment)

					updatePaymentWithTransaction(payment, transaction)
					authorized.push([ payment, transaction ])
				}
				// 2. auto-capture last part  (last is undefined for zero net amount order, ie. no payment)
				const lastTransaction = last ? await this.paymentRequest(mOrder, last) : undefined,
					{ status = PAID } = lastTransaction || {}

				if (lastTransaction) {
					updatePaymentWithTransaction(last, lastTransaction)
				}

				if (status === AUTHORIZED || status === PAID) {
					// 3a. apply qualified, capture all & fulfill
					const payments = [ ...parts, last ].filter(Boolean),		// last may be undefined when zero net amount
						applied = await this.orderApplyQualified(mOrder)

					entitlements.push(...applied)

					for (const [ payment, transaction ] of authorized) {
						const captured = await this.paymentCapture(transaction)
						updatePaymentWithTransaction(payment, captured)
					}
					const orderCreated = await this.orderCreate(mOrder, payments, entitlements),
						R = buildResponseToApp(mOrder, last, lastTransaction)

					R.fulfilled = await this.orderFulfill(orderCreated, entitlements, options)
					return R
				}

				// 3b. last payment pending/async (Alipay, GrabPay, LinePay, etc)
				const payments = [ ...parts, last ].filter(Boolean)		// remove undefined
				await this.orderCreate(mOrder, payments, qualified)
				return buildResponseToApp(mOrder, last, lastTransaction)
			}
			catch (err) {
				const { message, statusCode } = err || {}
				if ((message?.startsWith('Unhandled error') || statusCode === 500) && parts.length && authorized.length) {
					const payments = [ ...parts, last ].filter(Boolean)
					await this.orderCreate(mOrder, payments, qualified)
					throw err
				}

				appNotify('[orderPay]rollback', { err, parts, authorized }, 'error')

				this.orderWithdrawQualified(mOrder, qualified, entitlements)

				for (const [ payment, transaction ] of authorized) {
					await this.paymentCancel(transaction).catch(e => null)	// suppress error
				}
				throw err
			}
		})
			.catch(error => {
				console.error(ORDER_PAY_FAILED, { error })
				throw error
			})
	}

	if (!Model.orderPrepare) {
		Model.orderPrepare = async function(userId, payments, order, pricings, options) {
			order.sourceType = Model.name
			await Model.injectRecipientProfile(userId, order)

			const mOrder = new MOrder(order, pricings, payments, userId, options),
				valid = mOrder.validate()

			return valid === true
				? mOrder
				: Model.rejectErr(ORDER_INVALID, { err: valid }, true)
		}
	}

	if (!Model.orderPaid) {
		/**
		 * Mark Order instance as Paid				(used by Buy mixin)
		 * @param	{mOrder} mOrder
		 * @param	{Object[]} payments
		 * @param	{Object[]} entitlements - not used.  returned by orderApplyQualified()
		 * @return	{Order} order
		 */
		Model.orderPaid = async function(mOrder, payments, entitlements) {
			const { Order } = this.app.models,
				data = mOrder.buildOrder(payments)

			return Order.markPaid(data)
		}
	}

	// -----  Placeholders (to be implemented by host model)

	if (!Model.orderQualify) {
		/**
		 * Qualify order for purchase				(used by Buy mixin)
		 * @param	{mOrder} mOrder
		 * @return	{Object[]} list of qualified - eg. profile of qualified person, used for creating order
		 */
		Model.orderQualify = async function(mOrder) {
			return []
		}
	}

	if (!Model.orderApplyQualified) {
		/**
		 * Apply Qualified benefit for order, eg. issue membership		(used by Buy mixin)
		 * @param	{mOrder} mOrder
		 * @return	{Object[]} entitlements - list of custom data, eg. list of membership issued, used for creating order
		 */
		Model.orderApplyQualified = async function(mOrder) {
			return []
		}
	}

	if (!Model.orderWithdrawQualified) {
		/**
		 * Reverse applied benefit for order, eg. cancelling membership	(used by Buy mixin)
		 * @param	{mOrder} mOrder
		 * @param	{Object[]} qualified - returned by orderQualify()
		 * @param	{Object[]} entitlements - returned by orderAppliedQualified()
		 */
		Model.orderWithdrawQualified = async function(mOrder, qualified, entitlements) {
			return
		}
	}

	if (!Model.orderCreate) {
		/**
		 * Create order								(used by Buy mixin)
		 * @param	{mOrder} mOrder
		 * @param	{Object[]} payments
		 * @param	{Object[]} qualifiedOrEntitlements
		 * @return	{Order} order
		 */
		Model.orderCreate = async function(mOrder, payments, qualifiedOrEntitlements) {
			return
		}
	}

	if (!Model.orderFulfill) {
		/**
		 * Fulfill paid order						(used by Buy mixin)
		 * @param	{Order} order - CRM
		 * @param	{Object[]} entitlements
		 * @param	{Object} options
		 *			{Object} notification
	 	 *			{Boolean} noNotify
		 * @return	{Order} order
		 */
		Model.orderFulfill = async function(order, entitlements, options) {
			return
		}
	}

	/**
	 * Inject into order:
	 * 	- personId
	 * 	- recipient profile (gender & profileImageUrl) into fulfillment.recipient
	 * @param {String} userId
	 * @param {Object} order - mutates fulfillment.recipient
	 */
	Model.injectRecipientProfile = async function(userId, order = {}) {
		const { Person } = this.app.models,
			{ fulfillment } = order,
			person = userId ? await Person.findOneByUserId(userId) : undefined,
			{ fullName, gender, profileImageUrl, phoneList = [] } = person ?? {}

		order.personId = person?.id

		if (fulfillment && userId) {
			const { recipient = {} } = fulfillment

			recipient.fullName || (recipient.fullName = fullName)
			recipient.gender = gender
			recipient.phone || (recipient.phone = phoneList.find(phone => phone.type === MOBILE)?.fullNumber)
			recipient.profileImageUrl = profileImageUrl
			order.fulfillment.recipient = recipient
		}
	}

	// -----  Methods required by Payment mixin  -----

	/**
	 * Commit (pending) Order, created earlier			(used by BuyApi mixin & handlePaymentEvent)
	 * 		Note: triggered via both events & app API
	 * @param	{Object} transaction
	 *			{String} referenceId
	 *			{String} status
	 * @param	{Object} options
	 *			{String} through
	 * @return	{Object} { payment: {}, payments: [], fulfilled: [] }
	 */
	Model.orderCommit = async function(transaction, options = {}) {
		const { models, service } = this.app,
			{ Order } = models,
			serviceName = service.name.toLowerCase(),
			{ status, referenceId } = transaction,
			entitlements = [],
			{ through } = options

		return this.queue(`${Q_ORDER}:${referenceId}`, async () => {
			const order = await Order.findByTransaction(referenceId, Model.name),
				{ id, billingList } = order || {},
				NOW = new Date()

			if (!order) return undefined		// not ours

			if (order.status !== PENDING) {
				// already committed due to race condition between app & provider event
				const payments = billingsToPayments(billingList)
				return buildResponseToApp({ id, payments }, {}, transaction)
			}

			try {
				switch (status) {
				case PAID:
				case AUTHORIZED: {
					updateBillingWithTransaction(billingList, transaction)

					const applied = await this.orderApplyQualified(order),
						captures = []

					entitlements.push(...applied)

					// capture any pending payments
					for (const billing of billingList) {
						const { paymentMethod } = billing,
							{ status, transactions = [] } = paymentMethod,
							[ latest ] = transactions

						if (latest && status === AUTHORIZED) {
							captures.push(
								this.paymentCapture(latest).then(captured => {
									updateBillingWithTransaction(billingList, captured)
								})
							)
						}
					}
					await Promise.all(captures)

					const { itemList, membershipId, program } = order
					await order.markPaid({ itemList, billingList, membershipId, program }, null, through)		// persist changes to item.custom by orderApplyQualified()

					const payments = billingsToPayments(billingList),
						R = buildResponseToApp({ id, payments }, {}, transaction)

					R.fulfilled = await this.orderFulfill(order, entitlements)
					return R
				}

				case FAILED: {
					const failedTransaction = await this.paymentCancel(transaction)
					failedTransaction.status = FAILED

					updateBillingWithTransaction(billingList, failedTransaction)
					return order.updateAttributes({ billingList })
				}

				case CANCELLED: {
					const through = {
							type: CRM,
							attributedTo: { type: SYSTEM, name: serviceName },
							touchedAt: NOW
						},
						cancelOptions = {
							reason: PAYMENT_CANCELED,
							cancelledAt: NOW,
							touchedAt: NOW,
							through
						}

					updateBillingWithTransaction(billingList, transaction)
					await order.updateAttributes({ billingList })
					return order.cancel(cancelOptions)
				}

				default: appNotify('[orderCommit] error - invalid status:', status, 'error')
				}
			}
			catch (err) {
				appNotify('[orderCommit]rollback', { err, order })
				this.orderWithdrawQualified(order, [], entitlements)
			}
		})
			.catch(err => {
				appNotify('[orderCommit] error:', { err, transaction }, 'error')
			})
	}

	/**
	 * Cancel (pending) Order, created earlier					(used by BuyApi mixin)
	 * @param	{Object} transaction
	 * 			{String} referenceId
	 * @param	{Object} options - { reason, cancelledAt, through }
	 * @return {Order} cancelled order
	 */
	Model.orderCancel = async function(transaction = {}, options) {
		const { Order } = Model.app.models,
			{ referenceId } = transaction

		return Order.cancelByTransaction(referenceId, options)
	}

	// -----  Validations  -----

	function validateImplements() {
		[
			'paymentRequest',
			'paymentAuthorize',
			'paymentCapture',
			'paymentRefund',
		].forEach(fn => assert(typeof Model[`${fn}`] === 'function', `[Buy mixin] ❌ ${Model.name}.${fn} not implement`))
	}

	validateImplements()

}
