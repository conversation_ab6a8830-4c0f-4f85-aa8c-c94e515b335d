{"name": "Resource", "plural": "Resources", "description": "Resources available for booking", "base": "PersistedModel", "idInjection": true, "strict": false, "options": {"validateUpsert": true}, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "string", "id": true, "generated": true}}, "relations": {}, "acls": [], "indexes": {}, "methods": {"availableBy": {"description": "Shortlist available qualified resources for time period", "http": {"path": "/availableby", "verb": "post"}, "accepts": [{"arg": "ids", "type": "array", "required": true}, {"arg": "from", "type": "Date", "required": true}, {"arg": "to", "type": "Date", "required": true}, {"arg": "quantity", "type": "number", "required": true}, {"arg": "price", "type": "number", "description": "Must match price (optional)"}], "returns": {"type": "object", "root": true}}}}