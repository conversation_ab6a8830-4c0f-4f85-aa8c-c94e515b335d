{"name": "RemoteRequest", "base": "Model", "description": "Shell for accessing RequestGateway lambda service", "strict": true, "options": {}, "mixins": {"MultitenantRemote": true}, "properties": {"accessToken": {"type": "string"}}, "relations": {}, "acls": [], "indexes": {}, "methods": {"create": {"description": "Create remoteRequest", "http": {"path": "/", "verb": "post"}, "accepts": [{"arg": "data", "type": "object", "required": true}], "returns": {"type": "RemoteRequest", "root": true}}, "deleteById": {"description": "Delete remoteRequest by Id", "http": {"path": "/:id", "verb": "delete"}, "accepts": [{"arg": "id", "type": "string", "required": true}], "returns": {"type": "RemoteRequest", "root": true}}}}