/**
 *  @module Model:RemoteRequest
 */
const { Context } = require('@perkd/multitenant-context')

module.exports = function(RemoteRequest) {

	RemoteRequest.create = async function(data) {
		const token = Context.generateAccessToken(),
			{ tenant } = Context,
			[ created ] = await RemoteRequest.createRest(data, tenant, token)

		return created
	}

	RemoteRequest.deleteById = async function(id) {
		const token = Context.generateAccessToken(),
			{ tenant } = Context,
			[ result ] = await RemoteRequest.deleteByIdRest(id, tenant, token)

		return result
	}
}
