{"name": "Printer", "plural": "Printers", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "validations": [], "relations": {"place": {"type": "belongsTo", "model": "Place", "foreignKey": "placeId"}}, "acls": [], "methods": {"prototype.isOnline": {"http": {"verb": "get", "path": "/online"}, "accepts": [], "description": "Get online status of printer", "returns": {"type": "boolean", "root": true}}, "prototype.print": {"http": {"verb": "post", "path": "/print"}, "accepts": [{"arg": "content", "type": "any", "required": true, "description": "string OR object (data payload when template provided)"}, {"arg": "template", "type": "string", "description": "Name of template (defined in provider package) to use"}, {"arg": "options", "type": "any", "description": "{language: ISO 639 code, copies: number of copies}"}], "description": "Print content", "returns": {"type": "any", "root": true}}}}