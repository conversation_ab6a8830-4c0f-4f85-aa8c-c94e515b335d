{"_meta": {"sources": ["loopback/common/models", "loopback/server/models", "../common/models", "./lib/common/models", "./lib/crm/models", "./models"], "mixins": ["loopback/common/mixins", "loopback/server/mixins", "../common/mixins", "./lib/common/mixins", "./lib/crm/mixins", "./mixins"]}, "User": {"dataSource": "db", "public": false}, "AccessToken": {"dataSource": "db", "public": false}, "ACL": {"dataSource": "db", "public": false}, "RoleMapping": {"dataSource": "db", "public": false}, "Role": {"dataSource": "db", "public": false}, "Service": {"dataSource": "db", "public": true}, "Product": {"dataSource": "trap", "public": true}, "Variant": {"dataSource": "trap", "public": true}, "Resource": {"dataSource": "trap", "public": true}, "Queue": {"dataSource": "trap", "public": true}, "Pricing": {"dataSource": "trap", "public": false}, "Price": {"dataSource": "transient", "public": false}, "Variation": {"dataSource": "transient", "public": false}, "Option": {"dataSource": "transient", "public": false}, "OptionValue": {"dataSource": "transient", "public": false}, "Bundle": {"dataSource": "transient", "public": false}, "BundleValue": {"dataSource": "transient", "public": false}, "Inventory": {"dataSource": "transient", "public": false}, "Link": {"dataSource": "transient", "public": false}, "OpeningHour": {"dataSource": "transient", "public": false}, "Geometry": {"dataSource": "transient", "public": false}, "Globalize": {"dataSource": "transient", "public": false}, "Tag": {"dataSource": "transient", "public": false}, "Spot": {"dataSource": "transient", "public": false}, "TouchPoint": {"dataSource": "transient", "public": false}, "ProductProvider": {"dataSource": "transient", "public": false}, "Image": {"dataSource": "imageRemote", "public": false}, "ProductImage": {"dataSource": "imageRemote", "public": false}, "Business": {"dataSource": "businessRemote", "public": false}, "OfferMaster": {"dataSource": "offerRemote", "public": false}, "Offer": {"dataSource": "offerRemote", "public": false}, "Person": {"dataSource": "person<PERSON><PERSON><PERSON>", "public": false}, "Member": {"dataSource": "membershipRemote", "public": false}, "Membership": {"dataSource": "membershipRemote", "public": false}, "Program": {"dataSource": "membershipRemote", "public": false}, "Order": {"dataSource": "salesRemote", "public": false}, "Fulfillment": {"dataSource": "salesRemote", "public": false}, "Booking": {"dataSource": "salesRemote", "public": false}, "Place": {"dataSource": "placeRemote", "public": false}, "MicrositeTemplate": {"dataSource": "contentRemote", "public": false}, "Microsite": {"dataSource": "contentRemote", "public": false}, "Reservation": {"dataSource": "reservationRemote", "public": false}, "Provider": {"dataSource": "businessRemote", "public": false}, "SharedProvider": {"dataSource": "businessRemote", "public": false}, "shopify": {"dataSource": "transient", "public": true}, "grab": {"dataSource": "transient", "public": true}, "grabfood": {"dataSource": "transient", "public": false}, "grabmart": {"dataSource": "transient", "public": false}, "ubereats": {"dataSource": "transient", "public": true}, "GTIN": {"dataSource": "productLookup", "public": true}}