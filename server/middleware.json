{"initial": {"compression": {}, "cors": {"params": {"origin": true, "credentials": true, "maxAge": 86400}}, "loopback#favicon": {}}, "session": {}, "auth": {"./lib/common/middleware/multitenant": {"name": "multitenant", "paths": ["/api/Products", "/api/Variants", "/api/Resources", "/api/shopify", "/api/grab", "/api/ubereats", "/api/Queues", "$!^(explorer)"], "params": {"tenant-code": "trap"}, "enabled": true}, "./lib/common/middleware/idempotency": {"name": "idempotency", "paths": ["/api"]}, "./lib/common/middleware/install": {"name": "install", "paths": ["/api/Products/staff", "/api/Products/app/booking"]}, "./lib/common/middleware/location": {"name": "location", "paths": ["/api/Products/staff", "/api/Products/app/booking"]}}, "auth:after": {"./lib/common/middleware/error-handler": {"name": "error-handler", "paths": ["${restApiRoot}"], "enabled": true}, "strong-error-handler": {"params": {"debug": true, "log": true, "safeFields": ["code", "details"]}}}, "parse:before": {"./lib/common/middleware/multitenant-ds": {"name": "multitenant-ds", "paths": ["/api/Products", "/api/Variants", "/api/Resources", "/api/shopify", "/api/grab", "/api/ubereats", "/api/Queues"], "enabled": true}}, "parse": {}, "routes": {"loopback#rest": {"paths": ["${restApiRoot}"]}}, "files": {}, "final": {"loopback#urlNotFound": {}}, "final:after": {"./lib/common/middleware/error-handler": {"name": "error-handler", "paths": ["${restApiRoot}"], "enabled": true}, "strong-error-handler": {"params": {"debug": true, "log": true, "safeFields": ["code", "details"]}}}}