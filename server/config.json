{"restApiRoot": "/api", "host": "0.0.0.0", "port": 3122, "remoting": {"cors": false, "rest": {"normalizeHttpPath": false, "handleErrors": false, "xml": false}, "json": {"strict": false, "limit": "100kb"}, "urlencoded": {"extended": true, "limit": "100kb"}}, "service": {"name": "Product", "domain": "product", "version": "1.0.0", "description": "", "appPath": "lib/", "settings": ["settings"], "dependencies": {}, "autoStart": true, "canTerminate": true, "state": {"now": 0, "text": "", "since": ""}, "multitenancy": true, "tenantCode": "", "tenants": {}}, "modules": {"metrics": {"enabled": true}, "eventbus": {"enabled": true}, "perkd": {"enabled": true}, "watchdog": {"enabled": true}, "provider": {"enabled": true}, "mcp": {"enabled": true}}, "apiRequest": {"version": "1.0.0", "apiVersion": 0, "timeout": 10000, "https": false, "host": "", "port": "", "apiRoot": "/api", "multitenancy": true}}