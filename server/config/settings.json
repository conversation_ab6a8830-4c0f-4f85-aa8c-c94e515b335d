{"order": {"fields": {"item": ["id", "kind", "units", "quantity", "unitPrice", "price", "tax", "unitPriceMeasure", "discountAmount", "<PERSON><PERSON><PERSON><PERSON>", "discount", "variantOptions", "bundled", "bundleId", "images", "properties", "custom", "admit", "shippingMethodId"], "product": ["id", "title", "brand", "attributes", "external", "tags", "behaviors", "businessId"], "variant": ["id", "kind", "title", "gtin", "sku", "mpn", "cost", "weight", "inventory", "prices", "taxable", "variation", "options", "attributes", "storedValue", "fulfillmentService", "preparation", "digital", "external", "productId", "businessId", "imageIds"]}}}