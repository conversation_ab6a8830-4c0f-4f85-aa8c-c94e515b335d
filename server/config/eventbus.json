{"definitions": "@perkd/event-registry-crm", "tenant": {"subscribe": ["payment.transaction.paid", "payment.transaction.authorized", "payment.transaction.chargeable", "payment.transaction.cancelled", "payment.transaction.failed", "payment.transaction.event", "sales.booking.confirmed.reservation", "sales.booking.cancelled.reservation", "sales.booking.cancelled", "sales.booking.ended", "sales.booking.noshow", "sales.booking.deleted", "membership.card.registered.reservation", "sales.order.paid.reservation"], "publish": ["product.*", "watchdog.*", "person.visit.arrive", "person.visit.leave"]}, "mapping": [{"from": "payment.transaction.paid", "to": "payment.transaction.event"}, {"from": "payment.transaction.authorized", "to": "payment.transaction.event"}, {"from": "payment.transaction.chargeable", "to": "payment.transaction.event"}, {"from": "payment.transaction.cancelled", "to": "payment.transaction.event"}, {"from": "payment.transaction.failed", "to": "payment.transaction.event"}], "MAXLEN": 1000, "LIMIT": 100}