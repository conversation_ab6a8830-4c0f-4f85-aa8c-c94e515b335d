{"enabled": true, "name": "crm-product-mcp", "version": "1.0.0", "port": 8081, "path": "/mcp", "transport": "streamable-http", "protocolVersion": "2025-06-18", "security": {"allowedOrigins": ["*"], "requireAuthentication": true, "oauth": {"enabled": false}}, "session": {"enabled": true, "timeout": 1800000, "cleanupInterval": 300000}, "logging": {"enabled": true, "level": "debug"}, "bodyParser": {"jsonLimit": "10mb"}}