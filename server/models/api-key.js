'use strict';

const crypto = require('crypto');

module.exports = function(ApiKey) {
  /**
   * Generate a new API key
   * @returns {string} Generated API key
   */
  ApiKey.generateKey = function() {
    return crypto.randomBytes(32).toString('hex');
  };
  
  /**
   * Create a new API key for a tenant
   * @param {string} name Name of the API key
   * @param {string} tenantCode Tenant code
   * @param {Date} [expiresAt] Expiration date (optional)
   * @returns {Promise<object>} Created API key
   */
  ApiKey.createForTenant = async function(name, tenantCode, expiresAt = null) {
    const key = ApiKey.generateKey();
    
    return ApiKey.create({
      name,
      key,
      tenantCode,
      active: true,
      expiresAt
    });
  };
  
  /**
   * Validate an API key
   * @param {string} key API key to validate
   * @returns {Promise<string|null>} Tenant code if valid, null otherwise
   */
  ApiKey.validate = async function(key) {
    const apiKey = await ApiKey.findOne({
      where: {
        key,
        active: true,
        or: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } }
        ]
      }
    });
    
    return apiKey ? apiKey.tenantCode : null;
  };
};
