{"name": "Bundle", "plural": "Bundles", "base": "Model", "description": "Bundled variants", "idInjection": false, "strict": true, "mixins": {}, "properties": {"key": {"type": "string", "id": true, "defaultFn": "nanoid"}, "title": {"type": "String", "max": 24, "description": "Display name in bag, eg. Drinks"}, "value": {"type": [{"type": "String"}]}, "values": {"type": [{"type": "BundleValue"}], "required": true, "default": [], "description": "Setting for component variants"}, "required": {"type": "boolean", "default": true, "description": "Is bundle mandatory?"}, "unique": {"type": "boolean", "description": "Single selection per option"}, "min": {"type": "number", "description": "Minimum number of selections"}, "max": {"type": "number", "description": "Maximum number of selections"}}, "methods": {}}