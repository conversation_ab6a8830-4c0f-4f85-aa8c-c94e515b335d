{"name": "shopify", "plural": "shopify", "base": "ProductProvider", "strict": true, "mixins": {"Errors": true, "Provider": true, "DisableAllRemotes": {"resetProvider": false, "prototype.resetProvider": false}}, "properties": {}, "acls": [], "scopes": {}, "methods": {"syncProductVariant": {"description": "Sync all Products & Variants", "http": {"path": "/sync", "verb": "post"}, "accepts": [{"arg": "until", "type": "date"}, {"arg": "shop", "type": "string"}], "returns": {"type": "object", "root": true}}, "handleExtProductUpdated": {"description": "Handle Ext Product Updated", "http": {"path": "/handleExtProductUpdated", "verb": "post"}, "accepts": [{"arg": "product", "type": "object"}], "returns": {"type": "object", "root": true}}}}