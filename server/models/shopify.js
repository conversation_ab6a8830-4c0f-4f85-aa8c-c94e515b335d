/**
 *  @module Model:Shopify	ProductProvider
 */
const { Providers } = require('@crm/types'),
	{ Context } = require('@perkd/multitenant-context')

const { SHOPIFY, PERKD } = Providers.PROVIDER,
	{ PRODUCTS } = Providers.Module,
	TRAP = 'trap',
	SYNC = {}

module.exports = function(Shopify) {

	Shopify.handleExtProductCreated = async function (extProduct) {
		return Shopify.saveProductVariant(extProduct, true)
	}

	Shopify.handleExtProductUpdated = async function (extProduct) {
		return Shopify.saveProductVariant(extProduct)
	}

	Shopify.handleExtProductDeleted = async function (extProduct = {}) {
		const { Product, Variant } = Shopify.app.models,
			{ id } = extProduct,
			NOW = new Date(),
			key = `external.${SHOPIFY}.productId`,
			productFilter = {
				where: { [key]: String(id) }
			},
			product = await Product.findOne(productFilter)

		if (!product) return []

		const { id: productId = null } = product,
			providerKey = `external.${SHOPIFY}`,
			variantFilter = {
				where: {
					productId,
					[providerKey]: { exists: true },
					deletedAt: null
				}
			},
			variants = await Variant.find(variantFilter),
			deletes = []

		for (const variant of variants) {
			deletes.push(
				variant.updateAttributes({ deletedAt: NOW })
			)
		}

		await Promise.all(deletes)
		return product.updateAttributes({ deletedAt: NOW })
	}

	Shopify.handleExtInventoryConnected = async function (data) {
		const { Variant, Place } = Shopify.app.models,
			{ inventory_item_id, location_id } = data,
			shopifyStoreId = `gid://shopify/Location/${location_id}`,
			[ variant, place ] = await Promise.all([
				Variant.findByInventoryItemId(SHOPIFY, inventory_item_id),
				Place.findOneByProviderStoreId(SHOPIFY, shopifyStoreId)
			]),
			{ id: placeId } = place ?? {},
			changes = { 'preparation.placeId': placeId, placeId }

		if (variant && placeId) {
			return variant.updateAttributes(changes)
		}
	}

	Shopify.handleExtInventoryDisconnected = async function (data) {
		const { Variant, Place } = Shopify.app.models,
			{ inventory_item_id, location_id } = data,
			shopifyStoreId = `gid://shopify/Location/${location_id}`,
			[ variant, place ] = await Promise.all([
				Variant.findByInventoryItemId(SHOPIFY, inventory_item_id),
				Place.findOneByProviderStoreId(SHOPIFY, shopifyStoreId)
			]),
			{ id } = place ?? {},
			{ preparation = {} } = variant?.toJSON() ?? {},
			{ placeId } = preparation,
			matchingPlace = String(placeId) === String(id)

		if (variant && matchingPlace) {
			delete preparation.placeId
			return variant.updateAttributes({ preparation, placeId: undefined })
		}
	}
	// ---  APIs  ---

	/**
	 * Sync Product & Variant created/modified >= given time
	 * NOTE: Shopify products have updated_at on creation
	 * @param	{Date} until - in ISO8601 UTC datetime (ie. 2020-08-13T11:30:30.000Z)
	 * @param	{String} shop name
	 * @return	{Promise<Object>} - { count | statusCode }
	 */
	Shopify.syncProductVariant = async function (until, shop) {
		const api = await this.getProvider(SHOPIFY, PRODUCTS, shop),
			{ tenant } = Context,
			options = { updated_at_min: until }

		if (tenant === TRAP) return { statusCode: 401 }
		if (SYNC[tenant]) return { statusCode: 102 }
		if (!api) return { statusCode: 404 }

		SYNC[tenant] = true

		let params = {},
			count = 0

		try {
			do {
				const shopifyProducts = await api.products.list(params),
					{ length, nextPageParameters } = shopifyProducts

				for (const sProduct of shopifyProducts) {
					const productData = api.products.toProduct(sProduct),
						product = await upsertProduct(productData),
						imagesData = api.products.toImages(sProduct, product.id),
						images = await upsertImages(imagesData),
						imageIdsList = api.products.variantsImageIds(sProduct, images),
						variantsData = api.products.toVariants(sProduct, imageIdsList)

					await upsertVariants(variantsData, product.id)
					this.markDeleteVariants(variantsData, product.id)
				}
				count += length
				params = nextPageParameters
			}
			while (params !== undefined)

			delete SYNC[tenant]
			return { count }
		}
		catch (error) {
			delete SYNC[tenant]
			return { count, error }
		}
	}

	// ---  Private

	Shopify.saveProductVariant = async function (extProduct = {}, isNew) {
		const { shop } = extProduct,
			api = await this.getProvider(SHOPIFY, PRODUCTS, shop)

		if (!api) return

		try {
			const crmProduct = api.products.toProduct(extProduct),
				product = await upsertProduct(crmProduct),
				crmImages = api.products.toImages(extProduct, String(product.id)),
				images = await upsertImages(crmImages)
			console.log('debug [saveProductVariant] %j', { crmProduct, product, crmImages, images })
			const variantsImageIds = api.products.variantsImageIds(extProduct, images),
				crmVariants = api.products.toVariants(extProduct, variantsImageIds)

			await Promise.all([
				upsertVariants(crmVariants, product.id),
				this.markDeleteVariants(crmVariants, product.id),
				!isNew && deleteUnusedImages(crmImages, product.id)
			])
		}
		catch (err) {
			appLog('[Shopify]saveProductVariant', { err, extProduct })
		}
	}

	Shopify.markDeleteVariants = async function(variantsData = [], productId = null) {
		const { Variant } = Shopify.app.models,
			existingIds = variantsData.reduce((ids, { external = {} }) => {
				const { variantId } = external[SHOPIFY] ?? {}
				if (variantId) ids.push(variantId)
				return ids
			}, []),
			NOW = new Date(),
			key = `external.${SHOPIFY}.variantId`,
			filter = {
				where: {
					productId,
					deletedAt: null,
					and: [
						{ [key]: { nin: existingIds } },
						{ [key]: { exists: true } }
					]
				},
			},
			variants = await Variant.find(filter),
			updates = []

		for (const variant of variants) {
			updates.push(
				variant.updateAttributes({ deletedAt: NOW })
			)
		}
		return Promise.all(updates)
	}

	// Functions

	// replaced by inventoryLocationId
	// async function getPlaceId(data = {}) {
	// 	const { Business, Place } = Shopify.app.models,
	// 		{ vendor: merchantCode } = data,
	// 		filter = {
	// 			where: { merchantCode },
	// 		}

	// 	if (!merchantCode) return

	// 	const business = await Business.findOne(filter)
	// 	if (!business) return

	// 	const { id: ownerId } = business,
	// 		byOwner = {
	// 			where: { ownerId }
	// 		},
	// 		place = await Place.findOne(byOwner),
	// 		{ id } = place ?? {}

	// 	return id
	// }

	async function upsertProduct(data = {}) {
		const { Product } = Shopify.app.models,
			{ external = {} } = data,
			{ productId } = external[SHOPIFY],
			key = `external.${SHOPIFY}.productId`,
			filter = {
				where: { [key]: productId },
			},
			[ product, isNew ] = await Product.findOrCreate(filter, data)

		console.log('debug [upsertProduct] data: %j, variant: %j', data, product.toJSON())
		return isNew ? product : product.updateAttributes(data)
	}

	async function upsertVariants(variants = [], productId, placeId) {
		const upserts = []

		for (const variant of variants) {
			variant.productId = productId
			variant.placeId = placeId
			upserts.push(
				upsertVariant(variant)
			)
		}
		return Promise.all(upserts)
	}

	async function upsertVariant(data = {}) {
		const { Variant } = Shopify.app.models,
			{ external = {} } = data,
			{ variantId } = external[SHOPIFY],
			key = `external.${SHOPIFY}.variantId`,
			filter = {
				where: { [key]: variantId },
			},
			[ variant, isNew ] = await Variant.findOrCreate(filter, data),
			{ placeId } = variant.preparation ?? {}

		// keep preparation.placeId when update variant
		if (placeId) {
			data.preparation = data.preparation || {}
			data.preparation.placeId = placeId
		}

		console.log('debug [upsertVariant] data: %j, variant: %j', data, variant.toJSON())
		return isNew ? variant : variant.updateAttributes(data)
	}

	async function upsertImages(images = []) {
		const upserts = []

		for (const image of images) {
			upserts.push(
				upsertImage(image)
			)
		}
		return Promise.all(upserts)
	}

	async function upsertImage(data = {}) {
		const { ProductImage } = Shopify.app.models,
			{ external = {} } = data,
			{ imageId } = external[SHOPIFY],
			key = `external.${SHOPIFY}.imageId`,
			filter = {
				where: { [key]: imageId },
			},
			image = await ProductImage.findOne(filter)

		console.log('debug [upsertImage] data: %j, image: %j', data, image?.toJSON())
		return image ? image.updateAttributes(data) : ProductImage.create(data)
	}

	async function deleteUnusedImages(images = [], productId) {
		const { ProductImage, Variant } = Shopify.app.models,
			inuseIds = images.map(img => {
				const { external = {} } = img,
					{ imageId } = external[SHOPIFY]

				return imageId
			}),
			key = `external.${SHOPIFY}.imageId`,
			filter = {
				where: {
					ownerId: productId,
					and: [
						{ [key]: { nin: inuseIds } },
						{ [key]: { exists: true } }
					]
				},
			},
			variantFilter = {
				where: { productId }
			},
			[ variants, unusedImages ] = await Promise.all([
				Variant.find(variantFilter),
				ProductImage.find(filter)
			]),
			toDeleteIds = unusedImages.map(i => String(i.id))

		if (!toDeleteIds.length) return

		for (const variant of variants) {
			const imageIds = variant.imageIds.filter(id => !toDeleteIds.includes(id))

			if (imageIds.length < variant.imageIds.length) {
				await variant.updateAttributes({ imageIds })
			}
		}

		for (const id of toDeleteIds) {
			ProductImage.deleteById(id)
		}
	}
}
