{"name": "Pricing", "plural": "Pricings", "description": "Policy-based pricing: time-based", "base": "PersistedModel", "idInjection": true, "strict": false, "options": {"validateUpsert": true}, "mixins": {"Multitenant": true, "Timestamp": true, "DisableAllRemotes": {"create": true, "upsert": false, "updateAll": false, "prototype.patchAttributes": true, "find": true, "findById": true, "findOne": true, "findOrCreate": false, "createChangeStream": false, "deleteById": true, "confirm": true, "exists": false, "replaceById": false, "replaceOrCreate": false, "upsertWithWhere": false}}, "properties": {"policy": {"type": "string", "required": true, "enum": ["time"], "default": "time"}, "hours": {"type": "OpeningHour", "required": true}, "capacity": {"type": "number", "description": "Total units available a day"}}, "relations": {"variant": {"type": "belongsTo", "model": "<PERSON><PERSON><PERSON>", "foreignKey": "variantId"}}, "acls": [], "indexes": {}, "methods": {}}