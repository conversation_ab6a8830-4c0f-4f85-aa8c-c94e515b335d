/**
 *  @module Model:Grab	 ProductProvider
 */
const { Providers, Settings, Products } = require('@crm/types'),
	{ Context } = require('@perkd/multitenant-context'),
	{ Currencies } = require('@perkd/utils'),
	{ hours2SellingTimes, products2Categories: martCategories } = require('@provider/grabmart'),
	{ nextSellingTime, products2Categories: foodCategories } = require('@provider/grabfood')

const { GRABFOOD, GRABMART } = Providers.PROVIDER,
	{ PRODUCTS } = Providers.Module,
	{ LOCALE } = Settings.Name,
	{ HIDE, SOLDOUT } = Products.Availability,
	MAX_TIMEOUT = Math.pow(2, 31) - 1,
	TIMERS = {}			// { [tenantCode]: { [provider]: timeoutID } }

module.exports = function(Grab) {

	// --- Event Handlers ---

	/**
	 * Handle product inventory update events
	 * @param {Object} data - Event data containing variant and inventory information
	 */
	Grab.handleInventoryUpdated = async function(data) {
		const { variantId: itemId, channels, inventory, availability, placeId } = data,
			stock = availability === HIDE || availability === SOLDOUT ? 0 : inventory.quantity,
			promises = []

		if (!placeId || !itemId || !channels?.length) return []

		const { Place } = Grab.app.models,
			place = await Place.findById(placeId).catch(error => {
				throw new Error(`[Grab.handleInventoryUpdated] Place not found: ${error.message}`)
			}),
			{ external = {} } = place ?? {}

		if (!external) return []

		if (channels.includes(GRABFOOD) && place.external[GRABFOOD]?.storeId) {
			const shop = place.external[GRABFOOD].storeId
			promises.push(Grab.menuItemRefresh(GRABFOOD, shop , itemId, stock))
		}
		if (channels.includes(GRABMART) && place.external[GRABMART]?.storeId) {
			const shop = place.external[GRABMART].storeId
			promises.push(Grab.menuItemRefresh(GRABMART, shop, itemId, stock))
		}

		return Promise.all(promises)
	}

	// --- Menu callback API	(used by grab-callbacks lambda)

	/**
	 * GrabFood typically uses this endpoint to get the latest outlet menu(s) from the partner's database
	 * @param	{String} merchantID - grab store id
	 * @return	{Promise<Object>} - { currency, sellingTimes, categories }
	 */
	Grab.foodMenu = async function(merchantID) {
		return Grab.menu(GRABFOOD, merchantID, foodCategories)
	}

	/**
	 * GrabMart typically uses this endpoint to get the latest outlet menu(s) from the partner's database
	 * @param	{String} merchantID - grab store id
	 * @return	{Promise<Object>} - { categories, currency, sellingTimes }
	 */
	Grab.martMenu = async function(merchantID) {
		return Grab.menu(GRABMART, merchantID, martCategories)
	}

	Grab.menu = async function(provider, merchantID, toCategories) {
		const { app } = Grab,
			{ Place } = app.models,
			{ tenant } = Context,
			NOW = new Date(),
			store = await Place.findOneByProviderStoreId(provider, merchantID),
			{ id: storeId, openingHours, startTime, endTime } = store?.toJSON() ?? {},
			timers = TIMERS[tenant],
			timerId = timers?.[provider]

		if (timerId) {
			clearTimeout(timerId)
			delete timers[provider]
		}

		if (!store) {
			appNotify(`[${provider}] store not found`, { merchantID }, 'error')
			return {}
		}
		if ((startTime && startTime > NOW) || endTime && endTime < NOW) {
			appNotify(`[${provider}] store not operational`, { merchantID, startTime, endTime }, 'error')
			return {}
		}

		const products = await this.findProducts(provider, String(storeId)),
			{ country, currency: code } = app.getTenantSettings(tenant, LOCALE),
			{ symbol, decimals: exponent } = Currencies.currency(code),
			currency = { code, symbol, exponent },
			sellingTimes = hours2SellingTimes(openingHours),
			{ id, endTime: refreshTime } = nextSellingTime(sellingTimes) ?? {},
			refreshAt = refreshTime.getTime() - Date.now() + parseInt(Math.random() * 20 * 1000) + 10 * 1000,
			timeout = Math.min(MAX_TIMEOUT, Math.max(refreshAt, 60 * 1000)),	// min. 1 min
			categories = toCategories(country, products, id)

		TIMERS[tenant] ||= {}
		TIMERS[tenant][provider] = setTimeout(() => Grab.menuRefreshTimer(provider), timeout)	// next refresh

		return { categories, currency, sellingTimes }
	}

	/**
	 * Timer handler - notify Grab to refresh menu
	 * @param	{String} provider
	 * @param	{String} [tenant] code
	 */
	Grab.menuRefreshTimer = async function(provider, tenant = Context.tenant) {
		const msg = `[${provider}]menuRefreshTimer - ${tenant}`

		try {
			const grab = await this.getProvider(provider)

			if (!grab) return

			appNotify(msg, 'called')
			await grab.products.refresh()
		}
		catch (error) {
			console.error(msg, error)
			appNotify(msg, error, 'error')
		}
	}

	/**
	 * For Shopify / Staff App - notify Grab to refresh menu
	 * @param	{String} provider
	 * @param	{String} shop
	 */
	Grab.menuRefresh = async function(provider, shop) {
		if (!provider) return

		const msg = `[${provider}]menuRefresh`

		try {
			const grab = await this.getProvider(provider, PRODUCTS, shop)

			if (!grab) return

			await grab.products.refresh()
		}
		catch (error) {
			console.error(msg, error)
			appNotify(msg, error, 'error')
		}
	}

	/**
	 * For Shopify / Staff App - update Grab menu item's inventory and price
	 * @param	{String} provider
	 * @param	{String} itemId
	 * @param	{Number} inventory
	 * @param	{String} price
	 */
	Grab.menuItemRefresh = async function(provider, shop, itemId, inventory, unitPrice) {
		if (!provider) return
		const logMsg = `[${provider}: ${shop}: ${itemId}: ${inventory}: ${unitPrice}]menuItemRefresh`
		try {
			const grab = await this.getProvider(provider, PRODUCTS, shop)
			if (!grab) return
			await grab.products.updateItem(itemId, inventory, unitPrice)
		}
		catch (error) {
			console.error(logMsg, error)
			appNotify(logMsg, error, 'error')
		}
	}
}
