{"name": "Product", "plural": "Products", "base": "PersistedModel", "idInjection": true, "strict": true, "options": {}, "mixins": {"Multitenant": true, "Common": true, "Timestamp": true, "Errors": true, "Mongo": true, "Tag": true, "Image": {"model": "ProductImage", "relation": "images", "description": "Upload pictures of Product."}, "Globalize": {"properties": ["title", "description", "brand"]}, "FindProduct": true, "Booking": true, "TableBooking": true, "TableQueuing": true, "AppLink": true, "Provision": true, "BookingWidgetApi": true, "EventWidgetApi": true, "TableWidgetApi": true, "QueuingWidgetApi": true, "MenuWidgetApi": true, "StaffWidgetApi": true, "TableWidgetHandler": true, "DisableAllRemotes": {"create": true, "upsert": true, "prototype.patchAttributes": true, "find": true, "findById": true, "findOne": true, "findOrCreate": true, "deleteById": true, "confirm": false, "count": true, "exists": true, "replaceById": true, "replaceOrCreate": true, "upsertWithWhere": true, "prototype.__create__images": true, "prototype.__create__resource": true, "prototype.__destroy__resource": true}}, "properties": {"title": {"type": "String", "description": "product’s name", "required": true, "max": 128}, "description": {"type": "String"}, "brand": {"type": "String", "max": 64, "description": "Brand name of the product"}, "availability": {"type": "String", "enum": ["active", "soldout", "hide", "preorder"], "description": "Availability status"}, "availabilityDate": {"type": "date", "description": "The date a pre-ordered/backorder product becomes available for delivery, use this attribute if you submit availability as preorder"}, "priceRange": {"type": {"min": {"type": "Number"}, "max": {"type": "Number"}, "currency": {"type": "String"}}}, "isLowQuantity": {"type": "Boolean", "default": false, "description": "Indicates when at least one of variants `inventoryQuantity` are lower then their `lowInventoryWarningThreshold"}, "isSoldOut": {"type": "Boolean", "default": false, "description": "Indicates when all variants `inventoryQuantity` is zero"}, "isBackOrder": {"type": "Boolean", "default": false, "description": "It is `true` if product not in stock, but customers anyway could order it."}, "attributes": {"type": "object", "description": "Custom attributes"}, "external": {"type": {"shopify": {"type": {"productId": {"type": "String"}, "shop": {"type": "String", "description": "Shopify shopName"}}}}, "default": {}}, "links": {"type": [{"type": "Link"}], "default": []}, "tags": {"type": "Tag", "default": {"system": [], "user": [], "category": []}}, "behaviors": {"type": "object", "default": {}}, "visible": {"type": "Boolean", "default": true}, "globalize": {"type": "Globalize", "default": {}}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}, "deletedAt": {"type": "date"}}, "hidden": [], "validations": [], "relations": {"bundles": {"type": "embeds<PERSON><PERSON>", "model": "Bundle", "property": "bundleList", "options": {"validate": false, "persistent": true}}, "variations": {"type": "embeds<PERSON><PERSON>", "model": "Variation", "property": "variationList", "options": {"validate": false, "persistent": true}}, "vendor": {"type": "belongsTo", "model": "Business", "foreignKey": "businessId"}, "images": {"type": "hasMany", "model": "ProductImage", "foreignKey": "ownerId"}, "variants": {"type": "hasMany", "model": "<PERSON><PERSON><PERSON>", "foreignKey": "productId"}, "resource": {"type": "hasOne", "model": "Resource", "foreignKey": "productId"}}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"findByIds": {"description": "Find all instances from the data source.", "accessType": "READ", "http": {"path": "/findByIds", "verb": "get"}, "accepts": [{"arg": "ids", "type": "array", "required": true}, {"arg": "query", "type": "object"}], "returns": {"type": "array", "root": true}}, "gtinLookup": {"description": "Get related product information by GTIN", "http": {"path": "/gtin/lookup/:upc", "verb": "get"}, "accepts": [{"arg": "upc", "type": "string", "http": {"source": "path"}, "required": true}], "returns": {"type": "array", "root": true}}, "prototype.deleteLink": {"description": "Delete a related item by id for link", "http": {"path": "/links/:fk", "verb": "DELETE"}, "accepts": [{"arg": "fk", "type": "string", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}}}