{"name": "Variation", "plural": "Variations", "base": "Model", "description": "Properties that define variants", "idInjection": false, "strict": true, "mixins": {}, "options": {}, "properties": {"id": {"type": "string", "id": true}, "title": {"type": "String", "max": 24, "description": "Display name, eg. size, color"}, "value": {"type": "string", "max": 24, "description": "Used in Variant, one of values, eg. s, m, l, red, green, blue"}, "values": {"type": ["string"], "description": "Used in Product, all values of variation (name), eg. ['red', 'green'...]"}}, "scopes": {}, "methods": {}}