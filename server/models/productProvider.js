/**
 *  @module Model:ProductProvider
 */

const { PROVIDER: PROVIDER_ERR } = require('@perkd/errors/dist/service')

const { EVENT_NOT_HANDLED, HANDLER_NOT_IMPLEMENTED } = PROVIDER_ERR,
	// local events
	PRODUCT = {
		INVENTORY_UPDATED: 'product.inventory.updated',
	},
	PLACE = {
		UPDATED: 'place.place.updated'
	},
	// external events
	PRODUCT_CREATED = 'product.created',
	PRODUCT_UPDATED = 'product.updated',
	PRODUCT_DELETED = 'product.deleted',
	PRODUCTS_REFRESH = 'product.refresh',		// request to refresh menu, (Grab, UberEats)
	INVENTORY_CONNECTED = 'inventory.connected',
	INVENTORY_DISCONNECTED = 'inventory.disconnected',
	// findProducts
	FIELDS = [ 'id', 'title', 'gtin', 'position', 'prices', 'variations', 'options', 'attributes',
		'weight', 'minOrderQuantity', 'globalize', 'inventory', 'productId', 'imageIds' ],
	MAX_RESULTS = 500,
	Q_INTERNAL_EVENTS = 'providerInternalEvents'

module.exports = function(ProductProvider) {

	ProductProvider.providerExternalEvents = async function(name, data = {}) {
		const { modelName: provider } = this,
			{ id } = data

		return this.queue(`providerExternalEvents_${provider}_${id}`, (() => {
			switch (name) {
			case PRODUCT_CREATED:
				return this.handleExtProductCreated(data)

			case PRODUCT_UPDATED:
				return this.handleExtProductUpdated(data)

			case PRODUCT_DELETED:
				return this.handleExtProductDeleted(data)

			case PRODUCTS_REFRESH:
				return this.handleExtProductsRefresh(data)

			case INVENTORY_CONNECTED:
				return this.handleExtInventoryConnected(data)

			case INVENTORY_DISCONNECTED:
				return this.handleExtInventoryDisconnected(data)

			case INVENTORY_UPDATED:
				return this.handleExtInventoryUpdated(data)

			default:
				appNotify(EVENT_NOT_HANDLED, { provider, name, data }, 'error')
			}
		}))
	}

	ProductProvider.providerInternalEvents = async function(name, data = {}) {
		const { modelName: provider } = this,
			{ id } = data

		return this.queue(`${Q_INTERNAL_EVENTS}:${provider}:${id}`, async() => {
			switch(name) {
			case PRODUCT.INVENTORY_UPDATED:
				return this.handleInventoryUpdated(data)
			case PLACE.UPDATED:
				return this.handlePlaceUpdated(data)
			default:
				appNotify(EVENT_NOT_HANDLED, { provider, name, data }, 'error')
			}
		})
	}

	// ---  Shared methods

	/**
	 * Get Menu Items for channel of given store
	 * @param	{String} channel
	 * @param	{String} storeId
	 * @returns	{Object[]} - list of simplified, flattened product/variant
	 */
	ProductProvider.findProducts = async function (channel, storeId) {
		const { Variant, ProductImage } = ProductProvider.app.models,
			filter = {
				where: {
					channels: channel,
					visible: true,
					deletedAt: null,
					placeId: storeId
				},
				include: {
					relation: 'product',
					scope: { fields: [ 'description', 'tags' ] }
				},
				fields: FIELDS,
				order: 'position ASC',
				limit: MAX_RESULTS
			},
			list = await Variant.find(filter),
			result = []

		for (const instance of list) {
			const variant = instance.toJSON(),
				{ id: ID, title, gtin, position, weight, inventory, prices, product, imageIds = [] } = variant,
				id = String(ID),
				urlsWithIds = imageIds.length > 0 ? await ProductImage.getUrlsWithIds(imageIds) : {}, // loopback will return badArgumentError for empty array param
				imageUrls = Object.values(urlsWithIds),
				{ description, tags: tg } = product,
				{ category: tags = [] } = tg ?? {}

			result.push({ id, title, gtin, position, weight, inventory, prices, description, tags, imageUrls })
		}

		return result
	}

	// ----  Provider-specific methods  ----

	// internal event handlers
	ProductProvider.handleInventoryUpdated
	= ProductProvider.handlePlaceUpdated
	= function(data) {
				this.rejectErr(HANDLER_NOT_IMPLEMENTED)
			}

	// external event handlers
	ProductProvider.handleExtProductCreated
	= ProductProvider.handleExtProductUpdated
	= ProductProvider.handleExtProductDeleted
	= ProductProvider.handleExtProductsRefresh
	= ProductProvider.handleExtInventoryConnected
	= ProductProvider.handleExtInventoryDisconnected = async function(data) {
							appNotify(`TODO: ${JSON.stringify(data)}`, 'error')
							return this.rejectErr(HANDLER_NOT_IMPLEMENTED)
						}
}
