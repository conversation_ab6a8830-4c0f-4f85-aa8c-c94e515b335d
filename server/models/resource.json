{"name": "Resource", "plural": "Resources", "base": "PersistedModel", "idInjection": true, "strict": true, "options": {"validateUpsert": true}, "mixins": {"Multitenant": true, "Timestamp": true, "Provider": true, "Calendar": true, "Allocate": true, "DisableAllRemotes": {"create": true, "updateAll": true, "prototype.patchAttributes": true, "find": true, "findById": true, "findOne": true, "deleteById": true, "confirm": true}}, "properties": {"kind": {"type": "string", "enum": ["event", "venue", "table", "person"], "default": "event", "required": true}, "name": {"type": "string", "max": 32, "description": "Used as Calendar name 'summary'"}, "description": {"type": "string", "max": 120, "description": "Used as Calendar description"}, "position": [{"type": {"key": {"type": "string", "description": "Unique name for position identifier, eg. table, hall, seat (globalized)"}, "value": {"type": "string", "description": "Value position key, eg. 8 (for table), 3H (for seat)"}}}], "timeZone": {"type": "string", "description": "Time zone of resource, IANA time zone name, eg. Asia/Singapore"}, "hours": {"type": "OpeningHour", "required": true}, "startTime": {"type": "date", "description": "Start time of resource availability derived from 'hours', absent if no 'specific' in hours"}, "endTime": {"type": "date", "description": "End time of resource availability derived from 'hours', absent if no 'specific' in hours"}, "capacity": {"type": "number", "description": "Max number allowed (usually person)"}, "shared": {"type": "boolean", "default": false, "description": "Shared resource allow (partial) booking, limited by capacity"}, "interval": {"type": "number", "description": "Time (minutes) between booking time slots"}, "leadTime": {"type": "number", "description": "Minimum time before a time slot required for booking the slot"}, "maxCombined": {"type": "number", "description": "Max resources that can be combined, 0 => cannot be combined (default)"}, "cleanupTime": {"type": "number", "description": "Time needed between bookings"}, "calendarId": {"type": "string", "description": "Google Calendar Id"}}, "relations": {"product": {"type": "belongsTo", "model": "Product", "foreignKey": "productId"}, "variants": {"type": "hasMany", "model": "<PERSON><PERSON><PERSON>", "foreignKey": "resourceId"}, "orders": {"type": "hasMany", "model": "Order", "foreignKey": "resourceId"}, "place": {"type": "belongsTo", "model": "Place", "foreignKey": "placeId"}, "adjacent": {"type": "referencesMany", "model": "Resource", "foreignKey": "adjacentIds", "description": "Adjacent resources that can be combined"}}, "acls": [], "indexes": {}, "methods": {"availableBy": {"description": "Shortlist available qualified resources for time period", "http": {"path": "/availableby", "verb": "post"}, "accepts": [{"arg": "ids", "type": "array", "required": true}, {"arg": "from", "type": "Date", "required": true}, {"arg": "to", "type": "Date", "required": true}, {"arg": "quantity", "type": "number", "required": true}, {"arg": "price", "type": "number", "description": "Must match price (optional)"}], "returns": {"type": "object", "root": true}}, "prototype.balance": {"description": "Balance quantity available for resource", "http": {"path": "/balance", "verb": "post"}, "accepts": [{"arg": "from", "type": "Date", "required": true}, {"arg": "to", "type": "Date", "required": true}], "returns": {"type": "Number", "root": true}}}}