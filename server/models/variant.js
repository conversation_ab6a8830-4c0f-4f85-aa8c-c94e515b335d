/**
 *  @module Model:Variant
 */

const { Orders, Products, Fulfillments, Settings } = require('@crm/types'),
	{ uniqueItems } = require('@perkd/commerce'),
	{ isOpen } = require('@perkd/utils'),
	{ ORDER: ORDER_ERR } = require('@perkd/errors/dist/service')

const { BOOKING, PRODUCT } = Orders.ItemKind,
	{ SHOPIFY, PERKD, MENU } = Products.Channel,
	{ VENDING } = Products.FulfillmentService,
	{ DINEIN, PICKUP } = Fulfillments.Type,
	{ LOCALE } = Settings.Name,
	{ ITEM_NOT_FOUND, ITEM_NO_STOCK, DISCOUNT_CANNOT_USE, FULFILLMENT_UNAVAILABLE, INVALID_SCHEDULED_TIME } = ORDER_ERR,
	DENY = 'deny'

module.exports = function(Variant) {

	/**
	 * Qualify offers are redeemable (when qualifying order)		(used by Order mixin)
	 * @param	{MOrder} mOrder
	 * @return	{Object}
	 */
	Variant.checkOffers = async function(mOrder) {
		const { Perkd } = appModule('perkd'),
			{ Offer } = this.app.models,
			{ discounts = [] } = mOrder,
			offerIds = []

		for (const { offerId } of discounts) {
			if (offerId) offerIds.push(offerId)
		}

		if (!discounts.length || !offerIds.length) return undefined

		try {
			const res = await Offer.qualifyRedeem(offerIds)
			return res
		}
		catch (err) {
			appLog('Variant.checkOffers', { err, mOrder })

			for (const id of offerIds) {
				const offer = await Offer.findById(id)
				await Perkd.offers.update(offer).catch(() => null)		// refresh offer in X
			}
			return this.rejectErr(DISCOUNT_CANNOT_USE)
		}
	}

	/**
	 * Qualify DINEIN & PICKUP fulfillment hours (when qualifying order)
	 * @param {MOrder} mOrder
	 * @return {Promise<boolean|Error>}
	 */
	Variant.checkHours = async function(mOrder) {
		const { app } = this,
			{ Place } = app.models,
			{ timeZone } = app.getSettings(LOCALE),
			NOW = new Date(),
			{ fulfillments = [] } = mOrder,
			[ fulfillment = {} ] = fulfillments,
			{ type, destination = {}, scheduled = {} } = fulfillment,
			{ placeId } = destination

		// Early return if not a relevant fulfillment type
		if (![ DINEIN, PICKUP ].includes(type)) {
			return true
		}

		// Validate placeId exists
		if (!placeId) {
			appNotify('[checkHours] missing placeId', { fulfillment }, 'error')
			return this.rejectErr(FULFILLMENT_UNAVAILABLE)
		}

		// Find store and validate existence
		const store = await Place.findById(placeId)
		if (!store) {
			appNotify('[checkHours] store not found', { placeId }, 'error')
			return this.rejectErr(FULFILLMENT_UNAVAILABLE)
		}

		// Get store configuration
		const { openingHours } = store,
			config = store[type] || {},
			{ available = true, hours = openingHours } = config

		// Check if fulfillment type is available at this store
		if (!available) {
			appNotify('[checkHours] fulfillment not available', { type, placeId }, 'error')
			return this.rejectErr(FULFILLMENT_UNAVAILABLE)
		}

		// If no hours defined but available, assume 24/7
		if (!hours) {
			return true
		}

		try {
			// Get relevant time to check
			const { maxTime } = scheduled,
				checkTime = type === PICKUP
					? (maxTime ? new Date(maxTime) : NOW)  // For pickup, use scheduled time if exists
					: NOW                                  // For dine-in, always use current time

			// Validate the time is not in the past
			if (checkTime < NOW) {
				appNotify('[checkHours] invalid scheduled time', { checkTime, NOW }, 'error')
				return this.rejectErr(INVALID_SCHEDULED_TIME)
			}

			// Convert times to store's timezone for comparison
			const isStoreOpen = isOpen(hours, checkTime, undefined, timeZone)

			if (!isStoreOpen) {
				appNotify('[checkHours] outside operating hours', {
					type,
					placeId,
					checkTime,
					hours
				}, 'error')
				return this.rejectErr(FULFILLMENT_UNAVAILABLE)
			}

			return true

		}
		catch (err) {
			appNotify('[checkHours] error checking hours', {
				err,
				type,
				placeId,
				scheduled,
				hours
			}, 'error')
			return this.rejectErr(FULFILLMENT_UNAVAILABLE)
		}
	}

	/**
	 * Verify sufficient stock (when qualifying order)		(used by Order mixin)
	 * @param	{Object[]} items
	 * @param	{string} channel
	 * @return	{Object} { order_item_no_stock: [], order_item_not_found: [] }
	 */
	Variant.checkInventory = async function(items = [], channel) {
		const res = {
				[ITEM_NOT_FOUND]: [],
				[ITEM_NO_STOCK]: []
			},
			unique = uniqueItems(items)

		for (const { kind, quantity, variantId, custom } of unique) {
			if (kind === BOOKING) {
				if (custom.shortfall) {
					res[ITEM_NO_STOCK].push({ variantId, quantity, custom })
				}
			}
			else {
				const variant = await Variant.findById(variantId)

				if (!variant) {
					res[ITEM_NOT_FOUND].push({ variantId })
				}
				else {
					const { inventory, external = {}, sku } = variant,
						{ policy = DENY, quantity: balance } = inventory,
						stock = balance >= 0 ? balance : 0,
						{ variantId: extVariantId } = external,
						channelVariantId = (channel === SHOPIFY && extVariantId)
							? extVariantId
							: variantId		// other channels?

					if (policy === DENY && quantity > stock) {
						res[ITEM_NO_STOCK].push({ variantId: channelVariantId, sku, quantity: stock })
					}
				}
			}
		}

		return res
	}

	/**
	 * Return all quantities of 'product' items
	 * @param	{Object[]} items
	 */
	Variant.returnInventory = async function (items = []) {
		for (const { kind, variantId, quantity } of items) {
			if (kind !== PRODUCT) continue

			const variant = await Variant.findById(variantId)
			await variant?.addInventory(quantity)
		}
	}

	/**
	 * Deduct inventory by quantity					(used by Order mixin)
	 * 	- when apply qualified AFTER payment
	 * 	- throw if insufficient balance & DENY policy
	 * @param	{Number} quantity
	 * @return	{Number} balance
	 */
	Variant.prototype.deductInventory = async function (quantity = 1) {
		const { id, sku, inventory = {} } = this,
			{ management, policy = DENY, quantity: balance } = inventory,
			query = { id },
			update = {
				$inc: { 'inventory.quantity': -quantity }
			},
			outOfStockItems = {
				items: [ { variantId: id, sku, quantity: balance } ]
			}

		if (management !== PERKD) return balance	// not managed by us

		if (quantity > balance && policy === DENY) {
			return this.rejectErr(ITEM_NO_STOCK, outOfStockItems)
		}

		if (policy === DENY) {
			query['inventory.quantity'] = { $gte: quantity }
		}

		const updated = await Variant.findOneAndUpdate(query, update)

		return updated
			? updated.inventory.quantity
			: this.rejectErr(ITEM_NO_STOCK, outOfStockItems)
	}

	/**
	 * Add inventory by quantity
	 * @param	{Number} quantity
	 * @return	{Number} balance
	 */
	Variant.prototype.addInventory = async function (quantity = 1) {
		const { id } = this,
			updated = await Variant.findOneAndUpdate(
				{ id },
				{
					$inc: { 'inventory.quantity': quantity }
				})

		return updated.inventory.quantity
	}

	/**
	 * Get price for variant
	 * @param {Date} [at]
	 * @return {Object} { value, currency }
	 */
	Variant.prototype.priceAt = function(at = new Date()) {
		const { prices = [] } = this,
			applicable = prices.reduce((res, { price, salePrice }) => {
				if (salePrice?.startAt <= at && salePrice?.endAt >= at) {
					res.push(
						(salePrice.value < price.value) ? salePrice : price
					)
				}
				else if (price) res.push(price)
				return res
			}, []),
			[ { value, currency } ] = applicable.sort((a, b) => {
				if (a.value < b.value) return -1
				if (a.value > b.value) return 1
				return 0
			})

		return { value, currency }
	}

	// ---  used by AppLink mixin  ---

	Variant.prototype.applinkPrepare = async function(fallbackActionId, options = {}) {
		const { id, imageIds } = this,
			{ Business, ProductImage } = Variant.app.models,
			{ Perkd } = appModule('perkd'),
			{ state, shopUri } = options,
			filter = {
				where: { id: { inq: imageIds } },
				order: 'position ASC',
			},
			businessFilter = {
				where: { isMain: true },
				fields: [ 'id', 'name' ],
				// include: { relation: 'logos', scope: { fields: ['original'] } },
			},
			[ action, images, business, product ] = await Promise.all([
				Perkd.actions.get(fallbackActionId),
				ProductImage.find(filter),
				Business.findOne(businessFilter),
				this.product.get(),
			]),
			payload = Variant.applinkAction(action.data.cardMasterIds, product, this, images),
			addtobag = await Perkd.actions.uri(fallbackActionId, payload),
			links = { [id]: addtobag }

		return Variant.applinkSiteData(product, [ this ], business, images, links, state, shopUri)
	}

	// -----  Operation hooks  -----

	function isMenuVariant(data) {
		const { channels = [] } = data || {}
		return channels.includes(MENU)
	}

	function isMachineVariant(data) {
		const { fulfillmentService } = data
		return fulfillmentService === VENDING
	}

	/**
	 * Build Item event data
	 */
	async function buildItemEvent(instance) {
		const { Product } = Variant.app.models,
			data = instance.toJSON(),
			{ productId = null, id: variantId, channels, inventory, placeId  } = data,
			product = await Product.findById(productId).catch(err => undefined),
			{ availability } = product ?? {}

		return { variantId, productId, channels, placeId, inventory, availability }
	}

	/**
	 * Build Menu event data
	 */
	async function buildMenuEvent(instance) {
		const { Product, ProductImage } = Variant.app.models,
			data = instance.toJSON(),
			{ productId = null, imageIds = [] } = data,
			[ product, imageUrls ] = await Promise.all([
				Product.findById(productId).catch(err => undefined),
				imageIds.length ? ProductImage.getUrlsWithIds(imageIds).catch(err => undefined) : {}
			]),
			{ description, availability, tags } = product ?? {},
			images = Object.values(imageUrls ?? {})

		data.description = description
		data.availability = availability
		data.tags = tags
		data.images = images
		return data
	}

	/**
	 * Build Machine event data
	 */
	async function buildMachineEvent(instance) {
		// FIXME
		return instance.toJSON()
	}

	Variant.observe('before save', async ({ currentInstance, data, isNewInstance, hookState }) => {
		console.log('debug [Variant before save] %j', { currentInstance, data, isNewInstance, hookState })
		const { inventory = {} } = data ?? {},
			{ quantity: newQty } = inventory

		if (!isNewInstance && newQty) {
			const { quantity: oldQty = 0 } = currentInstance.inventory ?? {}
			hookState.changed = newQty - oldQty
		}
	})

	Variant.observe('after save', async ({ instance, isNewInstance, hookState }) => {
		console.log('debug [Variant after save] %j', { instance, isNewInstance, hookState })
		const { Event } = Variant.app,
			{ changed } = hookState

		if (isNewInstance) {
			appEmit(Event.variant.created, instance)

			if (isMenuVariant(instance)) {
				const event = await buildMenuEvent(instance)
				appEmit(Event.variant.menu.created, event)
			}
			if (isMachineVariant(instance)) {
				const event = await buildMachineEvent(instance)
				appEmit(Event.variant.machine.created, event)
			}
		}
		else {
			appEmit(Event.variant.updated, instance)

			if (changed) {
				const event = { ...await buildItemEvent(instance), changed }
				appEmit(Event.product.inventory.updated, event)
			}
			if (isMenuVariant(instance)) {
				const event = await buildMenuEvent(instance)
				appEmit(Event.variant.menu.updated, event)
			}
			if (isMachineVariant(instance)) {
				const event = await buildMachineEvent(instance)
				appEmit(Event.variant.machine.updated, event)
			}
		}
	})

	Variant.observe('before delete', async ({ where, hookState }) => {
		hookState.instance = await Variant.findOne({ where })
	})

	Variant.observe('after delete', async ({ hookState }) => {
		const { Event } = Variant.app,
			{ instance } = hookState

		if (!instance) return // no variant deleted

		appEmit(Event.variant.deleted, instance)
		if (isMenuVariant(instance)) {
			const event = await buildMenuEvent(instance)
			appEmit(Event.variant.menu.deleted, event)
		}
		if (isMachineVariant(instance)) {
			const event = await buildMachineEvent(instance)
			appEmit(Event.variant.machine.deleted, event)
		}
	})
}
