{"name": "Price", "plural": "Prices", "base": "Model", "idInjection": false, "strict": false, "mixins": {}, "options": {}, "properties": {"name": {"type": "String", "description": "default: base", "id": true}, "price": {"type": {"value": {"type": "number"}, "currency": {"type": "string", "description": "eg: HKD"}}}, "salePrice": {"type": {"value": {"type": "number"}, "currency": {"type": "string", "description": "eg: HKD"}, "startAt": {"type": "date", "description": "The effective date of the price"}, "endAt": {"type": "date", "description": "The date when this price will end"}}}, "increment": {"type": "number", "description": "for variable pricing, eg. variable topup for stored value"}, "fee": {"type": "object"}, "paymentMethods": {"type": [{"type": "string"}], "description": "limit to these methods"}, "notPaymentMethods": {"type": [{"type": "string"}], "description": "exclude these methods"}, "countries": {"type": [{"type": "string"}], "default": []}}, "validations": [], "scopes": {}, "methods": {}}