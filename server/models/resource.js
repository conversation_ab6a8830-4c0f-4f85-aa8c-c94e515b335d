/**
 * @module Model: Resource
 */
const { Settings, Providers } = require('@crm/types'),
	{ isOpen, openUntil, startEndOf } = require('@perkd/utils')

const { BOOKING, LOCALE } = Settings.Name,
	{ GOOGLE } = Providers.PROVIDER,
	OWNER = 'owner'

module.exports = function(Resource) {

	/**
	 * Find available resources (in ascending order of capacity)
	 * 	1. Find candidate resources that match capacity
	 * 	2. Shortlist resources that are available at time for duration
	 * @param	{String} placeId
	 * @param	{String} kind - kind of resource
	 * @param	{Number} capacity - number of people
	 * @param	{Date} at - start time
	 * @param	{Number} duration - in MINUTES
	 * @param	{Boolean} combined - accept combined resources
	 * @param	{Boolean} [includeOverstay=false] - Whether to include overstaying bookings check
	 * @return	{Resource[]} list of available resources
	 */
	Resource.available = async function (placeId, kind, capacity, at, duration = 0, combined, includeOverstay = false) {
		const { app } = Resource,
			{ maxPartySize } = app.getSettings(BOOKING)[kind],
			from = at || new Date(),
			to = new Date(from.getTime() + duration * 60000 - 1)

		// Validate capacity requirements
		if (capacity > maxPartySize) {
			throw 'CAPACITY_EXCEEDED'
		}

		// Find candidate resources within physical size constraints
		const filter = {
				where: {
					kind,
					placeId,
					capacity: combined ? { gte: 1 } : { gte: capacity },
				},
				order: 'capacity ASC'
			},
			resources = await Resource.find(filter)

		if (!resources.length) return { available: [], occupiedIds: new Set() }

		// Get available resources and validate their status
		const resourceIds = resources.map(r => String(r.id))

		const { available, occupiedIds } = await Resource.availableBy(resourceIds, from, to, 1, null, includeOverstay)
		return { available, occupiedIds }
	}

	/**
	 * Shortlist available qualified resources for time period
	 * @param	{String|Resource[]} idsOrInstances - of qualified resources to select from
	 * @param	{Date} from
	 * @param	{Date} to
	 * @param	{Number} [quantity]
	 * @param	{Number} [price]
	 * @param	{Boolean} [includeOverstay=false] - Whether to include overstaying bookings check
	 * @return	{Resource[]} list of available resources
	 */
	Resource.availableBy = async function(idsOrInstances, from, to, quantity = 1, price, includeOverstay = false) {
		const { Booking } = Resource.app.models,
			filter = {
				where: { id: { inq: idsOrInstances } }
			},
			isInstances = idsOrInstances[0] instanceof Resource,
			ids = isInstances ? idsOrInstances.map(i => String(i.id)) : idsOrInstances,
			[ resources, occupied ] = await Promise.all([
				isInstances ? idsOrInstances : Resource.find(filter),
				Booking.occupied(ids, from, to, { includeOverstay })
			]),
			opened = Resource.isOpen(resources, from, to),
			occupiedIds = new Set(occupied.map(o => o.resourceId)),
			available = opened.filter(({ id, shared, capacity }) => {
				const resourceId = String(id)
				if (!shared) return !occupiedIds.has(resourceId)

				const sold = occupied
					.filter(o => o.resourceId === resourceId)
					.reduce((total, { quantity }) => total + quantity, 0)

				return sold !== capacity
			})

		const prioritized = await Resource.prioritize(from, to, available)

		return { available: prioritized, occupiedIds }
	}

	/**
	 * Prioritize list of available resources
	 * @param	{Date} from
	 * @param	{Date} to
	 * @param	{Resource[]} available resources
	 * @return	{Resource[]} prioritized
	 */
	Resource.prioritize = async function(from, to, available = []) {
		const { Booking } = Resource.app.models,
			ids = available.map(r => r.id),
			resourceHours = available.reduce((res, resource) => {
				const { hours } = resource.toJSON()

				res.periods.push(...hours.periods)
				hours.specific && res.specific.push(...hours.specific)
				return res
			}, { periods: [], specific: [] }),
			endOfDay = openUntil(resourceHours, to),
			occupiedAfter = await Booking.occupied(ids, to, endOfDay),		// sorted by startTime
			prioritized = []

		for (const id of ids) {
			const [ earliest = {} ] = occupiedAfter.filter(r => r.resourceId === id),
				{ startTime: next = endOfDay } = earliest

			prioritized.push({ id, next })
		}

		prioritized.sort((a, b) => b.next - a.next)		// latest first

		// sort available per proritized ordering
		return available.sort((a, b) => prioritized.findIndex(r => r.id === a.id) - prioritized.findIndex(r => r.id === b.id))
	}

	/**
	 * Shortlist Resources (from given list) that are Open for given time period (from-to)
	 * @param	{Resource[]} resources
	 * @param	{Date} from
	 * @param	{Date} to
	 * @return	{Resource[]}
	 */
	Resource.isOpen = function(resources = [], from, to) {
		return resources.filter(resource => resource.isOpen(from, to))
	}

	/**
	 * Check if resource is available for time period & capacity
	 * @param	{String} resourceId
	 * @param	{Number} capacity - number of people
	 * @param	{Date} at - start time
	 * @param	{Number} duration - in MINUTES
	 * @return	{Resource|void} resource when available, undefined otherwise
	 */
	Resource.isAvailable = async function (resourceId, capacity, at, duration = 0) {
		const resource = await Resource.findById(resourceId)
		return await resource.isAvailable(capacity, at, duration) ? resource : undefined
	}

	// -----  Instance Methods  -----

	/**
	 * Check if Resource is Open for time period
	 * @param	{Date} from
	 * @param	{Date} to
	 * @return	{Boolean}
	 */
	Resource.prototype.isOpen = function(from, to) {
		const { hours } = this
		return isOpen(hours, from, to)
	}

	/**
	 * Check if this resource is available for time period & capacity
	 * @param	{Number} capacity - number of people
	 * @param	{Date} at - start time
	 * @param	{Number} duration - in MINUTES
	 * @return	{Boolean}
	 */
	Resource.prototype.isAvailable = async function (capacity, at, duration = 0) {
		const { id, shared, capacity: resourceCapacity } = this,
			{ Booking } = Resource.app.models,
			from = at || new Date(),
			to = new Date(from.getTime() + duration * 60000 - 1),
			resourceId = String(id)

		// 1. Check if resource is open during the requested time period
		if (!this.isOpen(from, to)) {
			return false
		}
		// 2. Check if resource has sufficient capacity for the requested party size
		if (capacity > resourceCapacity) {
			return false
		}

		// 3. Check if resource is not already occupied during the requested time period
		const occupied = await Booking.occupied([ resourceId ], from, to)

		// For non-shared resources, any booking means it's not available
		if (!shared) {
			return occupied.length === 0
		}

		// For shared resources, check if there's enough remaining capacity
		const sold = occupied
			.filter(o => o.resourceId === resourceId)
			.reduce((total, { quantity }) => total + quantity, 0)

		return (resourceCapacity - sold) >= capacity
	}

	/**
	 * Quantity available for booking
	 * @param	{Date} from
	 * @param	{Date} to
	 * @return	{Number}
	 */
	Resource.prototype.balance = async function(from, to) {
		const { id, shared, capacity } = this,
			resourceId = String(id),
			{ Booking } = Resource.app.models,
			occupied = await Booking.occupied([ resourceId ], from, to)

		if (!shared) return occupied.length ? 0 : 1

		const sold = occupied
			.filter(o => o.resourceId === resourceId)
			.reduce((total, { quantity }) => total + quantity, 0)

		return capacity - sold
	}

	// -----  Operation Hooks  -----

	Resource.observe('before save', async ({ instance, data, isNewInstance }) => {
		const { app } = Resource,
			updated = instance || data,
			{ hours, timeZone, adjacentIds } = updated

		if (hours) {	// inject computed start/end time
			const { startTime, endTime } = startEndOf(hours)

			updated.startTime = startTime
			updated.endTime = endTime
		}

		if (isNewInstance) {
			if (!timeZone) {
				const { timeZone } = app.getSettings(LOCALE)
				instance.timeZone = timeZone
			}
		}

		// Validate adjacent resources have same timezone
		if (adjacentIds?.length) {
			const { timeZone } = app.getSettings(LOCALE),
				adjacentResources = await Resource.find({
					where: { id: { inq: adjacentIds } }
				}),
				adjacentTimezones = adjacentResources.map(r => r.timeZone || timeZone)

			if (new Set([ timeZone, ...adjacentTimezones ]).size > 1) {
				throw new Error('Adjacent resources must have the same timezone')
			}
		}
	})

	Resource.observe('after save', async ({ instance, isNewInstance }) => {
		if (!isNewInstance) return

		const { name: summary, description } = instance,
			{ app } = Resource,
			{ timeZone: tz } = app.getSettings(LOCALE),
			{ calendar = {} } = app.getSettings(BOOKING),
			{ enabled, user } = calendar,
			timeZone = instance.timeZone || tz,
			data = { summary, description, timeZone }

		if (enabled && user) {
			try {
				const google = await Resource.getProvider(GOOGLE),
					{ id: calendarId } = await google.calendars.create(data)

				await Promise.all([
					google.calendars.share(calendarId, user, undefined, OWNER),
					instance.updateAttributes({ calendarId })
				])
			}
			catch (err) {
				appNotify('[Resource] unable to create/share calendar', { err, data })
			}
		}
	})

	Resource.observe('before delete', async ({ where, hookState }) => {
		const { app } = Resource,
			{ calendar = {} } = app.getSettings(BOOKING),
			{ deleteWithResource } = calendar

		if (deleteWithResource) {
			const instance = await Resource.findOne({ where }),
				{ calendarId } = instance

			hookState.calendarId = calendarId
		}
	})

	Resource.observe('after delete', async ({ hookState }) => {
		const { calendarId } = hookState

		if (!calendarId) return

		try {
			const google = await Resource.getProvider(GOOGLE)
			await google.calendars.delete(calendarId)
		}
		catch (err) {
			appNotify('[Resource] unable to delete calendar', { err, calendarId })
		}
	})
}
