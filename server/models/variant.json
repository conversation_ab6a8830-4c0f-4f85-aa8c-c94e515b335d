{"name": "<PERSON><PERSON><PERSON>", "plural": "Variants", "description": "A product variant is a different version of a product, such as differing sizes or differing colors.", "base": "PersistedModel", "idInjection": true, "strict": false, "options": {}, "mixins": {"Multitenant": true, "MongoCollection": true, "FindOneAndUpdate": true, "Timestamp": true, "Errors": true, "Queue": true, "FindVariant": true, "AppLink": true, "Bag": true, "Image": {"model": "ProductImage", "relation": "images", "description": "Upload pictures of <PERSON><PERSON><PERSON>."}, "Payment": true, "Order": true, "Buy": true, "BuyApi": true, "ProductApi": true, "BagApi": true, "FulfillDigital": true, "ProofOfPurchase": true, "DisableAllRemotes": {"create": true, "upsert": true, "updateAll": false, "prototype.patchAttributes": true, "find": true, "findById": true, "findOne": true, "findOrCreate": true, "createChangeStream": false, "deleteById": true, "count": true, "exists": true, "replaceById": true, "replaceOrCreate": true, "upsertWithWhere": true, "prototype.__create__pricings": true, "prototype.__get__pricings": true, "prototype.__updateById__pricings": true, "prototype.__delete__pricings": true}}, "properties": {"kind": {"type": "string", "enum": ["product", "membership", "giftcard", "storedvalue", "booking", "ticket", "voucher", "widget", "nft"], "default": "product", "required": true}, "title": {"type": "String", "max": 128, "description": "Displayed in checkout and orders"}, "position": {"type": "number", "default": 1, "description": "The order of the product variant in the list of product variants. 1 is the first position"}, "gtin": {"type": {"code": {"type": "String", "description": "Barcode type, ie. GTIN-8 (EAN-8), GTIN-12 (UPC), GTIN-13 (EAN-13), GTIN-14 (EAN/ITF)"}, "value": {"type": "String"}}, "default": {}}, "sku": {"type": "String", "description": "A unique identifier for the product"}, "mpn": {"type": "String", "description": "Manufacturer Part Number"}, "cost": {"type": {"value": {"type": "number"}, "currency": {"type": "String"}}}, "weight": {"type": {"value": {"type": "number", "description": "The weight of product, which will help determine shipping costs."}, "unit": {"type": "String", "enum": ["g", "kg", "lb", "oz", "ml", "l"], "description": "weight units"}, "count": {"type": "number", "description": "quantity, multiply by value to get total weight (used by Grab)"}}}, "inventory": {"type": {"management": {"type": "String", "enum": ["shopify", "perkd", "resource"], "description": "The fulfillment service tracking the number of items in stock. no value => no tracking"}, "policy": {"type": "string", "enum": ["deny", "continue"], "default": "deny", "description": "continue = allow customers to place orders for variant even when it's out of stock"}, "lowQuantityWarningThreshold": {"type": "number", "default": 0}, "quantity": {"type": "number", "default": 0}}, "default": {}}, "channels": {"type": [{"type": "string", "enum": ["store", "website", "perkd", "shopify", "grabfood", "grabmart", "ubereats", "foodpanda"]}], "description": "Where this variant is listed for sale, provider key", "default": []}, "prices": {"type": [{"type": "Price"}]}, "taxable": {"type": "Boolean", "default": true}, "taxCode": {"type": "String"}, "variations": {"type": [{"type": "Variation"}], "default": [], "description": "Combination of variation values that defines Variant"}, "options": {"type": [{"type": "Option"}], "default": [], "description": "List of options available, options may add to price of variant"}, "attributes": {"type": "object", "description": "Custom attributes"}, "unitCostMeasure": {"type": {"value": {"type": "number", "default": 1, "description": "Increments of unit for costing"}, "unit": {"type": "String", "enum": ["day", "hour", "minute", "kg", "g"], "description": "The unit of the measure. eg. 'ml', 'kg'"}}}, "unitPriceMeasure": {"type": {"unit": {"type": "String", "enum": ["day", "hour", "minute", "kg", "g", "ml", "l"], "description": "The unit of the measure. eg. 'ml', 'kg'"}, "value": {"type": "number", "default": 1, "description": "Increments of unit sold"}}}, "minOrderQuantity": {"type": "number", "default": 1}, "fulfillmentService": {"type": "String", "enum": ["none", "store", "digital", "shopify", "kitchen", "vending"], "default": "none", "description": "Provider to perform fulfillment, digital: campaign to handle event, manual: no action"}, "digital": {"type": {"masterId": {"type": "string"}, "widgetKey": {"type": "string"}}}, "storedValue": {"type": {"balance": {"type": "number", "default": 0, "description": "Balance stored value (integer)"}, "currency": {"type": {"code": {"type": "string", "max": 8, "description": "ISO 4217 currency code, 'CREDIT' or 'POINT'"}, "precision": {"type": "number", "default": 0, "description": "Decimal precision, ie. 2 => amount of 1000 = $10.00"}}, "required": true}}, "description": "Use for initial value or top up during order fulfillment"}, "preparation": {"type": {"time": {"type": "number", "description": "Production time in seconds"}, "placeId": {"type": "string", "description": "Kitchen (station) to fulfill from"}}}, "external": {"type": {"shopify": {"type": {"variantId": {"type": "String"}, "inventoryItemId": {"type": "String"}, "inventoryLocationId": {"type": "String"}, "shop": {"type": "String", "description": "Shopify shopName"}}}}, "default": {}}, "visible": {"type": "Boolean", "default": true}, "globalize": {"type": "Globalize", "default": {}}, "add2bagUri": {"type": "string", "description": "URI to default add-to-bag page"}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}, "deletedAt": {"type": "date"}}, "hidden": [], "validations": [], "relations": {"product": {"type": "belongsTo", "model": "Product", "foreignKey": "productId"}, "images": {"type": "referencesMany", "model": "ProductImage", "foreignKey": "imageIds"}, "vendor": {"type": "belongsTo", "model": "Business", "foreignKey": "businessId"}, "resource": {"type": "belongsTo", "model": "Resource", "foreignKey": "resourceId"}, "pricings": {"type": "hasMany", "model": "Pricing", "foreignKey": "variantId"}, "soldAt": {"type": "belongsTo", "model": "Place", "foreignKey": "placeId"}}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"order": {"description": "Order only (no payment)", "http": {"path": "/order", "verb": "post"}, "accepts": [{"arg": "userId", "type": "string", "description": "Perkd personId, omit for anonymous (web) orders"}, {"arg": "order", "type": "object", "required": true, "description": "see @perkd/commerce"}, {"arg": "pricings", "type": ["object"], "required": true, "description": "see @perkd/commerce"}, {"arg": "options", "type": "object", "description": "{ reservation, through, cardId, description, metadata, notification, noNotify }"}], "returns": {"type": "object", "root": true, "description": "{ orderId, fulfilled }"}}, "cancelDigitalFulfill": {"description": "Cancel fulfilled digital products - Stored Value, Voucher/Tickets, Gift Cards", "http": {"path": "/fulfillments/digital/cancel", "verb": "post"}, "accepts": [{"arg": "fulfilled", "type": "array", "required": true, "description": "Fulfilled items to cancel"}], "returns": {"type": "array", "root": true}}}}