{"name": "grab", "plural": "grab", "base": "ProductProvider", "strict": true, "mixins": {"Errors": true, "Provider": true, "DisableAllRemotes": {"resetProvider": false, "prototype.resetProvider": false}}, "properties": {}, "acls": [], "scopes": {}, "methods": {"foodMenu": {"description": "Get GrabFood Menu for outlet  (Grab callback API)", "http": {"path": "/food/merchant/menu", "verb": "get"}, "accepts": [{"arg": "merchantID", "type": "string", "http": {"source": "query"}, "required": true, "description": "Store Id"}], "returns": {"type": "object", "root": true, "description": "{ currency, sellingTimes, categories }"}}, "martMenu": {"description": "Get GrabMart Menu for outlet  (Grab callback API)", "http": {"path": "/mart/merchant/menu", "verb": "get"}, "accepts": [{"arg": "merchantID", "type": "string", "http": {"source": "query"}, "required": true, "description": "Store Id"}], "returns": {"type": "object", "root": true, "description": "{ currency, sellingTimes, categories }"}}, "menuRefresh": {"description": "Notify Grab to refresh menu (Grab callback API)", "http": {"path": "/refresh/menu", "verb": "post"}, "accepts": [{"arg": "provider", "type": "string", "required": true, "description": "provider name: grabfood or grabmart"}, {"arg": "shop", "type": "string", "description": "shop name"}]}, "menuItemRefresh": {"description": "Update Grab menu item's price and inventory", "http": {"path": "/refresh/menuItem", "verb": "post"}, "accepts": [{"arg": "provider", "type": "string", "required": true, "description": "provider name: grabfood or grabmart"}, {"arg": "shop", "type": "string", "description": "shop name"}, {"arg": "itemId", "type": "string", "required": true, "description": "item id"}, {"arg": "inventory", "type": "number", "description": "inventory"}, {"arg": "unitPrice", "type": "number", "description": "unit selling price"}]}}}