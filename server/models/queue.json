{"name": "Queue", "plural": "Queues", "base": "PersistedModel", "idInjection": true, "strict": true, "options": {}, "mixins": {"Multitenant": true, "Common": true, "Timestamp": true, "Queue": true, "Notify": true, "DisableAllRemotes": {"create": true, "upsert": true, "prototype.patchAttributes": true, "find": true, "findById": true, "findOne": true, "findOrCreate": false, "deleteById": true, "confirm": false, "count": true, "exists": false, "replaceById": false, "replaceOrCreate": false, "upsertWithWhere": false}}, "properties": {"kind": {"type": "string", "required": true, "enum": ["table"], "description": "Type of queue, follow Resource kind"}, "capacity": {"type": "number", "required": true, "min": 1, "description": "Party size"}, "tier": {"type": "string", "required": true, "enum": ["regular", "vip", "asap"], "default": "regular", "description": "Tier of customer, 'asap' is highest priority"}, "priority": {"type": "number", "required": true, "default": 0, "description": "Computed dynamic priority, higher is more important"}, "status": {"type": "string", "required": true, "default": "waiting", "enum": ["waiting", "assigned", "notified", "confirmed", "completed", "skipped", "left"]}, "preferences": {"allowCombined": {"type": "Boolean"}, "adjacentOnly": {"type": "Boolean"}}, "digitalCard": {"type": {"id": {"type": "string", "description": "Card instance id"}}, "description": "Digital card information"}, "source": {"type": "string", "required": true, "enum": ["walkin", "noshow", "overflow"]}, "hadReservation": {"type": "boolean", "default": false, "description": "Whether this queue entry had a reservation that couldn't be fulfilled immediately"}, "originalReservationId": {"type": "string", "description": "The original reservation ID if this queue entry had a reservation"}, "scheduledAt": {"type": "date", "description": "Expected service time (local time)"}, "endTime": {"type": "date", "description": "Time after which the queue entry is considered abandoned"}, "when": {"type": {"waited": {"type": "date", "default": null, "description": "Time of joining queue"}, "notified": {"type": "date", "default": null, "description": "Time customer was notified of availability"}, "confirmed": {"type": "date", "default": null, "description": "Time customer confirmed acceptance"}, "completed": {"type": "date", "default": null, "description": "Time customer served, eg. seated"}, "skipped": {"type": "date", "default": null, "description": "Time customer was skipped"}, "left": {"type": "date", "default": null, "description": "Time customer abandoned queuing"}}, "default": {}, "description": "All in local time"}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}, "deletedAt": {"type": "date"}}, "relations": {"person": {"type": "belongsTo", "model": "Person", "foreignKey": "personId"}, "membership": {"type": "belongsTo", "model": "Membership", "foreignKey": "membershipId"}, "place": {"type": "belongsTo", "model": "Place", "foreignKey": "placeId"}, "booking": {"type": "belongsTo", "model": "Booking", "foreignKey": "reservationId"}}, "indexes": {"status_idx": {"status": 1}, "priority_status_idx": {"priority": 1, "status": 1}}}