/**
 *  @module Model:UberEats	ProductProvider
 */
const { Providers, Products } = require('@crm/types'),
	{ MenuType, products2Categories, toItem, toAppOrder } = require('@provider/ubereats')

const { UBEREATS } = Providers.PROVIDER,
	{ DELIVER } = MenuType,
	{ ACTIVE } = Products.Availability

module.exports = function(UberEats) {

	UberEats.handleExtProductsRefresh = async function (data = {}) {
		const { app } = UberEats,
			{ Place } = app.models,
			{ store_id } = data,
			[ ubereats, store ] = await Promise.all([
				this.getProvider(UBEREATS),
				Place.findOneByProviderStoreId(UBEREATS, store_id)
			]),
			{ id: placeId = null } = store ?? {},
			products = await this.findProducts(UBEREATS, String(placeId)),
			menu = {
				menu_type: DELIVER,
				menus: [],
				categories: [],
				items: [],
				modifier_groups: []
			}

		await ubereats.products.refresh(menu)
		appNotify(`[UberEats] menu refreshed, placeId: ${placeId}`)
	}

	// sync menu to ubereats when place opening hours are changed
	UberEats.handlePlaceUpdated = async function (data = {}) {
		const { external = {}, context = {} } = data,
			{ storeId: merchantId } = external[UBEREATS] ?? {},
			{ delta = [] } = context

		if (!merchantId) return // if this store is not connected to ubereats, ignore
		if (!delta.find(d => d.path.includes('/openingHours') || d.path.includes('/deliver'))) return // if openingHours or deliver is not changed, ignore

		await this.menuRefresh(merchantId)
			.catch(err => appNotify(`[UberEats] menuRefresh failed, merchantId: ${merchantId}`, err))
	}

	// ---  APIs  ---

	// /**
	//  * Get the latest outlet menu(s) from the partner's database
	//  * @param	{String} merchantID - grab store id
	//  * @return	{Promise<Object>} - { currency, sellingTimes, categories }
	//  */
	// 	UberEats.getMenu = async function(shopid, MenuType) {
	// 		return UberEats.menu(UBEREATS, shopid, foodCategories)
	// 	}

	/**
	 * Push menu to UberEats
	 * @param	{String} - shopId
	 * @return	{Number} - items pushed
	 */
	UberEats.syncMenu = async function (shopid) {
		return this.handleExtProductsRefresh({ store_id: shopid })
	}

	UberEats.toAppOrder = async function (data) {
		const { app } = UberEats,
			{ Place } = app.models,
			{ id: merchantId } = data.store,
			[ ubereats, store ] = await Promise.all([
				this.getProvider(UBEREATS),
				Place.findOneByProviderStoreId(UBEREATS, merchantId)
			]),
			storeId = store ? String(store.id) : undefined,
			order = ubereats.orders.toAppOrder({ ...data, storeId })
		return order
	}

	/**
	 * For Shopify / Staff App - refresh menu with ubereats
	 * @param	{String} provider
	 * @param	{String} shop - UberEats merchantId
	 */
	UberEats.menuRefresh = async function(shop) {
		const { Product, Variant, Place } = UberEats.app.models,
			[ ubereats, place ] = await Promise.all([
				this.getProvider(UBEREATS),
				Place.findOneByProviderStoreId(UBEREATS, shop)
			])

		if (!place) throw new Error(`[UberEats] menuRefresh, shop: ${shop} not found`)

		const { id: placeId } = place,
			variantsFilter = { where: {
				channels: UBEREATS,
				'preparation.placeId': placeId,
				deletedAt: null,
				visible: true
			}, include: {
				relation: 'images',
				scope: {
					where: { visible: true },
					order: 'position DESC'
				}
			} },
			variants = await Variant.find(variantsFilter),
			productIds = variants.map(v => v.productId),
			productFilter = { where: {
				id: { inq: productIds },
				visible: true,
				deletedAt: null,
				availability: ACTIVE
			} },
			products = await Product.find(productFilter),
			{ menuId, language } = ubereats.options,
			productList = products.map(p => p.toJSON()),
			variantList = variants.map(v => v.toJSON()),
			menu = ubereats.products.fromProduct(productList, variantList, place, menuId, language)

		try {
			return ubereats.products.update(menu, shop)
		}
		catch (error) {
			console.error(`[UberEats] menuRefresh, shop: ${shop} %j`, error)
			throw error
		}
	}

	UberEats.getMenu = async function(shop) {
		const ubereats = await this.getProvider(UBEREATS)
		const menu = await ubereats.products.get(shop)
		return menu
	}
}
