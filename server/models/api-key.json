{"name": "<PERSON><PERSON><PERSON><PERSON>", "plural": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "base": "PersistedModel", "idInjection": true, "options": {"validateUpsert": true}, "mixins": {"Multitenant": true, "Timestamp": true}, "properties": {"name": {"type": "string", "required": true}, "key": {"type": "string", "required": true, "index": {"unique": true}}, "tenantCode": {"type": "string", "required": true}, "active": {"type": "boolean", "default": true}, "expiresAt": {"type": "date"}}, "validations": [], "relations": {}, "acls": [], "methods": {}}