/**
 *  @module Model:Product
 *
 *  References:
 *		GTIN 	http://www.gtin.info
 *		Unique Product Identifiers (Google)	https://support.google.com/merchants/answer/160161?hl=en
 *
 *  Google
 * 		https://support.google.com/merchants/answer/7052112
 * 		https://developers.google.com/shopping-content/v2/reference/v2/products
 *
 * 	Shopify
 * 		https://help.shopify.com/api/reference/product
 * 		https://help.shopify.com/api/reference/product_variant
 *
 * 	Reaction Commerce
 * 		https://docs.reactioncommerce.com/docs/concepts-products
 */
const { Products } = require('@crm/types')

const { MENU } = Products.Channel

module.exports = function(Product) {

	// -----  Static Methods  -----

	Product.gtinLookup = async function(upc) {
		const { GTIN } = Product.app.models
		return GTIN.lookup(upc)
	}

	// ---  Methods required by AppLink mixin  ---

	Product.prototype.applinkPrepare = async function(fallbackActionId, options = {}) {
		const { id: ownerId } = this,
			{ Business, ProductImage } = Product.app.models,
			{ Perkd } = appModule('perkd'),
			{ state, shopUri } = options,
			imageFilter = {
				where: { ownerId },
				order: 'position ASC',
			},
			businessFilter = {
				where: { isMain: true },
				fields: [ 'id', 'name' ],
			},
			filter = {
				where: { deletedAt: null },
				order: 'position ASC',
			},
			[ action, images, business, variants ] = await Promise.all([
				Perkd.actions.get(fallbackActionId),
				ProductImage.find(imageFilter),
				Business.findOne(businessFilter),
				this.variants.find(filter),
			]),
			links = variants.reduce((obj, v) => {
				obj[v.id] = ''
				return obj
			}, {})

		for (const variant of variants) {
			const payload = Product.applinkAction(action.data.cardMasterIds, this, variant, images),
				addtobag = await Perkd.actions.uri(fallbackActionId, payload)

			links[variant.id] = addtobag
		}

		return Product.applinkSiteData(this, variants, business, images, links, state, shopUri)
	}

	// -----  Operation hooks  -----

	/**
	 * Emit Menu event data (also used by Product)
	 */
	Product.emitMenuEvent = async function (product, changes, event) {
		const variants = await product.variants.find()

		for (const { id: variantId, channels, placeId } of variants) {
			if (channels.includes(MENU)) {
				appEmit(event, { ...changes, variantId, placeId })
			}
		}
	}

	Product.observe('before save', async ({ currentInstance, data, isNewInstance, hookState }) => {
		console.log('debug [Product before save] %j', { currentInstance, data, isNewInstance, hookState })
		const { description, availability, tags } = data ?? {}

		if (description || availability || tags) {
			hookState.emitMenu = true
		}
	})

	Product.observe('after save', async ({ instance, isNewInstance, hookState }) => {
		console.log('debug [Product after save] %j', { instance, isNewInstance, hookState })
		const { Event } = Product.app,
			{ description, availability, tags } = instance,
			changes = { description, availability, tags },
			{ emitMenu } = hookState

		if (isNewInstance) {
			appEmit(Event.product.created, instance)
		}
		else {
			appEmit(Event.product.updated, instance)

			if (emitMenu) {
				Product.emitMenuEvent(instance, changes, Event.variant.menu.updated)
			}
		}
	})

	Product.observe('before delete', async ({ instance, where, hookState }) => {
		hookState.instance = instance || await Product.findOne({ where })
	})

	Product.observe('after delete', async ({ hookState }) => {
		const { Event, models } = Product.app,
			{ Variant } = models,
			{ instance } = hookState,
			{ id: productId } = instance ?? {}

		if (instance) {
			appEmit(Event.product.deleted, instance)
			Variant.deleteAll({ productId })
		}
	})
}
