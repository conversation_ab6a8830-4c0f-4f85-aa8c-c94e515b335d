/**
 *  @module Model:OrderProvider
 */

const { Persons, Settings } = require('@crm/types'),
	{ REGISTRY } = require('@perkd/event-registry-crm'),
	{ PROVIDER: PROVIDER_ERR } = require('@perkd/errors/dist/service')

const { CUSTOMER } = Persons.Identities,
	{ EVENT_NOT_HANDLED, HANDLER_NOT_IMPLEMENTED } = PROVIDER_ERR,
	{ sales } = REGISTRY,
	// local events
	ORDER = {
		CREATED: sales.order.created,
		PAID: sales.order.paid,
		UPDATED: sales.order.updated,
		FULFILLED: sales.order.fulfilled,
		CANCELLED: sales.order.cancelled,
	},
	FULFILLMENT = {
		DELIVER_PACKED: sales.fulfillment.deliver.packed
	},
	// external events
	ORDER_SCHEDULED = 'order.scheduled',
	ORDER_REQUEST = 'order.request',
	ORDER_CREATED = 'order.created',
	ORDER_PAID = 'order.paid',
	ORDER_CANCELLED = 'order.cancelled',
	ORDER_DELETED = 'order.deleted',
	// fulfillment
	FULFILLMENT_CREATED = 'fulfillment.created',
	FULFILLMENT_UPDATED = 'fulfillment.updated',
	FULFILLMENT_ACCEPTED = 'fulfillment.accepted',
	FULFILLMENT_ALLOCATED = 'fulfillment.allocated',
	FULFILLMENT_ARRIVED = 'fulfillment.arrived',
	FULFILLMENT_NEARBY = 'fulfillment.nearby',
	FULFILLMENT_COLLECTED = 'fulfillment.collected',
	FULFILLMENT_DELIVERED = 'fulfillment.delivered',
	FULFILLMENT_CANCELLED = 'fulfillment.cancelled',
	FULFILLMENT_FAILED = 'fulfillment.failed',
	// reserve (stock) - machine
	RESERVE_SUCCESS = 'reserve.success',
	RESERVE_FAILED = 'reserve.failed',
	// refund
	REFUND_CREATED = 'refund.created',
	FULFILL_EVENTS = [ FULFILLMENT_CREATED, FULFILLMENT_UPDATED, FULFILLMENT_ACCEPTED, FULFILLMENT_ALLOCATED, FULFILLMENT_NEARBY,
		FULFILLMENT_ARRIVED, FULFILLMENT_COLLECTED, FULFILLMENT_DELIVERED, FULFILLMENT_CANCELLED, FULFILLMENT_FAILED ],
	Q_EXTERNAL_EVENTS = 'providerExternalEvents',
	Q_INTERNAL_EVENTS = 'providerInternalEvents'

module.exports = function(OrderProvider) {

	OrderProvider.providerExternalEvents = async function(name, data = {}) {
		const { modelName: provider } = this,
			{ id = this.orderIdOf(data) } = data

		appNotify(`[${provider}]providerExternalEvents`, data)

		return this.queue(`${Q_EXTERNAL_EVENTS}:${provider}:${id}`, async () => {
			switch (name) {
			// order
			case ORDER_SCHEDULED:
				return this.handleExtOrderScheduled(data)

			case ORDER_REQUEST:
				return this.handleExtOrderRequest(data)

			case ORDER_CREATED:
				return this.handleExtOrderCreated(data)

			case ORDER_PAID:
				return this.handleExtOrderPaid(data)

			case ORDER_CANCELLED:
				return this.handleExtOrderCancelled(data)

			case ORDER_DELETED:
				return this.handleExtOrderDeleted(data)

			// refund
			case REFUND_CREATED:
				return this.handleExtOrderRefund(data)

			// reserve (stock)
			case RESERVE_SUCCESS:
				return this.handleExtOrderReserveSuccess(data)

			case RESERVE_FAILED:
				return this.handleExtOrderReserveFailed(data)

			default:
				if (FULFILL_EVENTS.includes(name)) {
					return this.handleExtFulfillmentEvents(name, data)
				}
				appNotify(EVENT_NOT_HANDLED, { provider, name, data }, 'error')
			}
		})
	}

	/**
	 * Call internal event handlers of providers
	 * 	IMPORTANT - ALL events are forwarded to provider handlers (including those NOT theirs)
	 * 		- provider event handlers MUST ignore those not theirs if necessary
	 * 		- usually can distinguish with acquired.attributedTo.type === provider name
	 */
	OrderProvider.providerInternalEvents = async function(name, data = {}) {
		const { modelName: provider } = this,
			{ id } = data

		return this.queue(`${Q_INTERNAL_EVENTS}:${provider}:${id}`, async () => {
			switch (name) {
			case ORDER.CREATED:
				return this.handleOrderCreated(data)

			case ORDER.PAID:
				return this.handleOrderPaid(data)

			case ORDER.UPDATED:
				return this.handleOrderUpdated(data)

			case ORDER.FULFILLED:
				return this.handleOrderFulfilled(data)

			case ORDER.CANCELLED:
				return this.handleOrderCancelled(data)

			case FULFILLMENT.DELIVER_PACKED:
				return this.handleFulfillmentDeliverPacked(data)

			default:
				appNotify(EVENT_NOT_HANDLED, { provider, name, data }, 'error')
			}
		})
	}

	OrderProvider.handleExtFulfillmentEvents = async function (name, data = {}) {
		const { modelName: provider, app } = this,
			{ Fulfillment } = app.models,
			orderId = this.orderIdOf(data),
			fulfillment = await Fulfillment.findOneByProviderOrderId(provider, orderId)

		if (!fulfillment && name !== FULFILLMENT_CREATED) {
			throw new Error(`[${provider}]fulfillment not found (id: ${orderId}) data: ${JSON.stringify(data)}`)
		}

		switch (name) {
		case FULFILLMENT_CREATED:
			return this.handleExtOrderFulfillmentCreated(fulfillment, data)

		case FULFILLMENT_UPDATED:
			return this.handleExtOrderFulfillmentUpdated(fulfillment, data)

		case FULFILLMENT_ACCEPTED:
			return this.handleExtOrderFulfillmentAccepted(fulfillment, data)

		case FULFILLMENT_ALLOCATED:
			return this.handleExtOrderFulfillmentAllocated(fulfillment, data)

		case FULFILLMENT_NEARBY:
			return this.handleExtOrderFulfillmentNearby(fulfillment, data)

		case FULFILLMENT_ARRIVED:
			appEmit(sales.fulfillment.deliver.arrived, fulfillment)
			break

		case FULFILLMENT_COLLECTED:
			return this.handleExtOrderFulfillmentCollected(fulfillment, data)

		case FULFILLMENT_DELIVERED:
			return this.handleExtOrderFulfillmentDelivered(fulfillment, data)

		case FULFILLMENT_CANCELLED:
			return this.handleExtOrderFulfillmentCancelled(fulfillment, data)

		case FULFILLMENT_FAILED:
			return this.handleExtOrderFulfillmentFailed(fulfillment, data)
		}
	}

	// ----  Order Provider common behaviors

	OrderProvider.orderIdOf = function(data) {
		const { id = null, orderId, order_id } = data
		return orderId ?? order_id ?? id
	}

	/**
	 * Build external Order from CRM order
	 * @param	{Object} api of provider
	 * @param	{Object} crmOrder
	 * @param	{Object[]} crmFulfillments
	 * @param	{Object} options
	 * 			{Boolean} markPaid - coerce status to paid
	 * 			{Boolean} linkToMerchant - associate order with business/merchant (marketplace) instead of person when true
	 * @return	{Promise<Object>} {provider} order data
	 */
	OrderProvider.buildOrder = async function(api, crmOrder = {}, crmFulfillments = [], options = {}) {
		const { app } = this,
			{ Person, Place, Business } = app.models,
			{ noReceiptNumber } = app.getSettings(Settings.Name.ORDER),
			{ name } = api,
			{ personId, storeId, acquired = {} } = crmOrder,
			{ attributedTo = {} } = acquired,
			{ name: shop } = attributedTo,
			{ markPaid, linkToMerchant } = options,
			[ store, identity ] = await Promise.all([
				storeId && Place.findById(storeId),
				personId && !linkToMerchant && Person.findIdentity(String(personId), name, CUSTOMER, shop),
			]),
			{ external = {}, ownerId } = store ?? {},
			customerId = linkToMerchant
				? await Business.findIdentity(String(ownerId), name, CUSTOMER, shop)
				: identity,
			{ storeId: locationId } = external

		if (noReceiptNumber) {
			crmOrder.receipt.number = undefined
		}

		return api.orders.fromOrder(crmOrder, crmFulfillments, { markPaid, customerId, locationId })
	}

	OrderProvider.redeemOffers = async function(codes = [], channel, personId, at) { // FIXME: replace offerId
		const { Offer } = this.app.models
		return Offer.redeemByCodes(codes, channel, personId, at)
	}

	/**
	 * Find or Create CRM Place for Shopify store
	 * @param	{String} provider
	 * @param	{String} id - Shopify location_id
	 * @return	{Promise<Place|void>}
	 */
	OrderProvider.findOrCreateStore = async function(provider, id) {
		const { Place } = this.app.models
		return id ? Place.findOrCreateProviderStore(provider, { id }) : undefined
	}

	// ----  Provider-specific methods  ----

	// internal event handlers
	OrderProvider.handleOrderCreated
	= OrderProvider.handleOrderPaid
	= OrderProvider.handleOrderUpdated
	= OrderProvider.handleOrderFulfilled
	= OrderProvider.handleOrderCancelled
	= OrderProvider.handleFulfillmentDeliverPacked
	= function(order) {
								this.rejectErr(HANDLER_NOT_IMPLEMENTED)
							}

	// external Order event handlers
	OrderProvider.handleExtOrderScheduled
	= OrderProvider.handleExtOrderRequest
	= OrderProvider.handleExtOrderCreated
	= OrderProvider.handleExtOrderPaid
	= OrderProvider.handleExtOrderCancelled
	= OrderProvider.handleExtOrderDeleted
	= function (extOrder) {
								this.rejectErr(HANDLER_NOT_IMPLEMENTED)
							}

	// external Refund event handlers
	OrderProvider.handleExtOrderRefund = function(extOrder) {
		return this.rejectErr(HANDLER_NOT_IMPLEMENTED)
	}

	// external Fulfillment event handlers
	OrderProvider.handleExtOrderFulfillmentCreated
	= OrderProvider.handleExtOrderFulfillmentUpdated
	= OrderProvider.handleExtOrderFulfillmentAccepted
	= OrderProvider.handleExtOrderFulfillmentAllocated
	= OrderProvider.handleExtOrderFulfillmentNearby
	// = OrderProvider.handleExtOrderFulfillmentArrived
	= OrderProvider.handleExtOrderFulfillmentCollected
	= OrderProvider.handleExtOrderFulfillmentDelivered
	= OrderProvider.handleExtOrderFulfillmentCancelled
	= OrderProvider.handleExtOrderFulfillmentFailed
	= function (fulfillment, extFulfillment) {
											return this.rejectErr(HANDLER_NOT_IMPLEMENTED)
										}

	// external reserve (stock) event handlers
	OrderProvider.handleExtOrderReserveSuccess
		= OrderProvider.handleExtOrderReserveFailed
		= function (fulfillment, extFulfillment) {
				return this.rejectErr(HANDLER_NOT_IMPLEMENTED)
			}
}
