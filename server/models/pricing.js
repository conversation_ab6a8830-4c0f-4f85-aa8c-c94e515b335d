/**
 * @module Model: Pricing
 */
const { Settings, Bookings } = require('@crm/types'),
	{ nextOpen, dayjs } = require('@perkd/utils')

const { LOCALE } = Settings.Name,
	{ DEFAULT } = Settings,
	{ TIMEZONE } = DEFAULT,
	{ MINUTE } = Bookings.Unit

module.exports = function(Pricing) {

	/**
	 * Return pricings for variants
	 * @param	{Variant[]} variants
	 * @return	{Pricing[]}
	 */
	Pricing.forVariants = async function(variants = []) {
		const variantIds = variants.map(v => v.id),
			filter = {
				where: {
					variantId: { inq: variantIds }
				}
			}

		return Pricing.find(filter)
	}

	/**
	 * Shortlist Pricing (from list) applicable for given time period (from-to)
	 * @param	{Pricing[]} pricings
	 * @param	{Date} from - inclusive
	 * @param	{Date} to - inclusive
	 * @return	{Pricing[]}
	 */
	Pricing.applicable = function(pricings = [], from, to) {
		const { app } = Pricing,
			{ timeZone = TIMEZONE } = app.getSettings(LOCALE),
			endTime = dayjs(to).tz(timeZone).startOf(MINUTE),
			result = []

		let startTime = dayjs(from).tz(timeZone).startOf(MINUTE),
			count = pricings.length

		do {
			for (const pricing of pricings) {
				const next = pricing.nextApplicable(startTime, timeZone)

				if (next) {
					const { start } = next,
						nextEnd = dayjs(next.end).tz(timeZone).subtract(1, MINUTE),
						notEnded = nextEnd.isBefore(endTime),
						end = notEnded ? nextEnd : endTime

					result.push({ start, end: end.toDate(), pricing })

					if (notEnded) {
						startTime = end.add(1, MINUTE)
						count = pricings.length
						break
					}
					else {
						return result
					}
				}
				count--
			}
		} while (count > 0)

		return []
	}

	/**
	 * Next time slot pricing is available from onwards
	 * @param	{Date|Dayjs} from
	 * @param	{String} timeZone
	 * @return	{Object|void} { start, end }
	 */
	Pricing.prototype.nextApplicable = function(from, timeZone) {
		const { hours } = this,
			next = nextOpen(hours, from, timeZone)

		return next
			? (next.start <= from ? next : undefined)
			: undefined
	}

	/**
	 * Units for time period (rounded up to unit, >= value)
	 * @param	{Date} from
	 * @param	{Date} to
	 * @param	{Object} unitPriceMeasure
	 * @return	{Number} units
	 */
	Pricing.prototype.units = function(from, to, unitPriceMeasure = {}) {
		const { unit = MINUTE, value = 1 } = unitPriceMeasure,
			offset = unit === MINUTE ? 1 : 0,
			units = Math.ceil(dayjs(to).diff(from, unit, true)) + offset

		return Math.max(units, value)
	}
}
