{"name": "BundleValue", "plural": "BundleValues", "base": "Model", "description": "Bundle item", "idInjection": false, "strict": true, "mixins": {}, "properties": {"title": {"type": "String", "max": 128, "description": "Displayed in checkout and orders"}, "sku": {"type": "String", "description": "A unique identifier for the product"}, "kind": {"type": "String", "enum": ["product", "membership", "giftcard", "storedvalue", "booking", "ticket", "voucher", "widget", "nft"], "default": "product", "required": true}, "price": {"type": "Number", "default": 0, "description": "Overrides Variant price"}, "options": {"type": [{"type": "Option"}], "default": [], "description": "List of options available, options may add to price of variant"}, "images": {"type": [{"type": "String"}], "description": "List of product images"}}, "methods": {}}