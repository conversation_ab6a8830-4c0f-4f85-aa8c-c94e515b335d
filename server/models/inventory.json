{"name": "Inventory", "base": "Model", "strict": true, "options": {}, "mixins": {}, "properties": {"management": {"type": "Boolean", "default": false, "description": "true = inventory managed by Perkd"}, "policy": {"type": "string", "enum": ["deny", "continue"], "default": "deny", "description": "continue = allow customers to place orders fpr variant even when it's out of stock"}, "lowQuantityWarningThreshold": {"type": "number", "default": 0}, "quantity": {"type": "number", "default": 0}}, "acls": [], "methods": {}}