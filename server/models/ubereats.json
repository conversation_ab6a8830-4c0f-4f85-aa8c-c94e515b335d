{"name": "ubereats", "plural": "ubereats", "base": "ProductProvider", "strict": true, "mixins": {"Errors": true, "Provider": true, "DisableAllRemotes": {"resetProvider": false, "prototype.resetProvider": false}}, "properties": {}, "acls": [], "scopes": {}, "methods": {"syncMenu": {"description": "Sync products to UberEats (StaffWidget API)", "http": {"path": "/sync/menu", "verb": "post"}, "accepts": [], "returns": {}}, "toAppOrder": {"description": "Transform UberEats Order Event to App Order", "http": {"path": "/app/order", "verb": "post"}, "accepts": [{"arg": "data", "type": "object"}], "returns": {"type": "object", "root": true}}, "menuRefresh": {"description": "Refresh UberEats menu, include products and opening hours (Admin API)", "http": {"path": "/refresh/menu", "verb": "post"}, "accepts": [{"arg": "shop", "type": "string", "required": true, "description": "UberEats merchantId"}], "returns": {"type": "object", "root": true}}, "getMenu": {"description": "Get UberEats menu", "http": {"path": "/get/menu", "verb": "get"}, "accepts": [{"arg": "shop", "type": "string", "description": "UberEats merchantId"}], "returns": {"type": "object", "root": true}}}}