/**
 * @module Model: Queue
 * Provides queue management functionality. Handles queue entry creation, scheduling, and processing.
 */
const { Bookings, Queuings, Settings, Wallet, Notify } = require('@crm/types'),
	{ dayjs, cloneDeep } = require('@perkd/utils')

const { ENDED, NOSHOW } = Bookings.Status,
	{ WAITING, ASSIGNED, NOTIFIED, LEFT } = Queuings.Status,
	{ WALKIN } = Queuings.Source,
	{ BOOKING, QUEUING, LOCALE } = Settings.Name,
	{ QUEUEING, BOOK_TABLE } = Wallet.Widgets.Key,
	PROCESS_INTERVAL = 1000,
	Q_QUEUING = 'queuing'

module.exports = function(Queue) {

	/**
	 * Allows a customer to join the queue
	 * @param	{String} placeId
	 * @param	{String} kind - kind of resource
	 * @param	{Number} capacity - number of people (partySize)
	 * @param	{Object} preferences
	 *			{Boolean} allowCombined - Whether to allow combined resources
	 *			{Boolean} adjacentOnly - Whether to require adjacent resources
	 * @param	{Object} customer
	 *				{String} tier
	 *				{String} personId
	 *				{String} membershipId
	 *				{Object} digitalCard
	 * @param	{String} [source] - Source of queue entry
	 * @return	{Object} { kind, status, queueId, scheduledAt }
	 */
	Queue.join = async function (placeId, kind, capacity, preferences, customer = {}, source = WALKIN) {
		const { app } = Queue,
			{ Metric, Event } = app,
			{ timeZone } = app.getSettings(LOCALE),
			{ tier, personId, membershipId, cardId } = customer,
			priority = Queue.calculatePriority(kind, customer),
			scheduledAt = await Queue.schedule(placeId, kind, capacity, priority, preferences),
			waited = dayjs().tz(timeZone).toDate(),
			status = WAITING,
			tags = { placeId, kind, source },
			{ maxWaitTime = 60 } = app.getSettings(QUEUING)[kind] || {},
			// Calculate endTime based on scheduledAt + maxWaitTime
			// This will remain fixed even if scheduledAt is updated later
			endTime = dayjs(scheduledAt).add(maxWaitTime, 'minutes').toDate(),
			{ id } = await Queue.create({
				kind,
				capacity,
				tier,
				priority,
				scheduledAt,
				endTime,
				status,
				source,
				preferences,
				when: { waited },
				digitalCard: { id: cardId },
				placeId,
				personId,
				membershipId,
			}),
			queueId = String(id)

		appEmit(Event.widget.data.update, {
			kind,
			id: queueId,
			key: QUEUEING,
			cardId,
			status,
			scheduledAt,
			endTime,
			partySize: capacity,
			placeId,
			createdAt: waited,
			modifiedAt: waited
		})
		appMetric(Metric.queuing.total, 1, { tags })

		return { kind, status, queueId, scheduledAt, endTime, partySize: capacity, placeId }
	}

	/**
	 * Process next queue entry sequentially for a place and kind
	 *  - Prevents race condition
	 *  - Suppress errors
	 * @param	{String} placeId
	 * @param	{String} kind
	 * @return	{Promise<Object|undefined>} Assignment result: { status, resources, queueId }
	 */
	Queue.process = async function (placeId, kind) {
		return this.queue(`${Q_QUEUING}:${placeId}:${kind}`, async () => {
			try {
				const res = await Queue.processNext(placeId, kind)
				return res
			}
			catch (error) {
				console.error('[Queue.process]', error)
			}
		})
	}

	/**
	 * Process next eligible queue entry
	 * 	1. Update priorities for all waiting entries within validity period
	 * 	2. Get next entry with updated priorities
	 * 	3. Assign table to next entry
	 * 	4. Notify customer
	 * @param	{String} placeId - place id
	 * @param	{String} kind - kind of resource
	 * @returns {Object|undefined} { status, resources, queueId }
	 */
	Queue.processNext = async function (placeId, kind) {
		const { app } = Queue,
			{ Metric, Event, models } = app,
			{ Resource, Membership } = models,
			{ timeZone } = app.getSettings(LOCALE),
			{ minDuration } = app.getSettings(BOOKING)[kind],
			{ enabled, validity } = app.getSettings(QUEUING)[kind],
			now = dayjs().tz(timeZone),
			expired = now.subtract(validity, 'minutes').toDate(),
			filter = {
				where: {
					kind, placeId,
					status: WAITING,
					createdAt: { gt: expired },
					endTime: { gt: now.toDate() }
				}
			}

		if (!enabled) return

		// Get all waiting entries within the validity period
		const waitingEntries = await Queue.find(filter)
		if (!waitingEntries.length) return

		// Update waiting priorities and sort entries in memory
		const updated = await Queue.updateWaitingPriorities(waitingEntries, now, timeZone)
		waitingEntries.sort((a, b) =>
			b.priority - a.priority || a.createdAt - b.createdAt
		)

		// Cache resource timings once
		const resourceTimings = await Resource.getTimings(placeId, kind)

		// Update scheduled times of waiting entries if priorities changed
		if (updated) {
			await Queue.updateScheduledTimes(kind, waitingEntries, resourceTimings, minDuration)
		}

		const [ nextEntry ] = waitingEntries
		if (!nextEntry) return

		const { capacity, preferences, membershipId } = nextEntry,
			result = Resource.earliestAvailableTime(kind, resourceTimings, capacity, minDuration, preferences),
			{ scheduledAt } = result || {}

		if (!scheduledAt) return

		const assigned = await Queue.tryAssignResources(nextEntry, placeId, kind, scheduledAt, minDuration)

		if (assigned) {
			const { id, when, digitalCard } = nextEntry,
				{ id: cardId } = digitalCard ?? {},
				{ status, resources, createdAt } = assigned,
				queueId = String(id),
				tags = { placeId, kind },
				{ templates = {} } = app.getSettings(QUEUING) || {},
				{ TABLE_ASSIGNED: templateName } = Notify.Templates.Queuing,
				template = {
					name: templates[templateName] || templateName,
					widget: QUEUEING
				},
				personalize = { kind, scheduledAt }

			appMetric(Metric.queuing.total, -1, { tags })
			tags.variance = Date.now() - scheduledAt
			appMetric(Metric.queuing.wait, Date.now() - when.waited, { tags })

			// Immediate processing if assigned
			setTimeout(() => {
				appEmit(Event.widget.data.update, {
					id: queueId,
					key: QUEUEING,
					cardId,
					status,
					waitingSince: when.waited,
					partySize: capacity,
					resources,
					createdAt,
					modifiedAt: now.toDate()
				})

				Queue.process(placeId, kind)

				// Send push notification with fetch
				Membership.notify(String(membershipId), template, personalize, { fetch: true })
					.catch(err => appNotify('[Queue]notify', { err, membershipId, template, personalize }, 'error'))
			}, PROCESS_INTERVAL)
		}
		else {
			// Periodic check if no assignment
			setTimeout(() => Queue.process(placeId, kind), PROCESS_INTERVAL * 5)
			appEmit(Event.queuing.processed, { placeId, kind })
		}

		return assigned
	}

	/**
	 * Determine scheduled time for a new queue entry
	 * @param	{String} placeId - place id
	 * @param	{String} kind - kind of resource
	 * @param	{Number} capacity - number of people (partySize)
	 * @param	{Number} priority - priority, higher is more important
	 * @param	{Object} preferences - Preferences
	 *			{Boolean} allowCombined - Whether to allow combined resources
	 *			{Boolean} adjacentOnly - Whether to require adjacent resources
	 * @return	{Date} scheduledAt
	 */
	Queue.schedule = async function (placeId, kind, capacity, priority, preferences = {}) {
		const { app } = Queue,
			{ Resource } = app.models,
			{ minDuration } = app.getSettings(BOOKING)[kind],
			{ allowCombined } = preferences

		// Get all waiting entries in queue ordered by priority and creation time
		// Exclude entries with passed endTime (considered abandoned)
		const { timeZone } = app.getSettings(LOCALE),
			now = dayjs().tz(timeZone),
			waitingEntries = await Queue.find({
				where: {
					placeId,
					kind,
					status: WAITING,
					endTime: { gt: now.toDate() } // Exclude entries with passed endTime
				},
				order: 'priority DESC, createdAt ASC'
			})

		// Get timing information for all resources
		const resourceTimings = await Resource.getTimings(placeId, kind)
		if (!resourceTimings.length) throw `No '${kind}' available`

		// Filter resources that can accommodate the party size
		const candidate = resourceTimings.filter(resource => allowCombined
			? resource.capacity >= 1
			: resource.capacity >= capacity)

		if (!candidate.length) throw 'No candidate found'

		// Clone resource timings to simulate earlier queue entries
		let simulatedTimings = cloneDeep(candidate)

		// Process earlier queue entries first
		for (const entry of waitingEntries) {
			// Skip if this is a lower priority entry than current request
			if (entry.priority < priority) continue

			// Find earliest available time for this entry
			const result = Resource.earliestAvailableTime(
				kind,
				simulatedTimings,
				entry.capacity,
				minDuration,
				entry.preferences
			)

			if (result) {
				const { scheduledAt, allocated } = result
				Queue.updateSimulatedTimings(simulatedTimings, allocated, scheduledAt, minDuration)
			}
		}

		// Now find time for current request
		const { scheduledAt } = Resource.earliestAvailableTime(kind, simulatedTimings, capacity, minDuration, preferences) ?? {}

		if (!scheduledAt) throw 'No scheduled time found'

		return scheduledAt
	}

	/**
	 * Get next entry from queue
	 *  - Only process entries within validity period
	 * @param	{String} placeId
	 * @param	{String} kind - kind of resource
	 * @param	{String} [tier] - tier of customer
	 * @return	{Object} queue entry
	 */
	Queue.next = async function (placeId, kind, tier) {
		const { app } = Queue,
			{ timeZone } = app.getSettings(LOCALE),
			{ validity } = app.getSettings(QUEUING)[kind],
			now = dayjs().tz(timeZone),
			expired = now.subtract(validity, 'minutes').toDate(),
			filter = {
				where: {
					kind, tier, placeId,
					status: WAITING,
					createdAt: { gt: expired },
					endTime: { gt: now.toDate() } // Exclude entries with passed endTime (considered abandoned)
				},
				order: 'priority DESC, createdAt ASC'
			}

		return Queue.findOne(filter)
	}

	/**
	 * Update priorities for waiting entries
	 * @param {Array} entries - Queue entries to update (mutated)
	 * @param {dayjs} now - Current time
	 * @param {String} timeZone - Timezone for calculations
	 * @return {Promise<Boolean>} - if any updates were made
	 */
	Queue.updateWaitingPriorities = async function(entries, now, timeZone) {
		const updates = []

		for (const entry of entries) {
			if (!entry?.when?.waited) {
				console.warn('Invalid queue entry found:', entry)
				continue
			}

			const { kind, tier, when } = entry
			const waitTime = Math.max(0, now.diff(dayjs(when.waited).tz(timeZone), 'minutes'))
			const priority = Queue.calculatePriority(kind, { tier }, waitTime)

			if (priority !== entry.priority) {
				updates.push(entry.updateAttributes({ priority }))
			}
		}

		await Promise.all(updates)
		return (updates.length > 0)
	}

	/**
	 * Update scheduled times for entries based on new priorities
	 * @param {String} kind - Kind of resource
	 * @param {Array} entries - Queue entries to update
	 * @param {Array} resourceTimings - Available resource timings
	 * @param {Number} minDuration - Minimum duration for booking
	 */
	Queue.updateScheduledTimes = async function(kind, entries, resourceTimings, minDuration) {
		if (!resourceTimings.length) return

		let simulatedTimings = cloneDeep(resourceTimings)
		const { app } = Queue,
			{ Resource } = app.models,
			{ Event } = app

		for (const entry of entries) {
			const { capacity, preferences, id: queueId, digitalCard, status, createdAt } = entry
			const { id: cardId } = digitalCard ?? {}

			// Get both scheduled time and allocated resources
			const { scheduledAt, allocated } = Resource.earliestAvailableTime(kind, simulatedTimings, capacity, minDuration, preferences) ?? {}

			if (!scheduledAt) continue

			await entry.updateAttributes({ scheduledAt })

			// Emit widget.data.update event with updated scheduledAt
			appEmit(Event.widget.data.update, {
				id: queueId,
				key: QUEUING,
				cardId,
				status,
				scheduledAt,
				partySize: capacity,
				createdAt,
				modifiedAt: new Date()
			})

			// Update time of allocated resources
			Queue.updateSimulatedTimings(simulatedTimings, allocated, scheduledAt, minDuration)
		}
	}

	/**
	 * Update simulated resource timings for allocated resources
	 * @param {Array} timings - simulated timings for all resources
	 * @param {Array} allocatedResourceIndices - Indices of allocated resources
	 * @param {Date} scheduledAt - Time when resources are allocated
	 * @param {Number} duration - Duration in minutes
	 */
	Queue.updateSimulatedTimings = function (timings, allocatedResourceIndices, scheduledAt, duration) {
		const endTime = scheduledAt.getTime() + (duration * 60000)

		// Only update the specific resources that were allocated
		for (const index of allocatedResourceIndices) {
			if (index >= 0 && index < timings.length) {
				timings[index].availableAt = new Date(endTime)
			}
		}
	}

	/**
	 * Attempt to assign available resources to queue entry
	 * @param {Queue} entry - Queue entry to process
	 * @param {String} placeId - Place ID
	 * @param {String} kind - Resource kind
	 * @param {Date} from - Start time (timezone-aware)
	 * @param {Number} minDuration - Minimum duration
	 * @return {Promise<Object|undefined>} { status, resources, queueId }
	 */
	Queue.tryAssignResources = async function(entry, placeId, kind, from, minDuration) {
		const { Resource } = Queue.app.models,
			{ id: queueId, capacity, tier, personId, membershipId, preferences } = entry,
			customer = { tier, personId, membershipId },
			to = dayjs(from).add(minDuration, 'minutes').toDate(),
			options = { source: Bookings.Source.QUEUING }

		// Find available tables, include overstaying bookings check for tables
		const { available } = await Resource.available(placeId, kind, capacity, from, minDuration, false, true)
		if (!available.length) return undefined

		const candidates = Resource.candidates(available, capacity, preferences)
		if (!candidates.length) return undefined

		// Attempt resource assignment
		const resources = await Resource.assign(candidates, from, to, capacity, customer, preferences, options)
		if (!resources.length) return undefined

		// Get the reservationId from the first resource
		const [ firstResource ] = resources,
			{ reservationId, createdAt } = firstResource

		// Update queue entry status and store reservationId
		await entry.notify(reservationId)

		return { status: ASSIGNED, resources, queueId, createdAt }
	}

	/**
	 * Calculate priority for a customer based on wait time and reservation history
	 * @param	{String} kind of resource
	 * @param	{Object} customer - { tier, personId, membershipId, hadReservation }
	 * @param	{Number} [waitTime] how long the customer has waited (minutes)
	 * @return	{Number} priority
	 */
	Queue.calculatePriority = function(kind, customer = {}, waitTime = 0) {
		const { app } = Queue,
			{ tier, hadReservation } = customer,
			{ priority = {} } = app.getSettings(QUEUING)[kind],
			{ waitingTime = {}, regular = 0, reservationBonus = 10 } = priority,
			{ factor, maxBonus } = waitingTime,
			basePriority = priority[tier] ?? regular,
			waitingBonus = Math.min(waitTime * factor, maxBonus),
			// Add bonus for customers who had reservations
			reservationPriorityBonus = hadReservation ? reservationBonus : 0

		return basePriority + waitingBonus + reservationPriorityBonus
	}

	/**
	 * Update queue entry status to notified
	 * @param	{String} [reservationId] - Optional reservation ID to store
	 * @return	{Object} updated queue entry
	 */
	Queue.prototype.notify = async function (reservationId) {
		const updates = {
			status: NOTIFIED,
			'when.notified': new Date()
		}

		// Add reservationId if provided
		if (reservationId) {
			updates.reservationId = reservationId
		}

		await this.updateAttributes(updates)
	}

	/**
	 * Process abandoned queue entries
	 * - Marks entries as 'left' if their endTime has passed
	 * @param	{String} placeId - Place ID
	 * @param	{String} kind - Resource kind
	 * @return	{Promise<Number>} Number of entries marked as abandoned
	 */
	Queue.processAbandoned = async function (placeId, kind) {
		const { app } = Queue,
			{ Metric, Event } = app,
			{ timeZone } = app.getSettings(LOCALE),
			now = dayjs().tz(timeZone).toDate(),
			filter = {
				where: {
					kind, placeId,
					status: WAITING,
					endTime: { lt: now }
				}
			}

		// Find all waiting entries with passed endTime
		const abandonedEntries = await Queue.find(filter)
		if (!abandonedEntries.length) return 0

		// Update all abandoned entries to 'left' status
		const updates = abandonedEntries.map(entry => entry.updateAttributes({
			status: LEFT,
			'when.left': now
		}))

		await Promise.all(updates)

		// Update metrics
		const tags = { placeId, kind }

		// Emit widget.data.update event for each abandoned entry
		for (const entry of abandonedEntries) {
			const { id, digitalCard, capacity, createdAt } = entry,
				{ id: cardId } = digitalCard ?? {},
				queueId = String(id)

			appEmit(Event.widget.data.update, {
				id: queueId,
				key: QUEUEING,
				cardId,
				status: LEFT,
				partySize: capacity,
				createdAt,
				modifiedAt: now
			})
		}

		appMetric(Metric.queuing.total, -abandonedEntries.length, { tags })

		return abandonedEntries.length
	}

	// -----  Event handlers  -----

	/**
	 * Handle Booking Status Change - ended & no-show
	 * @param	{Object} booking
	 *			{String} kind
	 *			{String} placeId
	 */
	Queue.handleBookingStatusChanged = async function (booking = {}) {
		const { app } = Queue,
			{ Event } = app,
			{ reservationId, status, kind, placeId, digitalCard, createdAt, modifiedAt = new Date() } = booking,
			{ id: cardId } = digitalCard ?? {}

		if (cardId && reservationId) {
			appEmit(Event.widget.data.update, {
				key: BOOK_TABLE,
				cardId,
				reservations: [ { id: reservationId, status, createdAt, modifiedAt } ],
				createdAt,
				modifiedAt: new Date()
			})
		}

		// Trigger Queue processing
		if ([ ENDED, NOSHOW ].includes(status)) {
			// Process abandoned entries first
			await Queue.processAbandoned(placeId, kind)
			// Then process the queue
			Queue.process(placeId, kind)
			// appEmit(Event.resource.available, resource)	// TODO not required yet, need to retrieve resource
		}
	}

	/**
	 * Handle Booking Deleted event
	 * @param	{Object} booking
	 *			{String} kind
	*			{String} placeId
	*/
	Queue.handleBookingDeleted = async function (booking = {}) {
		const { kind, placeId } = booking

		// Process abandoned entries first
		await Queue.processAbandoned(placeId, kind)
		// Then process the queue
		Queue.process(placeId, kind)
		// appEmit(Event.resource.available, resource)	// TODO not required yet, need to retrieve resource
	}
}
