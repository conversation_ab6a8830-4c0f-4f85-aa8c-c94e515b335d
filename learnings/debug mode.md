# Debugging
- always fix the simplest and foundational issues first
- always try to reduce code, more code than necessary leads to more bugs
- always trace the call chain to find the root cause of issues
- always do debug runs with logging turned on for relevant modules and do tracing of call chain and inspection of data values
- never skip tests when you have difficulty in fixing them
- adding complex code during debugging likely lead to more bugs
- after a few failed attempts to fix, ask yourself: "take a step back, am I missing something simple and obvious??"
- debug methodically for difficult issues:
   1. create a new debug-journal (journal) to track learnings and fails/success
   2. add console.logs (logs) to trace runtime values
   3. add debug statements to the code to trace logic
- when fix attempts result in more failed tests, consider regressing to the previous working state, and log to debug-journal
- when tests fail, explain and confirm with me before modifying test logic 
- when debug run completes, such as passing all tests:
 1. clean up any logs added and review/summarise journal if used

 # Advanced Troubleshooting Techniques for Distributed Systems

## Race Condition Analysis
**Prompt:** "Simulate race conditions explicitly in test environments"
- Create controlled, reproducible race conditions with test hooks
- Design specific timing sequences that mimic production race scenarios
- Verify system state before, during, and after the race condition

## Staged Issue Isolation
**Prompt:** "Solve complex failures by addressing component issues in ascending complexity"
- Identify the simplest failing scenario first
- Fix basic cases before addressing complex interactions
- Apply insights from simple cases to inform complex ones
- Build a progression: single operations → concurrent operations → distributed operations

## Strategic Debug Points
**Prompt:** "Place debug statements at decision boundaries and state transitions"
- Focus on state changes rather than function calls
- Log complete object state before and after transformations
- Instrument decision points where logic branches
- Capture parameters at interface boundaries between components

## Bidirectional Tracing
**Prompt:** "Trace both from cause to effect and from effect to cause simultaneously"
- Forward trace: Follow execution path from initiating action
- Backward trace: Work backward from failure point
- Meet in the middle: Identify where expected and actual paths diverge
- Document the complete causal chain once identified

## State Transition Snapshots
**Prompt:** "Capture complete state snapshots at all asynchronous boundaries"
- Take snapshots before and after async operations
- Compare state differences to identify unexpected changes
- Focus on boundary transitions between components
- Look for state corruption during handoffs

## Contract-Based Testing
**Prompt:** "Use tests to verify interface contracts, not just functionality"
- Define expected behaviors at each interface
- Test contract compliance under various conditions
- Ensure consistent behavior across implementation changes
- Focus on preserving API semantics even when internals change

## Scenario-Based Testing
**Prompt:** "Model tests after real-world usage scenarios rather than functions"
- Design tests around user workflows and interactions
- Include typical error conditions and recovery paths
- Test concurrent access patterns that mirror production usage
- Validate entire scenarios rather than isolated functions

## Incremental Behavior Migration
**Prompt:** "When updating complex systems, reconcile behaviors one aspect at a time"
- Identify specific behavior domains within the system
- Change one behavioral aspect at a time
- Verify each change before moving to the next
- Maintain compatibility with existing components during transition

## Custom Debugging Utilities
**Prompt:** "Build problem-domain-specific debugging tools"
- Create formatters that highlight relevant properties
- Develop custom diffing tools for domain objects
- Implement context-aware loggers for your system
- Build visualization tools for complex state transitions

## Behavior Documentation
**Prompt:** "Document why code behaves as it does, not just what it does"
- Explain rationale behind implementation choices
- Document behavior constraints and requirements
- Note backward compatibility considerations
- Highlight edge cases and their handling

### Tests Failing or Timing Out
**Symptom:** Your tests fail or time out after migrating.
**Cause:** Tests using callbacks aren't properly updated for Promises.
**Solution:** Update your tests to use async/await or Promise chains:

### Mixed Promises and Callbacks in the Same Chain
**Symptom:** Functions returning undefined or incorrect values.
**Cause:** Mixing Promise and callback patterns.
**Solution:** Ensure all functions in your chain use the same pattern. Convert all callback-based functions to return Promises
