# MCP Troubleshooting Guide

This guide provides solutions for common issues encountered when working with the MCP server and clients.

## Connection Issues

### Protocol Version Mismatch

**Symptom**: Client fails to connect with error "Server's protocol version is not supported: 2025-03-26"

**Cause**: The client is using an older transport (SSEClientTransport) that doesn't support the newer protocol version (2025-03-26) that the server is using.

**Solution**:
1. Update the client to use StreamableHTTPClientTransport instead of SSEClientTransport
2. Specify supported protocol versions in the client configuration:

```javascript
// Create a client with explicit protocol version support
const client = new Client({
  name: "my-client",
  version: "1.0.0",
  supportedProtocolVersions: ['2025-03-26', '2024-11-05'] // Support both protocol versions
});
```

### Backward Compatibility with SSE Clients

The MCP server supports backward compatibility with older clients that use SSE transport with protocol version 2024-11-05, even when the server is configured to use Streamable HTTP with protocol version 2025-03-26.

**How it works**:
1. The server sets up endpoints for both transport types during initialization
2. The server detects SSE clients based on the Accept header (text/event-stream)
3. When a client connects, it sends its supported protocol versions
4. The server negotiates the protocol version based on the client's capabilities
5. For SSE clients, the server automatically uses protocol version 2024-11-05
6. The server sends a session/new notification to help clients identify their session ID

**Important Notes for SSE Clients**:
- SSE clients must use the `/mcp` endpoint (not `/mcp/sse` or other variations)
- Authentication for SSE clients can be provided via:
  - Authorization header in the initial SSE connection (may not work with all server implementations)
  - Query parameter in the URL (`?auth=your-jwt-token`) (recommended for SSE transport)
- The server automatically detects the transport type based on the Accept header
- For message endpoints, authentication must be included in each request
- The SSE transport requires authentication for both the initial connection and subsequent messages
- The server sends a session/new notification with the session ID after connection
- Clients should listen for this notification to get their session ID

**Example SSE client configuration**:
```javascript
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';

// Method 1: Authentication via headers
const transport1 = new SSEClientTransport(
  new URL("http://your-server/mcp"),
  {
    headers: {
      'Authorization': `Bearer ${jwtToken}`
    }
  }
);

// Method 2: Authentication via URL query parameter (more reliable for SSE)
const url = new URL("http://your-server/mcp");
url.searchParams.append('auth', jwtToken);
const transport2 = new SSEClientTransport(url);

// Method 3: Custom message endpoint with authentication
const url = new URL("http://your-server/mcp");
url.searchParams.append('auth', jwtToken);
const transport3 = new SSEClientTransport(url, {
  // Override the message endpoint to include the auth token in each message
  messageEndpoint: (sessionId) => {
    const messageUrl = new URL(`http://your-server/mcp/messages`);
    messageUrl.searchParams.append('sessionId', sessionId);
    messageUrl.searchParams.append('auth', jwtToken);
    return messageUrl.toString();
  }
});

// Create client with 2024-11-05 protocol version
const client = new Client({
  name: 'legacy-client',
  version: '1.0.0',
  supportedProtocolVersions: ['2024-11-05'] // Only support older version
});

// Connect to the server - will work even with a server configured for 2025-03-26
await client.connect(transport2);
```

**Verified Compatibility**:
We have verified through testing that SSE clients with protocol version 2024-11-05 can successfully connect to a server configured for Streamable HTTP with protocol version 2025-03-26. The server correctly negotiates the protocol version and handles the different transport mechanisms transparently.

**Common SSE Authentication Issues**:

If you encounter authentication issues with SSE transport, try the following:

1. **Use URL query parameters for authentication**:
   ```javascript
   const url = new URL("http://your-server/mcp");
   url.searchParams.append('auth', jwtToken);
   const transport = new SSEClientTransport(url);
   ```

2. **Include authentication in message endpoints**:
   Some server implementations require authentication for both the initial connection and subsequent messages. Use a custom message endpoint function to include the auth token in each message:
   ```javascript
   const transport = new SSEClientTransport(url, {
     messageEndpoint: (sessionId) => {
       const messageUrl = new URL(`http://your-server/mcp/messages`);
       messageUrl.searchParams.append('sessionId', sessionId);
       messageUrl.searchParams.append('auth', jwtToken);
       return messageUrl.toString();
     }
   });
   ```

3. **Verify the token is valid**:
   Make sure the JWT token is valid and has the correct payload structure:
   ```javascript
   const payload = { tenant: { code: 'your-tenant-code' } };
   const jwtToken = jwt.sign(payload, secretKey, { expiresIn: '1h' });
   ```

4. **Listen for session/new notifications**:
   The server sends a session/new notification with the session ID after connection. Make sure your client is listening for this notification:
   ```javascript
   // Set up event listener for SSE messages
   eventSource.onmessage = (event) => {
     try {
       const data = JSON.parse(event.data);
       if (data.method === 'session/new') {
         console.log('Received session ID:', data.params.sessionId);
         // Store the session ID for future use
         sessionId = data.params.sessionId;
       }
     } catch (error) {
       console.error('Error parsing message:', error);
     }
   };
   ```

### Missing Session ID

**Symptom**: Server logs show "Invalid or missing session ID: undefined undefined"

**Cause**: The client is not properly maintaining session state or the session has expired.

**Solution**:
1. Ensure the client is using the correct transport for the protocol version
2. For Streamable HTTP transport, the session ID is managed automatically
3. Check server session timeout settings (default is 30 minutes)

## Initialization Request Issues

**Symptom**: Client receives "Bad Request: No valid session ID provided and not an initialization request" error when trying to initialize a connection.

**Cause**: The server is not correctly identifying the initialization request or there's an issue with the request format.

**Solution**:
1. Ensure the client is sending a proper initialization request with the correct format:

```javascript
// Correct initialization request format
const initRequest = {
  jsonrpc: "2.0",
  id: "init-1",
  method: "initialize",
  params: {
    clientInfo: {
      name: "test-client",
      version: "1.0.0"
    },
    supportedProtocolVersions: ["2025-03-26"]
  }
};
```

2. Check the server logs for detailed error information. The server should log the request details to help diagnose the issue.

3. Verify that the client is using the correct transport for the protocol version:
   - For protocol version 2025-03-26, use StreamableHTTPClientTransport
   - For protocol version 2024-11-05, use SSEClientTransport

4. Test with a simple curl command to isolate client-specific issues:

```bash
TOKEN=$(node --require dotenv/config -e "console.log(require('jsonwebtoken').sign({ tenant: { code: 'TEST' } }, process.env.PERKD_SECRET_KEY || 'test-secret-key'))")

curl -i -X POST http://localhost:8081/mcp \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"jsonrpc":"2.0","id":"init-1","method":"initialize","params":{"clientInfo":{"name":"test-client","version":"1.0.0"},"supportedProtocolVersions":["2025-03-26"]}}'
```

5. If using the SDK client, enable debug logging to see the exact request being sent:

```javascript
// Enable debug logging
process.env.DEBUG = 'mcp:*';
```

## Identity Endpoint Issues

**Symptom**: Client receives "Cannot GET /.identity" error

**Cause**: The /.identity endpoint is not properly implemented or accessible.

**Solution**:
1. Ensure the server has the /.identity endpoint registered before other endpoints:

```typescript
// Register OAuth and identity endpoints first to ensure they're accessible
app.get('/.identity', (req, res) => identityHandler(req, res));
```

2. Verify authentication is working correctly
3. Test the endpoint directly:

```javascript
const response = await fetch("http://localhost:8082/.identity", {
  headers: {
    'Authorization': `Bearer ${jwtToken}`
  }
});
```

## Authentication Issues

**Symptom**: Client receives 401 Unauthorized errors with "invalid signature"

**Cause**: JWT token is signed with a different secret key than what the server is using.

**Solution**:
1. Ensure the PERKD_SECRET_KEY environment variable is set to the same value on both client and server:

```bash
# Set the environment variable before running the server or client
export PERKD_SECRET_KEY="your-secret-key"
```

2. Generate a valid JWT token with the correct payload structure:

```javascript
const jwtToken = jwt.sign(
  { tenant: { code: 'your-tenant-code' } },
  process.env.PERKD_SECRET_KEY || 'default-secret-key',
  { expiresIn: '1h' }
);
```

3. Include the token in all requests with the correct options structure:

```javascript
const transport = new StreamableHTTPClientTransport(
  new URL("http://your-server/mcp"),
  {
    requestInit: {
      headers: {
        'Authorization': `Bearer ${jwtToken}`
      }
    }
  }
);
```

4. Verify the token is valid by testing it:

```javascript
try {
  const decoded = jwt.verify(jwtToken, secretKey);
  console.log("Token verification successful. Decoded payload:", decoded);
} catch (verifyError) {
  console.error("Token verification failed:", verifyError);
}
```

## Timeout Issues

### 504 Gateway Timeout Errors

**Symptom**: Client receives 504 Gateway Timeout errors when making requests that take longer than 60 seconds to complete.

**Cause**: The default Node.js HTTP server timeout settings are too short for long-running operations, or there's a proxy/load balancer with restrictive timeout settings.

**Solution**:
1. Configure HTTP server timeout settings in your MCP configuration:

```javascript
// In your MCP configuration
{
  "http": {
    "keepAliveTimeout": 120000,  // 2 minutes
    "headersTimeout": 125000     // 2 minutes 5 seconds
  }
}
```

2. For applications with longer-running operations, use more generous settings:

```javascript
// For applications with long-running operations
{
  "http": {
    "keepAliveTimeout": 300000,  // 5 minutes
    "headersTimeout": 305000     // 5 minutes 5 seconds
  }
}
```

3. If behind a load balancer or proxy, ensure its timeout settings are also adjusted:
   - AWS ELB/ALB: Increase idle timeout (default is 60 seconds)
   - Nginx: Increase proxy_read_timeout and proxy_send_timeout
   - Apache: Increase ProxyTimeout directive

4. Verify the timeout settings are applied by checking the server logs:
```
[mcp-sdk] HTTP server timeouts configured: keepAliveTimeout=120000ms, headersTimeout=125000ms
```

### Client-Side Timeout Issues

**Symptom**: Client disconnects with timeout errors even though the server is still processing the request.

**Cause**: Client-side timeout settings are too restrictive.

**Solution**:
1. Increase client-side timeout settings to match or exceed server-side settings:

```javascript
// For StreamableHTTPClientTransport
const transport = new StreamableHTTPClientTransport(
  new URL("http://your-server/mcp"),
  {
    requestInit: {
      headers: {
        'Authorization': `Bearer ${jwtToken}`
      },
      // Set timeout to slightly longer than server timeout
      timeout: 130000  // 2 minutes 10 seconds
    }
  }
);

// For fetch API
const controller = new AbortController();
const timeoutId = setTimeout(() => controller.abort(), 130000);
const response = await fetch(url, {
  signal: controller.signal,
  // other options
});
clearTimeout(timeoutId);
```

2. Implement retry logic for operations that might exceed timeout limits:

```javascript
async function callWithRetry(fn, maxRetries = 3) {
  let lastError;
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      console.warn(`Attempt ${attempt} failed:`, error);
      lastError = error;
      if (error.name === 'AbortError' || error.message.includes('timeout')) {
        // Wait before retrying (with exponential backoff)
        await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, attempt - 1)));
        continue;
      }
      throw error; // Re-throw if it's not a timeout error
    }
  }
  throw lastError;
}
```

### Session Timeout Issues

**Symptom**: Client receives "Session not found" or "Invalid session ID" errors after periods of inactivity.

**Cause**: The session has expired due to inactivity exceeding the configured timeout.

**Solution**:
1. Increase the session timeout in your MCP configuration:

```javascript
{
  "session": {
    "enabled": true,
    "timeout": 1800000,       // 30 minutes (default)
    "cleanupInterval": 300000 // 5 minutes (default)
  }
}
```

2. Implement periodic ping/pong messages to keep the session alive:

```javascript
// Send a ping every 5 minutes to keep the session alive
setInterval(async () => {
  try {
    await client.ping();
    console.log("Ping successful, session kept alive");
  } catch (error) {
    console.error("Ping failed:", error);
    // Implement reconnection logic if needed
  }
}, 5 * 60 * 1000);
```

3. Implement automatic reconnection logic in your client:

```javascript
function setupReconnection(client, transport, options) {
  client.onDisconnect = async (reason) => {
    console.warn(`Disconnected: ${reason}`);

    // Wait a moment before reconnecting
    await new Promise(resolve => setTimeout(resolve, 1000));

    try {
      console.log("Attempting to reconnect...");
      await client.connect(transport);
      console.log("Reconnection successful");
    } catch (error) {
      console.error("Reconnection failed:", error);
      // Try again with exponential backoff
      setTimeout(() => setupReconnection(client, transport, options),
        options.reconnectDelay || 5000);
    }
  };
}
```

## Testing Tools

The package includes several test scripts to help diagnose issues:

- `yarn test:server` - Start a test MCP server on port 8082
- `yarn test:streamable-client` - Test connection using StreamableHTTPClientTransport
- `yarn test:identity` - Test the /.identity endpoint
- `yarn test:session` - Test session management
- `yarn test:sse-compatibility` - Test SSE client connecting to Streamable HTTP server
- `yarn test:sse-client` - Simple test for SSE client with protocol version 2024-11-05
- `yarn test:dual-transport` - Test both SSE and Streamable HTTP clients
- `yarn run-sse-server` - Start a dedicated SSE test server on port 8083
- `yarn test-sse-server` - Test connecting to the SSE test server
- `yarn verify-secret-key` - Verify the secret key in your .env file
- `yarn debug-auth` - Debug authentication issues
- `yarn check-server-key` - Check which secret key the server is using
- `yarn generate-token` - Generate a valid JWT token for testing

Run these tests to verify different aspects of the MCP server functionality.

## Running Tests with Environment Variables

To ensure consistent authentication, use the provided script to set the environment variable:

```bash
# Run tests with the PERKD_SECRET_KEY environment variable set
./scripts/test-with-env.sh test:identity
./scripts/test-with-env.sh test:streamable-client
./scripts/test-with-env.sh test:session
```

Or set the environment variable manually:

```bash
export PERKD_SECRET_KEY="test-secret-key"
yarn test:identity
```
