# Product Service

A comprehensive microservice for managing products, bookings, and fulfillment within the CRM ecosystem. Handles physical and digital products, table reservations, and integrates with multiple external providers.

## Table of Contents

1. [Overview](#overview)
2. [System Architecture](#system-architecture)
3. [Core Features](#core-features)
4. [Digital Fulfillment](#digital-fulfillment)
5. [Resources](#resources)
6. [Queuing for Resources](#queuing-for-resources)
7. [Service Integration](#service-integration)
8. [Data Models](#data-models)
9. [API Endpoints](#api-endpoints)
10. [Events](#events)


## Overview
The Product service manages all product-related operations with comprehensive functionality for:

- **Product Management**
  - Physical and digital product lifecycle
  - Multi-variant and bundle support
  - Multi-currency pricing system
  - Real-time inventory tracking

- **Digital Products**
  - Stored value and gift cards
  - Digital vouchers and tickets
  - Membership products
  - Digital widgets and NFTs

- **Resource Management**
  - Flexible resource configuration and types
  - Operating hours and capacity management
  - Real-time availability tracking
  - Location-based resource allocation
  - Integration with external providers

- **Table Booking**
  - Intelligent table allocation with weighted scoring
  - Combined table optimization with adjacency rules
  - Smart Release and Reassign for reservations
  - Table Relocation for moving customers between tables
  - Deposit handling for reservations
  - Real-time availability tracking
  - Resource capacity management

- **External Integration**
  - Integrating with external providers like Shopify and food delivery platforms:
    - E-commerce platforms (Shopify)
    - Food delivery services (Grab, UberEats)
  - Marketplace synchronization
  - Real-time inventory updates

- **Fulfillment**
  - Digital fulfillment for stored value, memberships, and vouchers
  - Table reservation management
  - Store pickup coordination
  - Multi-channel fulfillment

### System Architecture
```mermaid
flowchart TB
    %% External Interfaces
    subgraph EP[External Providers]
        %% E-commerce
        subgraph EC[E-commerce]
            SP[Shopify]
            SIS[Inventory Sync]
            SCS[Catalog Sync]
            SP <--> SIS
            SP <--> SCS
        end

        %% Food Delivery Providers
        subgraph MP[Food Delivery]
            GF[GrabFood]
            GM[GrabMart]
            UE[UberEats]
            FP[FoodPanda]
        end
    end

    %% CRM Services
    subgraph ES[CRM Services]
        MS1[Membership Service]
        SS[Sales Service]
        PS1[Payment Service]
    end

    %% Product Service
    subgraph PS[Product Service]
        %% API Layer
        subgraph API[API Layer]
            PA[Product API]
            BA[Bag API]
            WA[Widget APIs]
        end

        %% Core Mixins
        subgraph CM[Core Mixins]
            FD[Fulfill Digital]
            OM[Order Management]
            BM[Booking Management]
            FM[Find Operations]
        end

        %% Models
        subgraph MM[Model Layer]
            PM[Product Model]
            VM[Variant Model]
            RM[Resource Model]
            PRM[Price Model]
        end

        %% Internal connections
        PA --> FM
        BA --> OM
        WA --> BM

        FM --> PM
        FM --> VM
        OM --> VM
        BM --> RM
        FD --> VM
    end

    %% Infrastructure
    subgraph IN[Infrastructure]
        EB[Event Bus]
        subgraph ET[Event Types]
            PT[Payment Events]
            OE[Order Events]
            IE[Inventory Events]
            PE[Product Events]
        end

        PT --> EB
        OE --> EB
        IE --> EB
        PE --> EB
    end

    %% External connections
    EP --> API
    API --> EP
    CM --> EB
    EB --> CM
    ES --> EB
    EB --> ES
```

### Core Features

#### Product Management
- **CRUD Operations**
  - Product and variant creation/updates
  - Digital and physical product handling
  - Multi-currency price management
  - Inventory and availability controls
  - SKU and barcode (GTIN) management

#### Digital Product Features
- Digital fulfillment processing
- Supports multiple fulfillment types:
  - Digital Vouchers
  - Digital Tickets
  - Digital Stored Value
  - Digital Widgets

#### Staff Operations
- Product availability management
- Variant status controls
- Inventory level adjustments
- Menu refresh capabilities
- Store-specific product controls

#### Promotional Features
- Product promotion microsites
- Add-to-bag functionality
- Promotional page deployment
- Applink management
- Campaign tracking

#### External Provider Integration
- **Shopify Integration**
  - Bi-directional product/variant sync
  - Inventory synchronization
  - Price point management
  - Order status tracking

- **Food Delivery Integration**
  - GrabFood menu management
  - GrabMart catalog handling
  - UberEats menu synchronization
  - Real-time inventory updates
  - Order webhook processing


## Digital Fulfillment

- Digital Tickets/Vouchers
  - **Code Generation and Management**
    - Automated code generation with uniqueness validation
    - Support for single and multi-use codes
    - Batch code generation capabilities
    - Code status tracking (RESERVED → ISSUED → REDEEMED)

  - **Validation Controls**
    - Time-based validity periods
    - Usage limit enforcement
    - Membership tier restrictions
    - Location-specific redemption rules

  - **Fulfillment Process**
    - Synchronous fulfillment processing
    - Atomic transaction handling
    - Rollback support on failures
    - Event-driven status updates

  - **Booking Integration**
    - Intelligent table allocation system:
      - Single and combined table support (configurable)
      - Capacity-based matching with weighted scoring
      - Adjacent table optimization
      - Smart Release and Reassign for reservations
    - Table Relocation system:
      - Two-phase process (Intent and Commit)
      - Supports both booking-enabled and booking-disabled flows
      - Maintains order history during table transitions
      - Notifies kitchen about relocated orders
      - Atomic operations for data consistency
    - Party size validation (configurable)
    - Maximum combined capacity (configurable)
    - Deposit handling for reservations
    - Real-time availability tracking
    - Timezone-aware resource management
    - For detailed documentation, see:
      - [Table Booking Documentation](docs/table-booking.md)
      - [Table Relocation Documentation](docs/table-relocation.md)

  - **Event Notifications**
    - Fulfillment status updates
    - Expiration reminders
    - Usage tracking notifications
    - Redemption confirmations

- Stored Value Products
  - Fixed denomination support
  - Variable amount support with min/max limits
  - Automatic balance calculation with exchange rates
  - Preload and top-up configurations

## Resources

The service provides comprehensive resource management capabilities for various business needs:

### Key Features
- **Resource Types**
  - Physical locations (stores, pickup points)
  - Tables and seating arrangements
  - Service stations and counters
  - Vending machines and kiosks
  - Staff and service providers

- **Operating Hours**
  - Flexible scheduling configuration
  - Time zone support
  - Special holiday hours
  - Break time management

- **Capacity Management**
  - Dynamic capacity tracking
  - Multi-dimensional constraints
  - Overbooking controls
  - Waitlist functionality

- **Location Management**
  - Geo-location support
  - Service area definition
  - Multi-location coordination
  - Provider location integration

For detailed documentation on resource management, see [Resources Documentation](docs/resources.md)


## Queuing for Resources

The service provides a queuing system for managing and scheduling resource bookings within the application. It supports features such as:
- Joining the queue through an API call.
- Automated processing of queue entries based on wait time and priority.
- Dynamic scheduling based on resource availability and simulated timings.
- Assignment of resources upon successful scheduling.

For more details, please refer to the comprehensive documentation in [resource-queuing.md](./docs/resource-queuing.md).



## Service Integration
This service integrates with multiple microservices through REST APIs and event-driven communication:

```mermaid
graph TB
    %% Core Product Service
    subgraph PS[Product Service]
        direction TB

        subgraph API[API Layer]
            PA[Product API]
            BA[Bag API]
            WA[Widget API]
            OA[Order API]
            FA[Fulfillment API]
        end

        subgraph Core[Core Services]
            OM[Order Management]
            PM[Product Management]
            BM[Booking Management]
            FM[Fulfillment Manager]
            DM[Digital Manager]
        end

        subgraph Models[Data Models]
            PModel[Product Model]
            VModel[Variant Model]
            RModel[Resource Model]
            BModel[Booking Model]
        end
    end

    %% Event Bus Integration
    subgraph EB[Event Bus]
        direction LR
        PE[Product Events]
        OE[Order Events]
        BE[Booking Events]
        IE[Inventory Events]
        FE[Fulfillment Events]
    end

    %% Internal Services
    subgraph IS[Internal Services]
        direction TB
        MS[Membership Service]
        SS[Sales Service]
        PS1[Payment Service]
        NS[Notification Service]
    end

    %% External Providers
    subgraph EP[External Providers]
        direction TB

        subgraph EC[E-commerce]
            SP[Shopify]
            subgraph SP_INT[Shopify Integration]
                SI[Inventory Sync]
                SC[Catalog Sync]
            end
        end

        subgraph FD[Food Delivery]
            GF[GrabFood]
            GM[GrabMart]
            UE[UberEats]
            FP[FoodPanda]
        end

        subgraph DL[Delivery]
            LL[Lalamove]
            GE[Grab Express]
        end
    end

    %% Connections
    API --> Core
    Core --> Models

    %% Event Bus Connections
    Core --> EB
    EB --> Core
    EB --> IS
    IS --> EB

    %% External Provider Connections
    SP_INT --> SP
    SP --> SP_INT
    Core --> FD
    FD --> Core
    Core --> DL

    %% Service Connections
    MS --> API
    SS --> API
    PS1 --> API
    API --> NS

    %% Event Types
    Core --> PE
    Core --> OE
    Core --> BE
    Core --> IE
    Core --> FE

    classDef service fill:#f9f,stroke:#333,stroke-width:2px
    classDef provider fill:#bbf,stroke:#333,stroke-width:2px
    classDef events fill:#bfb,stroke:#333,stroke-width:2px
    class MS,SS,PS1,NS service
    class SP,GF,GM,UE,FP,LL,GE provider
    class PE,OE,BE,IE,FE events
```

### Membership Service
- Handles membership-based product access and validation
- Integrates through remote connector at port 3103
- Key interactions:
  - Membership tier validation for product access
  - Membership upgrade/downgrade product eligibility
  - Digital fulfillment for membership-restricted products

### Sales Service
- Manages order processing and fulfillment workflows
- Key integrations:
  - Order validation and processing
  - Fulfillment status tracking
  - Payment processing coordination
  - Digital and physical product delivery

### External Provider Integration

#### Shopify Integration
- Bi-directional sync with configurable update intervals
- Automatic inventory synchronization with location support
- Support for multiple Shopify shops per tenant
- Deletion tracking and cleanup of removed products

#### Food Delivery Integration
- GrabFood & GrabMart:
  - Automatic menu refresh based on store operating hours
  - Support for multiple store locations
  - Real-time inventory sync
  - Configurable selling times
  - Currency and pricing localization

- UberEats:
  - Menu synchronization with store-specific configurations
  - Order transformation and webhook processing
  - Support for menu type configurations (delivery/pickup)


## Data Models

The service uses a rich data model structure with the following key entities:

```mermaid
erDiagram
    Product ||--o{ Variant : has
    Product ||--o{ Bundle : contains
    Product ||--o{ ProductImage : has
    Product ||--o{ Variation : defines
    Product ||--o{ Resource : uses
    Product ||--o{ Category : belongs_to

    Variant ||--o{ Price : has
    Variant ||--o{ Option : offers
    Variant ||--o{ ProductImage : has
    Variant }|--|| InventoryPolicy : follows
    Variant ||--o{ FulfillmentPolicy : has

    Variation ||--o{ Option : contains
    Option }|--|| Variant : belongs_to

    Product {
        string id
        string title
        string description
        string brand
        string availability
        date availabilityDate
        object priceRange
        boolean isLowQuantity
        boolean isSoldOut
        boolean isBackOrder
        object attributes
        object external
        array tags
        string status "active|archived|draft"
    }

    Variant {
        string id
        string kind "product|membership|giftcard|storedvalue|booking|ticket|voucher|widget|nft"
        string title
        string sku
        object gtin
        string mpn
        object cost
        object weight
        object inventory
        array channels
        boolean taxable
        string taxCode
        object attributes
        number position "The order of the product variant (1 is first position)"
        object unitCostMeasure "Unit cost measurement details"
        object unitPriceMeasure "Unit price measurement details"
    }

    Variation {
        string id
        string name "e.g. Size, Color, Style"
        string label
        string type "select|radio|checkbox|text"
        boolean required
        object validation
        array allowedValues
        object attributes
    }

    Option {
        string id
        string value
        string label
        number position
        object metadata
        boolean isDefault
        boolean isDisabled
        object pricing "price adjustments"
        object inventory "option-specific inventory"
    }

    Resource {
        string id
        string type
        object operating_hours
        object capacity
        object location
        object fulfillment
    }

    Price {
        number value
        string currency
        object conditions
        array paymentMethods
    }

    InventoryPolicy {
        string management "shopify|perkd|resource"
        string policy "deny|continue"
        number quantity
        number lowQuantityWarningThreshold
    }

    Bundle {
        string id
        string title
        object value
        array items
    }
```

### Variant Types System

```mermaid
graph TB
    subgraph Variant Types
        direction TB
        V[Variant Base]

        subgraph Physical Products
            P[Product]
        end

        subgraph Digital Products
            direction TB
            D[Digital]

            subgraph Value Based
                SV[Stored Value]
                GC[Gift Card]
            end

            subgraph Time Based
                M[Membership]
                T[Ticket]
                W[Widget]
            end

            subgraph Redemption Based
                VO[Voucher]
                NFT[NFT Token]
            end
        end

        V --> P
        V --> D
        D --> SV
        D --> GC
        D --> M
        D --> T
        D --> W
        D --> VO
        D --> NFT
    end

    subgraph Type Specific Features
        direction LR
        VB[Value Based] --> |Features| VBF[Balance Tracking<br/>Exchange Rates<br/>Top-up Support]
        TB[Time Based] --> |Features| TBF[Validity Period<br/>Access Control<br/>Renewal Rules]
        RB[Redemption Based] --> |Features| RBF[Usage Limits<br/>Validation Rules<br/>Expiry Control]
    end
```

Each variant type has specific behaviors and fulfillment processes:

1. **Physical Products**
   - Regular products with inventory tracking
   - Widget products with assembly requirements

2. **Value Based Digital Products**
   - Balance management system
   - Multi-currency support
   - Top-up and reload capabilities
   - Exchange rate handling

3. **Time Based Digital Products**
   - Validity period management
   - Access control integration
   - Automatic renewal processing
   - Tier-based privileges

4. **Redemption Based Digital Products**
   - Usage tracking and limitations
   - Validation rule enforcement
   - Expiration management
   - Proof of ownership

### Digital Fulfillment System

```mermaid
flowchart TB
    subgraph Digital Fulfillment Flow
        direction TB
        O[Order Created] --> P[Order Preparation]
        P --> V[Validation]
        V --> Q[Qualification]
        Q --> A[Apply Qualified]
        A --> C[Create Order]
        C --> F[Fulfillment]

        subgraph Preparation
            P --> IV[Inventory Validation]
            P --> PV[Price Validation]
            P --> MV[Membership Validation]
        end

        subgraph Qualification
            Q --> QM[Membership Check]
            Q --> QB[Booking Check]
            Q --> QI[Inventory Check]
        end

        subgraph Fulfillment
            F --> FD[Digital]
            F --> FP[Physical]
            F --> FB[Booking]
        end
    end
```

The Digital Fulfillment System handles various types of digital products:

1. **Value Fulfillment Process**
   - Balance initialization
   - Currency conversion
   - Transaction recording
   - Balance verification

2. **Time Fulfillment Process**
   - Period calculation
   - Access grant processing
   - Membership activation
   - Renewal scheduling

3. **Redemption Fulfillment Process**
   - Code generation
   - Validation setup
   - Usage tracking
   - Expiration management

4. **Common Features**
   - Atomic transaction handling
   - Rollback mechanisms
   - Event notifications
   - Status tracking
   - Error handling

### Product Model
- Core product information and configuration
- Properties:
  - Basic details (name, description, status)
  - Visibility controls
  - Digital fulfillment settings
  - Provider mappings (Shopify, Grab, UberEats)
  - Price range configuration
  - Category and tag management

### Variant Model
- Product variations with extensive configuration:
  - Core Properties:
    - Multiple types: product, membership, giftcard, storedvalue, booking, ticket, voucher, widget, nft
    - Title and position management
    - Product identifiers:
      - SKU (Stock Keeping Unit)
      - GTIN (Global Trade Item Number)
      - MPN (Manufacturer Part Number)

  - Inventory Management:
    - Multiple tracking modes:
      - Shopify integration
      - Native tracking
      - Resource-based tracking
    - Policy controls for backorder and low quantity
    - Quantity thresholds and warnings

  - Fulfillment Configuration:
    - Service types:
      - None (self-managed)
      - Store pickup
      - Digital delivery
      - Shopify fulfillment
      - Kitchen preparation
      - Vending machine
    - Preparation time tracking
    - Location-based fulfillment

  - Price Management:
    - Multi-currency support
    - Cost tracking with currency
    - Weight-based pricing support
    - Payment method restrictions
    - Variable pricing with increment control

  - Reservation system:
    - Hold duration
    - Release triggers
    - Conflict resolution

  - Cache configuration:
    - TTL settings
    - Invalidation rules
    - Cache keys structure

  - External Integrations:
    - Shopify mappings:
      - Variant ID tracking
      - Inventory item mapping
      - Location-specific inventory
    - Food delivery service mappings
    - Marketplace synchronization

  - Real-time availability checks
  - Cross-channel inventory sync

### Resource Model
- Manages bookable resources and availability:
  - Resource Configuration:
    - Multiple resource types:
      - Store pickup locations
      - Convenience store networks
      - Vending machines
    - Operating hours management
    - Country-specific configurations

  - Booking Management:
    - Flexible time slot configuration
    - Lead time requirements
    - Minimum scheduling intervals
    - Capacity management per slot

  - Fulfillment Controls:
    - Pre-scheduled pickup support
    - Real-time availability tracking
    - Location-based allocation
    - Service type restrictions

  - Integration Features:
    - Third-party pickup location support
    - External service synchronization
    - Automated status updates
    - Resource allocation optimization

  - Capacity Management:
    - Dynamic capacity adjustment
    - Overbooking configuration
    - Waitlist management
    - Resource pooling

### Provider Models
- External service integrations with provider-specific implementations:

#### Shopify Provider
- Product Management:
  - Bi-directional product/variant synchronization
  - Automatic inventory level tracking
  - Multi-shop support within tenants
  - Deletion and cleanup handling
- Event Processing:
  - Product creation/update/deletion events
  - Inventory connection status changes
  - Order fulfillment status updates

#### Grab Provider
- Food Service Integration:
  - Menu management with time-based updates
  - Automatic refresh based on operating hours
  - Currency and pricing localization
  - Real-time inventory synchronization
- Mart Service Features:
  - Catalog management with categories
  - Product availability controls
  - Store-specific menu configurations
  - Automated menu refresh scheduling

#### UberEats Provider
- Menu Integration:
  - Store-specific menu configurations
  - Product category management
  - Pricing and availability sync
  - Menu type support (delivery/pickup)
- Order Processing:
  - Order transformation to internal format
  - Status webhook handling
  - Real-time updates processing

## API Endpoints
For detailed API documentation, see [API.md](docs/API.md).

### Product APIs

#### Core Product Operations
- `GET /Products/findByIds` - Find products by ID list

#### Product Provisioning
- `POST /Products/provision/storedvalue` - Create Product & Variants for Stored Value

#### Digital Fulfillment Operations
- `POST /Products/fulfillments/digital` - Process digital product fulfillment
- `POST /Products/fulfillments/digital/cancel` - Cancel digital fulfillment


### Variant APIs

#### Digital Fulfillment
- `POST /Variants/fulfillments/digital` - Digital product fulfillment
- `POST /Variants/fulfillments/digital/cancel` - Cancel digital fulfillment

#### Order Management
- `POST /Variants/order` - Create order without payment
- `POST /Variants/order/pay` - Create order with payment
- `POST /Variants/order/commit` - Commit pending payment for order
- `POST /Variants/order/items/complete` - Complete order items with full details

#### Fulfillment Management
- `POST /Variants/fulfillments/status` - Check fulfillment status
- `POST /Variants/fulfillments/request` - Send fulfillment request to provider
- `POST /Variants/fulfillments/delivered` - Mark fulfillment as delivered
- `POST /Variants/fulfillments/cancel` - Cancel fulfillment of items

#### Table Queuing Widget APIs
- `POST /Products/app/queuing/tables` - Queue for table for member
- `POST /Products/app/queuing/checkin` - Check-in for tables with Smart Release and Reassign

#### Table Booking Widget APIs
- `POST /Products/app/booking/tables/availability` - Check table availability
- `POST /Products/app/booking/tables` - Book tables for member

#### Table Booking Website APIs
- `GET /Products/web/booking/tables/availability` - Get comprehensive table availability matrix
- `POST /Products/web/booking/tables` - Book tables for non-member

#### External Provider Integration
- `POST /Variants/order/external/transform` - Transform external order to internal format

### Shop Widget APIs
- `GET /Variants/app/products` - Get variants for custom storefronts

### Staff Widget APIs
- `POST /Products/staff/products/:variantId/availability` - Set product availability
- `POST /Products/staff/variants/:id/status` - Update variant status
- `POST /Products/staff/menu/refresh` - Request provider menu sync

#### Table Relocation APIs
- `POST /Products/staff/tables/relocate/intent` - Express intent to relocate table(s)
- `POST /Products/staff/tables/relocate/commit` - Execute table relocation operation

### AppLink Management
- `POST /Products/applink/addtobag` - Create product promotion page
- `POST /Products/applink/addtobag/:micrositeId/deploy` - Deploy promotion page
- `POST /Products/applink/addtobag/:micrositeId/undeploy` - Remove promotion page
- `DELETE /Products/applink/addtobag/:micrositeId` - Delete promotion page
- `POST /Products/applink/refresh` - Refresh product applinks


### Provider Integration APIs

#### Shopify Integration
- `POST /shopify/sync` - Sync products and variants with Shopify

#### Grab Integration
- `GET /grab/food/merchant/menu` - Get GrabFood menu for outlet
- `GET /grab/mart/merchant/menu` - Get GrabMart menu for outlet
- `POST /grab/refresh/menu` - Refresh Grab menu

#### UberEats Integration
- `POST /ubereats/sync/menu` - Push menu to UberEats
- `POST /ubereats/app/order` - Transform UberEats order to app format


## Events

```mermaid
flowchart LR
    %% Event Categories
    subgraph Internal Events
        subgraph Order Events
            PT[Payment Transaction]
            BS[Booking Status]
            DF[Digital Fulfillment]
        end

        subgraph Menu Events
            MC[Menu Created]
            MU[Menu Updated]
            MD[Menu Deleted]
            MR[Menu Refresh]
        end
    end

    subgraph Product Events
        PC[Product Created]
        PU[Product Updated]
        PD[Product Deleted]
        IN[Inventory Updated]
    end

    subgraph External Events
        subgraph Shopify
            SP[Product Events]
            SI[Inventory Events]
        end

        subgraph Food Delivery
            GF[GrabFood Events]
            GM[GrabMart Events]
            UE[UberEats Events]
        end
    end

    %% Event Bus & Service
    EB[Event Bus]
    PS[Product Service]

    %% Flow Connections
    PT --> EB
    BS --> EB
    DF --> EB
    MC --> EB
    MU --> EB
    MD --> EB
    MR --> EB

    PC --> EB
    PU --> EB
    PD --> EB
    IN --> EB

    SP --> EB
    SI --> EB
    GF --> EB
    GM --> EB
    UE --> EB

    EB --> PS

    %% Bidirectional flows
    PS <--> SP
    PS <--> SI
    PS <--> GF
    PS <--> GM
    PS <--> UE
```

### Event Flow Details

1. **Internal Events**
   - Order Events: Payment, booking, and fulfillment status updates
   - Product Events: Lifecycle and inventory changes
   - Menu Events: Menu/catalog updates and refresh requests

2. **External Provider Integration**
   - Shopify: Product and inventory synchronization
   - Food Delivery:
     - GrabFood/GrabMart: Menu and availability updates
     - UberEats: Menu and order processing


### Published Events
Events emitted by this service for other services to consume.

#### Product Events
- `product.product.created` - New product creation
- `product.product.updated` - Product update
- `product.product.deleted` - Product deletion
- `product.inventory.updated` - Inventory level change

#### Variant Events
- `product.variant.created` - New variant creation
- `product.variant.updated` - Variant update
- `product.variant.deleted` - Variant deletion

#### Machine Events
- `product.variant.created.machine` - New machine variant creation
- `product.variant.updated.machine` - Machine variant update
- `product.variant.deleted.machine` - Machine variant deletion
- `product.inventory.updated.machine` - Product inventory level change for machine

#### Menu Events
- `product.variant.created.menu` - Menu variant creation
- `product.variant.updated.menu` - Menu variant update
- `product.variant.deleted.menu` - Menu variant deletion

#### Resource Events
- `product.resource.available` - Resource became available

#### Booking Events (on behalf of Sales Service)
- `sales.booking.confirmed.reservation` - All bookings confirmed for reservation
- `sales.booking.cancelled.reservation` - All bookings cancelled for reservation


### Subscribed Events
Events consumed by this service from other services.

#### Payment Events
- `payment.transaction.paid` - Payment successful
- `payment.transaction.authorized` - Payment authorized
- `payment.transaction.chargeable` - Payment chargeable
- `payment.transaction.cancelled` - Payment cancelled
- `payment.transaction.failed` - Payment failed

#### Booking Events
- `sales.booking.confirmed.reservation` - All bookings confirmed for reservation (local event)
- `sales.booking.cancelled.reservation` - All bookings cancelled for reservation (local event)
- `sales.booking.ended` - Booking ended
- `sales.booking.deleted` - Booking deleted

#### Provider Events
- `ubereats.product.refresh` - UberEats product refresh request
- `shopify.product.created` - Shopify product created
- `shopify.product.updated` - Shopify product updated
- `shopify.product.deleted` - Shopify product deleted
- `shopify.inventory.connected` - Shopify inventory connected
- `shopify.inventory.disconnected` - Shopify inventory disconnected

### Event Structure
```json
{
  "id": "unique_event_id",
  "name": "event.name",
  "domain": "service_domain",
  "actor": "entity_type",
  "action": "operation",
  "data": {
    // Event specific payload
  },
  "tenantCode": "tenant_identifier",
  "timezone": "Asia/Singapore",
  "timestamp": "iso-date"
}
```
