# Queuing Hours Validation

## Overview

The table queuing system now includes time-based validation to prevent customers from joining queues during inappropriate hours. This addresses a critical gap where customers could previously join table queues at any time, even when restaurants were closed.

## Implementation

### Validation Logic

The `requestTableQueue` function now validates the current request time against configured hours before allowing customers to join the queue:

1. **Primary Check**: If `place.queuing.hours` is configured, validate against these hours
2. **Fallback Check**: If queuing hours are not defined, validate against `place.openingHours`
3. **Error Handling**: Throw `OUTSIDE_QUEUING_HOURS` error (HTTP 400) when requests are made outside allowed hours

### Configuration Options

#### Option 1: Use Opening Hours (Default Behavior)
```json
{
  "openingHours": {
    "periods": [
      {
        "open": { "day": 1, "time": "1130" },
        "close": { "day": 1, "time": "1430" }
      },
      {
        "open": { "day": 1, "time": "1730" },
        "close": { "day": 1, "time": "2130" }
      }
    ]
  }
}
```

In this case, queue requests are only accepted during restaurant operating hours (11:30-14:30 and 17:30-21:30).

#### Option 2: Extended Queuing Hours
```json
{
  "openingHours": {
    "periods": [
      {
        "open": { "day": 1, "time": "1130" },
        "close": { "day": 1, "time": "2130" }
      }
    ]
  },
  "queuing": {
    "hours": {
      "periods": [
        {
          "open": { "day": 1, "time": "1100" },
          "close": { "day": 1, "time": "2200" }
        }
      ]
    }
  }
}
```

In this case, customers can join the queue from 11:00-22:00, even though the restaurant only operates 11:30-21:30. This allows:
- Early queue joining (30 minutes before opening)
- Late queue joining (30 minutes after closing)

## Benefits

### Business Value
- **Prevents inappropriate queue requests** during closed hours
- **Flexible configuration** allows restaurants to extend queue acceptance beyond operating hours
- **Better customer experience** with clear time-based boundaries
- **Operational efficiency** by controlling when queue management is active

### Technical Benefits
- **Consistent validation pattern** using existing `isOpen` utility function
- **Proper timezone handling** with Asia/Taipei support
- **Graceful error handling** with appropriate HTTP status codes
- **Backward compatibility** - falls back to opening hours if queuing hours not configured

## Error Responses

### OUTSIDE_QUEUING_HOURS
- **HTTP Status**: 400 Bad Request
- **When**: Request made outside configured queuing/opening hours
- **Message**: "OUTSIDE_QUEUING_HOURS"
- **Enhanced Details**: Includes next available queuing time for better user experience

**Response Structure:**
```json
{
  "error": {
    "statusCode": 400,
    "message": "OUTSIDE_QUEUING_HOURS",
    "details": {
      "currentTime": "2025-08-01T14:26:05.928Z",
      "nextQueueingAt": "2025-08-02T03:00:00.000Z",
      "timeZone": "Asia/Taipei"
    }
  }
}
```

**Fields:**
- `currentTime`: The timestamp when the request was made
- `nextQueueingAt`: Next time when queuing will be available (null if no future periods)
- `timeZone`: The timezone used for calculations

### PLACE_NOT_FOUND
- **HTTP Status**: 404 Not Found
- **When**: Invalid placeId provided
- **Message**: "PLACE_NOT_FOUND"

## Usage Examples

### Scenario 1: Restaurant with Standard Hours
- **Opening Hours**: 11:30-14:30, 17:30-21:30
- **Queuing Hours**: Not configured (uses opening hours)
- **Result**: Queue requests only accepted during operating hours

**Example Request at 22:26 (outside hours):**
```json
{
  "error": {
    "statusCode": 400,
    "message": "OUTSIDE_QUEUING_HOURS",
    "details": {
      "currentTime": "2025-08-01T14:26:05.928Z",
      "nextQueueingAt": "2025-08-02T03:30:00.000Z",
      "timeZone": "Asia/Taipei"
    }
  }
}
```
*Customer informed they can try again tomorrow at 11:30 AM*

### Scenario 2: Restaurant with Extended Queue Hours
- **Opening Hours**: 11:30-21:30
- **Queuing Hours**: 11:00-22:00
- **Result**: Queue requests accepted 30 minutes before opening and 30 minutes after closing

**Example Request at 11:15 (during extended queue hours):**
- ✅ **Success**: Queue request accepted even though restaurant not yet open
- Customer can join queue 15 minutes before restaurant opens

**Example Request at 22:30 (outside extended hours):**
```json
{
  "error": {
    "statusCode": 400,
    "message": "OUTSIDE_QUEUING_HOURS",
    "details": {
      "currentTime": "2025-08-01T14:30:00.000Z",
      "nextQueueingAt": "2025-08-02T03:00:00.000Z",
      "timeZone": "Asia/Taipei"
    }
  }
}
```
*Customer informed they can try again tomorrow at 11:00 AM*

### Scenario 3: 24/7 Queue Acceptance
- **Opening Hours**: 11:30-21:30
- **Queuing Hours**: 24/7 periods configured
- **Result**: Queue requests always accepted (original behavior)
- **nextQueueingAt**: Always null since queuing never closes

## Implementation Details

### Code Location
- **File**: `server/mixins/TableQueuing.js`
- **Function**: `Product.requestTableQueue`
- **Validation**: Added after existing party size validation, before resource availability check

### Dependencies
- **@perkd/utils**: `isOpen` function for hours validation
- **Place Model**: Access to `openingHours` and `queuing.hours` configuration
- **dayjs**: Timezone-aware date handling

### Timezone Handling
The validation properly handles timezone conversion using the configured `LOCALE.timeZone` setting (typically `Asia/Taipei`), ensuring accurate time comparisons regardless of server timezone.

## Testing

A comprehensive test suite is provided in `test/queuing-hours-validation.test.js` covering:
- Default behavior using opening hours
- Extended queuing hours configuration
- Error handling for invalid places
- Proper error responses with status codes
- Timezone-aware validation

## Migration Notes

This is a **backward-compatible** change:
- Existing places without `queuing.hours` will use `openingHours` (current behavior)
- No database migrations required
- No configuration changes required for existing deployments
- Restaurants can optionally configure `queuing.hours` for extended queue acceptance

## Related Issues

This implementation addresses the validation gap identified in the queue scheduling system where:
1. Customers could join queues at any time (now fixed)
2. Queue scheduling ignores business hours (separate issue, still needs fixing)
3. No separation between service hours and queue acceptance hours (now available)
