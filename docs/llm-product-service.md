# Product Service MCP Guide for AI Assistants

## Table of Contents

- [1. Introduction](#1-introduction)
  - [1.1 Purpose](#11-purpose)
  - [1.2 Product Service Overview](#12-product-service-overview)
  - [1.3 MCP for Product Service](#13-mcp-for-product-service)
  - [1.4 Key Concepts](#14-key-concepts)
- [2. LLM Interaction Model](#2-llm-interaction-model)
  - [2.1 Tool Invocation](#21-tool-invocation)
  - [2.2 Resource Access](#22-resource-access)
  - [2.3 Response Handling](#23-response-handling)
- [3. Available MCP Tools](#3-available-mcp-tools)
  - [3.1 Tool: `product_create`](#31-tool-product_create)
  - [3.2 Tool: `product_get`](#32-tool-product_get)
  - [3.3 Tool: `product_list`](#33-tool-product_list)
  - [3.4 Tool: `product_update`](#34-tool-product_update)
  - [3.5 Tool: `product_delete`](#35-tool-product_delete)
  - [3.6 Tool: `product_findByCategory`](#36-tool-product_findbycategory)
  - [3.7 Tool: `product_search`](#37-tool-product_search)
- [4. Available MCP Resources](#4-available-mcp-resources)
  - [4.1 Resource: `product://schema`](#41-resource-productschema)
  - [4.2 Resource: `product://examples`](#42-resource-productexamples)
- [5. Best Practices for LLMs](#5-best-practices-for-llms)
  - [5.1 General](#51-general)
  - [5.2 When Creating Products](#52-when-creating-products)
  - [5.3 When Retrieving Products](#53-when-retrieving-products)
  - [5.4 When Updating Products](#54-when-updating-products)
- [6. Common Patterns and Examples](#6-common-patterns-and-examples)
  - [6.1 Creating a Product](#61-creating-a-product)
  - [6.2 Retrieving and Updating a Product](#62-retrieving-and-updating-a-product)
- [7. Troubleshooting](#7-troubleshooting)
  - [7.1 Common Errors](#71-common-errors)
- [8. MCP Prompts](#8-mcp-prompts)
- [9. Conclusion](#9-conclusion)


## 1. Introduction

### 1.1 Purpose
This document provides comprehensive guidelines for AI assistants (LLMs) to effectively interact with the Product Service within the CRM Platform using the Model Context Protocol (MCP).

### 1.2 Product Service Overview
The Product Service is a core component of the CRM Platform responsible for managing all product-related data, including product creation, retrieval, updates, deletion, and querying. It provides a centralized way to handle product information.

### 1.3 MCP for Product Service
AI assistants can leverage MCP to programmatically access and manipulate product data. This allows for seamless integration of product management capabilities into various automated workflows and AI-driven applications. The MCP server name for the Product Service is `crm-product-mcp`.

### 1.4 Key Concepts
- **Product Model**: Represents a single product with attributes like name, description, price, SKU, categories, etc.
- **Standard CRUD Operations**: The service supports Create, Read, Update, Delete operations on products.
- **Querying**: Products can be listed with filters, searched by keywords, and found by category.
- **Tenant Isolation**: All operations are tenant-specific and isolated.

## 2. LLM Interaction Model

This section outlines how LLMs should interact with the Product Service MCP tools and resources.

### 2.1 Tool Invocation
LLMs should use their native MCP tool invocation mechanism, typically the `use_mcp_tool`.

**Example `use_mcp_tool` structure:**
```xml
<use_mcp_tool>
  <server_name>crm-product-mcp</server_name>
  <tool_name>product_create</tool_name>
  <arguments>
  {
    "name": "Premium Coffee",
    "description": "High-quality coffee beans",
    "price": 12.99,
    "sku": "COFFEE-001",
    "categories": ["beverages", "coffee"],
    "isActive": true
  }
  </arguments>
</use_mcp_tool>
```
The `server_name` will always be `crm-product-mcp` for the Product Service. The `tool_name` corresponds to one of the available tools listed in this guide, and `arguments` should be a JSON object matching the tool's input schema.

### 2.2 Resource Access
To access MCP resources like schemas or examples, LLMs should use the `access_mcp_resource` tool.

**Example `access_mcp_resource` structure:**
```xml
<access_mcp_resource>
  <server_name>crm-product-mcp</server_name>
  <uri>product://schema</uri>
</access_mcp_resource>
```
The `uri` will be one of the resource URIs like `product://schema` or `product://examples`.

### 2.3 Response Handling
MCP tools and resources typically return JSON responses. The LLM is responsible for parsing this JSON to extract necessary information. Key data points often include:
- `product.id`: The unique identifier for a product, usually returned by `product_create` or present in products returned by `product_get` or `product_list`.
- `products`: An array of product objects, returned by `product_list`, `product_findByCategory`, and `product_search`.
- `success`: A boolean flag indicating the outcome of an operation (though not explicitly part of all Product service tool responses, it's a general MCP concept).
- `message`: A confirmation message, often returned by `product_delete`.
- `error`: An error message or object if an operation fails.

The LLM should extract relevant fields from the response to proceed with its tasks or to inform the user. For example, after creating a product, extract `response.product.id`.

## 3. Available MCP Tools

The Product Service exposes several MCP tools. These tools are the primary way AI assistants will interact with product data. Each tool's `tool_name` should be used in the `<tool_name>` tag of the `use_mcp_tool` structure.

### 3.1 Tool: `product_create`
*   **Description:** Creates a new product in the system.
*   **Input Parameters (for `arguments` object):**
    *   `name` (string, required): The name of the product.
    *   `description` (string, optional): A detailed description of the product.
    *   `price` (number, required): The price of the product.
    *   `sku` (string, optional): Stock Keeping Unit (SKU) for the product.
    *   `categories` (array of strings, optional): Categories the product belongs to.
    *   `isActive` (boolean, optional, default: `true`): Whether the product is active.
    *   `metadata` (object, optional): Additional metadata for the product.
*   **Output:** An object containing the newly created `product` details, including its `id`.
*   **Example:**
    ```json
    {
      "tool_name": "product_create",
      "arguments": {
        "name": "Premium Coffee",
        "description": "High-quality coffee beans",
        "price": 12.99,
        "sku": "COFFEE-001",
        "categories": ["beverages", "coffee"],
        "isActive": true
      }
    }
    ```
    *(Refer to `product://examples` for more detailed client-side code examples)*

### 3.2 Tool: `product_get`
*   **Description:** Retrieves a specific product by its unique ID.
*   **Input Parameters (for `arguments` object):**
    *   `productId` (string, required): The ID of the product to retrieve.
*   **Output:** An object containing the `product` details.
*   **Example:**
    ```json
    {
      "tool_name": "product_get",
      "arguments": {
        "productId": "some-product-id"
      }
    }
    ```

### 3.3 Tool: `product_list`
*   **Description:** Lists products, with options for filtering, pagination, and sorting.
*   **Input Parameters (for `arguments` object):**
    *   `filter` (object, optional): An object defining filter conditions.
        *   `where` (object, optional): Conditions for filtering (e.g., `{ "isActive": true, "price": { "gt": 10 } }`).
        *   `limit` (number, optional, default: 20, max: 100): Maximum number of products to return.
        *   `skip` (number, optional, default: 0): Number of products to skip (for pagination).
        *   `order` (string or array of strings, optional): Sort order (e.g., `"price DESC"`, `["category ASC", "name ASC"]`).
*   **Output:** An object containing:
    *   `total` (number): Total number of products matching the filter.
    *   `skip` (number): The number of products skipped.
    *   `limit` (number): The limit used for the query.
    *   `products` (array): A list of product objects.
*   **Example:**
    ```json
    {
      "tool_name": "product_list",
      "arguments": {
        "filter": {
          "where": {
            "isActive": true,
            "price": { "gt": 10 }
          },
          "limit": 10,
          "skip": 0,
          "order": "price DESC"
        }
      }
    }
    ```

### 3.4 Tool: `product_update`
*   **Description:** Updates an existing product's information.
*   **Input Parameters (for `arguments` object):**
    *   `productId` (string, required): The ID of the product to update.
    *   Other parameters are optional and similar to `product_create` (e.g., `name`, `description`, `price`, `sku`, `categories`, `isActive`, `metadata`). Only provided fields will be updated.
*   **Output:** An object containing the updated `product` details.
*   **Example:**
    ```json
    {
      "tool_name": "product_update",
      "arguments": {
        "productId": "some-product-id",
        "price": 14.99,
        "description": "Updated description for the premium coffee"
      }
    }
    ```

### 3.5 Tool: `product_delete`
*   **Description:** Deletes a product from the system by its ID.
*   **Input Parameters (for `arguments` object):**
    *   `productId` (string, required): The ID of the product to delete.
*   **Output:** An object with a confirmation `message`.
*   **Example:**
    ```json
    {
      "tool_name": "product_delete",
      "arguments": {
        "productId": "some-product-id"
      }
    }
    ```

### 3.6 Tool: `product_findByCategory`
*   **Description:** Finds products belonging to a specific category.
*   **Input Parameters (for `arguments` object):**
    *   `category` (string, required): The category to filter products by.
    *   `limit` (number, optional, default: 20): Maximum number of products to return.
    *   `skip` (number, optional, default: 0): Number of products to skip (for pagination).
*   **Output:** An object containing:
    *   `total` (number): Total number of products in that category.
    *   `skip` (number): The number of products skipped.
    *   `limit` (number): The limit used for the query.
    *   `products` (array): A list of product objects.
*   **Example:**
    ```json
    {
      "tool_name": "product_findByCategory",
      "arguments": {
        "category": "beverages",
        "limit": 10
      }
    }
    ```

### 3.7 Tool: `product_search`
*   **Description:** Searches for products by name or description.
*   **Input Parameters (for `arguments` object):**
    *   `query` (string, required): The search term.
    *   `limit` (number, optional, default: 20): Maximum number of products to return.
    *   `skip` (number, optional, default: 0): Number of products to skip (for pagination).
*   **Output:** An object containing:
    *   `total` (number): Total number of products matching the search query.
    *   `skip` (number): The number of products skipped.
    *   `limit` (number): The limit used for the query.
    *   `products` (array): A list of product objects.
*   **Example:**
    ```json
    {
      "tool_name": "product_search",
      "arguments": {
        "query": "coffee",
        "limit": 5
      }
    }
    ```

## 4. Available MCP Resources

MCP resources provide supplementary information that can aid in using the tools effectively. Each resource URI should be used in the `<uri>` tag of the `access_mcp_resource` structure.

### 4.1 Resource: `product://schema`
*   **Description:** Provides the JSON schema definition for the Product model. This schema details all available fields, their types, validations, and relationships.
*   **How to Access:** Use the `access_mcp_resource` tool with the URI `product://schema`.
    ```json
    {
      "tool_name": "access_mcp_resource",
      "arguments": {
        "server_name": "crm-product-mcp",
        "uri": "product://schema"
      }
    }
    ```
*   **Content:** The resource returns a JSON object representing the Product model's schema. This is invaluable for understanding data structures and constructing valid tool arguments.

### 4.2 Resource: `product://examples`
*   **Description:** Provides usage examples for the Product Service MCP tools, including client-side code snippets.
*   **How to Access:** Use the `access_mcp_resource` tool with the URI `product://examples`.
    ```json
    {
      "tool_name": "access_mcp_resource",
      "arguments": {
        "server_name": "crm-product-mcp",
        "uri": "product://examples"
      }
    }
    ```
*   **Content:** The resource returns a JSON object containing a list of examples, each demonstrating how to use a specific tool with sample arguments and expected outcomes.

## 5. Best Practices for LLMs

### 5.1 General
*   **Server Name:** Always use `crm-product-mcp` as the `server_name` when calling Product Service tools or accessing its resources via MCP.
*   **Error Handling:** Be prepared to handle potential errors from tool calls. MCP responses will typically include error details if an operation fails. Parse responses carefully.
*   **Schema Adherence:** Always refer to `product://schema` to understand the data types, required fields, and constraints for product attributes. This ensures that the arguments provided to tools like `product_create` and `product_update` are valid.
*   **Idempotency:** Be aware of the idempotency hints provided in tool annotations (e.g., `product_delete` is idempotent, `product_create` is not). This is crucial for designing resilient interactions.
*   **Read-Only vs. Destructive Operations:** Pay attention to `readOnlyHint` and `destructiveHint` in tool annotations to understand the impact of a tool call. For example, `product_get` is read-only, while `product_delete` is destructive.

### 5.2 When Creating Products
*   **Provide Meaningful Data**: Use descriptive names for products. Ensure SKUs are unique if used.
*   **Use `isActive` Deliberately**: Set `isActive: false` for products that should not be immediately available or visible.
*   **Leverage Categories**: Assign products to relevant categories to improve organization and discoverability.
*   **Check Response**: After creation, extract the `product.id` from the response as it's needed for future updates or specific retrievals.

### 5.3 When Retrieving Products
*   **Use Pagination**: When listing products (`product_list`, `product_findByCategory`, `product_search`), always use `limit` and `skip` parameters for pagination, especially if the dataset might be large. Default `limit` is 20, max is 100.
*   **Use Specific Finders**: Utilize `product_findByCategory` or `product_search` when the criteria are known, as they can be more efficient than a generic `product_list` with complex filters.
*   **Be Specific with Filters**: When using `product_list` with the `filter` parameter, construct precise filter criteria (e.g., based on `price`, `isActive`, `categories`) to narrow down results. Refer to `product://schema` for filterable fields and operators.
*   **Handle Empty Results**: Be prepared for queries that return no products and inform the user appropriately.

### 5.4 When Updating Products
*   **Fetch Before Update (Optional but Recommended)**: If the LLM doesn't have the current state of the product, it's good practice to first use `product_get` to retrieve the latest version before attempting an update. This helps avoid unintended overwrites.
*   **Send Only Changed Fields**: The `product_update` tool typically updates only the fields provided in the `arguments`. Send only the attributes that need to change to avoid accidentally modifying other fields.
*   **Verify `productId`**: Ensure the `productId` used for the update is correct and corresponds to the product intended for modification.

## 6. Common Patterns and Examples

This section provides more detailed examples of common workflows.

### 6.1 Creating a Product

This example shows how to create a new product and extract its ID from the response.

**LLM Action:**
```xml
<use_mcp_tool>
  <server_name>crm-product-mcp</server_name>
  <tool_name>product_create</tool_name>
  <arguments>
  {
    "name": "Artisan Bread",
    "description": "Freshly baked whole wheat bread.",
    "price": 5.99,
    "sku": "BREAD-WW-001",
    "categories": ["bakery", "fresh food"],
    "isActive": true,
    "metadata": {
      "ingredients": ["whole wheat flour", "water", "yeast", "salt"],
      "allergen_info": "Contains gluten"
    }
  }
  </arguments>
</use_mcp_tool>
```

**Expected Response (Simplified):**
```json
{
  "product": {
    "id": "prod_123abc789xyz",
    "name": "Artisan Bread",
    "description": "Freshly baked whole wheat bread.",
    "price": 5.99,
    "sku": "BREAD-WW-001",
    "categories": ["bakery", "fresh food"],
    "isActive": true,
    "metadata": {
      "ingredients": ["whole wheat flour", "water", "yeast", "salt"],
      "allergen_info": "Contains gluten"
    }
    // ... other fields
  }
}
```
The LLM should then parse this response to get `product.id` (e.g., `"prod_123abc789xyz"`) for any subsequent operations on this product.

### 6.2 Retrieving and Updating a Product

This example demonstrates fetching a product, modifying some of its data, and then updating it.

**Step 1: Retrieve the Product**

**LLM Action:**
```xml
<use_mcp_tool>
  <server_name>crm-product-mcp</server_name>
  <tool_name>product_get</tool_name>
  <arguments>
  {
    "productId": "prod_123abc789xyz"
  }
  </arguments>
</use_mcp_tool>
```

**Expected Response (Simplified):**
The LLM receives the current product details. Let's assume the price is `5.99`.

**Step 2: Update the Product's Price**

The LLM decides to update the price to `6.49`.

**LLM Action:**
```xml
<use_mcp_tool>
  <server_name>crm-product-mcp</server_name>
  <tool_name>product_update</tool_name>
  <arguments>
  {
    "productId": "prod_123abc789xyz",
    "price": 6.49,
    "description": "Freshly baked whole wheat bread (organic)"
  }
  </arguments>
</use_mcp_tool>
```

**Expected Response (Simplified):**
```json
{
  "product": {
    "id": "prod_123abc789xyz",
    "name": "Artisan Bread",
    "description": "Freshly baked whole wheat bread (organic)",
    "price": 6.49,
    // ... other fields remain, potentially updated timestamps
  }
}
```
The LLM should confirm the update based on the response.

## 7. Troubleshooting

If issues arise when interacting with the Product Service MCP, consider the following:

### 7.1 Common Errors

| Error Message (or similar) | Possible Cause | Suggested Solution |
|----------------------------|----------------|--------------------|
| "Product with id `[ID]` not found" | The provided `productId` is incorrect, or the product has been deleted. | Verify the `productId`. Ensure the product exists and has not been recently deleted. |
| "Validation failed" / "Invalid input" (often with details about specific fields) | The data provided in the `arguments` for a tool (e.g., `product_create`, `product_update`) does not conform to the expected schema (e.g., wrong data type, missing required field). | Carefully review the input parameters against the tool's requirements and the `product://schema` resource. Correct data types and ensure all required fields are present. |
| "Unauthorized" / "Access Denied" | The request lacks proper authentication or authorization credentials, or the credentials do not grant permission for the requested operation. | This typically needs to be resolved at the MCP client or infrastructure level. Ensure the LLM's environment is correctly configured for authenticated MCP calls. |
| Timeout Error | The service took too long to respond. This could be due to network issues or high server load. | Retry the request after a short delay. If the problem persists, it might indicate a broader issue with the service or network. |
| Filter error / Query error | The `filter` object in `product_list` or the `query` in `product_search` is malformed or uses unsupported operators/fields. | Consult `product://schema` for filterable fields and supported query syntax. Simplify the filter/query to isolate the issue. |

## 8. MCP Prompts

Unlike some other services that might offer pre-defined MCP Prompts for guided interactions, the Product Service currently does not expose specific MCP Prompts. LLMs should rely on direct tool invocation as described in this guide for all interactions with the Product Service.

## 9. Conclusion

This guide provides AI assistants with the necessary information to effectively use the Product Service via MCP. By understanding the available tools, resources, and adhering to the guidelines and best practices outlined, AI assistants can seamlessly integrate product management functionalities into their workflows. For the most up-to-date schema and examples, always refer to the `product://schema` and `product://examples` resources respectively.
