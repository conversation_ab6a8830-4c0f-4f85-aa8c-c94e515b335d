# Table Relocation Implementation Guide

This document provides implementation details and code samples for the Table Relocation feature described in the [Table Relocation](./table-relocation.md) documentation.

## Table of Contents
1. [Implementation Overview](#implementation-overview)
2. [API Implementation](#api-implementation)
   - [Intent Endpoint](#intent-endpoint)
   - [Commit Endpoint](#commit-endpoint)
3. [Implementation Phases](#implementation-phases)

## Implementation Overview

The Table Relocation feature is implemented in the Product Service with integration points to the Sales Service for order relocation. The implementation follows a phased approach with core functionality prioritized in earlier phases.

## API Implementation

### Intent Endpoint

The `staffRelocateTableIntent` method in `StaffWidgetApi.js` handles the information gathering phase:

```javascript
/**
 * Express intent to relocate table(s) and retrieve available destination tables
 * @param {Object} sourceTable - Source table information
 *        {String} resourceId - ID of the source table
 * @param {Boolean} [checkBookingFeature=true] - Whether to check if Booking feature is enabled
 * @returns {Object} Source tables and available destination tables, with reservation details if booking is enabled
 */
Product.staffRelocateTableIntent = async function(sourceTable, checkBookingFeature = true) {
  const { app } = Product;
  const { Resource, Booking, Order, Setting } = app.models;
  const { resourceId } = sourceTable || {};

  // Validate source table
  if (!resourceId) {
    throw {
      statusCode: 400,
      code: 'INVALID_SOURCE_TABLE',
      message: 'Source table ID is required'
    };
  }

  // Find the source table resource
  const sourceTableResource = await Resource.findById(resourceId);
  if (!sourceTableResource) {
    throw {
      statusCode: 404,
      code: 'SOURCE_TABLE_NOT_FOUND',
      message: 'Source table not found'
    };
  }

  // Check if Booking feature is enabled
  const bookingEnabled = await Setting.findOne({
    where: { key: 'enableBooking', value: 'true' }
  });

  // Handle based on booking feature status
  if (bookingEnabled && checkBookingFeature) {
    // ===== BOOKING ENABLED FLOW =====

    // Find active booking for this table
    const booking = await Booking.findOne({
      where: {
        resourceId,
        status: { inq: ['SUCCESS', 'OPEN'] }
      },
      order: 'createdAt DESC'
    });

    if (!booking) {
      throw {
        statusCode: 404,
        code: 'NO_ACTIVE_BOOKING',
        message: 'No active booking found for this table'
      };
    }

    const { reservationId, partySize, startTime, endTime, note, deposit } = booking;

    // Find all tables under the same reservation
    const bookings = await Booking.find({
      where: {
        reservationId,
        status: { inq: ['SUCCESS', 'OPEN'] }
      }
    });

    // Get all source table IDs
    const sourceTableIds = bookings.map(b => b.resourceId);

    // Find all source table resources
    const sourceTableResources = await Resource.find({
      where: {
        id: { inq: sourceTableIds }
      }
    });

    // Find all orders associated with these tables
    const fulfillments = await Order.app.models.Fulfillment.find({
      where: {
        status: 'OPEN',
        'destination.position.key': 'table',
        'destination.position.value': { inq: sourceTableIds }
      }
    });

    // Group fulfillments by table
    const fulfillmentsByTable = {};
    for (const fulfillment of fulfillments) {
      const { destination = {} } = fulfillment;
      const { position = [] } = destination;
      const tablePosition = position.find(p => p.key === 'table');
      if (!tablePosition) continue;

      const tableId = tablePosition.value;
      fulfillmentsByTable[tableId] = fulfillmentsByTable[tableId] || [];
      fulfillmentsByTable[tableId].push(fulfillment);
    }

    // Prepare source tables with order information
    const sourceTables = sourceTableResources.map(table => {
      const tableFulfillments = fulfillmentsByTable[table.id] || [];
      const orderIds = new Set();

      for (const fulfillment of tableFulfillments) {
        orderIds.add(String(fulfillment.orderId));
      }

      return {
        resourceId: table.id,
        name: table.name,
        position: table.position,
        capacity: table.capacity,
        orders: orderIds.size,
        hasActiveOrders: orderIds.size > 0
      };
    });

    // Find available tables that can accommodate the party
    const now = new Date();
    const duration = Math.ceil((endTime - now) / (60 * 1000)); // remaining minutes

    // Find available tables
    const availableTables = await Resource.available(
      booking.placeId,
      'TABLE',
      partySize,
      now,
      duration,
      true // allow combined tables
    );

    // Filter out tables that are already part of this reservation
    const filteredAvailableTables = availableTables.filter(
      table => !sourceTableIds.includes(table.id)
    );

    // Get candidates with suitability scores
    const candidates = Resource.candidates(
      filteredAvailableTables,
      partySize,
      { allowCombined: true, adjacentOnly: true },
      true // include scores
    );

    // Prepare available tables with additional information
    const availableTablesWithInfo = candidates.map(table => {
      // Find adjacent tables
      const adjacentTables = table.adjacent || [];

      return {
        resourceId: table.id,
        name: table.name,
        position: table.position,
        capacity: table.capacity,
        suitabilityScore: table.score || 0,
        adjacentTables
      };
    });

    // Generate suggested combinations
    const suggestedCombinations = [];

    // Single table that can accommodate the whole party
    const singleTableOptions = availableTablesWithInfo
      .filter(table => table.capacity >= partySize)
      .map(table => ({
        tables: [table.resourceId],
        totalCapacity: table.capacity,
        suitabilityScore: table.suitabilityScore
      }))
      .sort((a, b) => b.suitabilityScore - a.suitabilityScore)
      .slice(0, 3); // Top 3 single table options

    suggestedCombinations.push(...singleTableOptions);

    // If we need to combine tables, add those suggestions too
    if (partySize > 4) {
      // Logic for table combinations would go here
      // This is a simplified version
      // In a real implementation, we would use more sophisticated algorithms
      // to find optimal table combinations
    }

    return {
      bookingEnabled: true,
      reservation: {
        id: reservationId,
        partySize,
        startTime,
        endTime,
        note,
        deposit
      },
      sourceTables,
      availableTables: availableTablesWithInfo,
      suggestedCombinations
    };
  } else {
    // ===== BOOKING DISABLED FLOW =====

    // When booking is disabled, we only have the single source table
    const sourceTableIds = [resourceId];

    // Find all orders associated with this table
    const fulfillments = await Order.app.models.Fulfillment.find({
      where: {
        status: 'OPEN',
        'destination.position.key': 'table',
        'destination.position.value': resourceId
      }
    });

    // Check if there are any active orders for this table
    if (fulfillments.length === 0) {
      throw {
        statusCode: 400,
        code: 'NO_ACTIVE_ORDERS',
        message: 'No active orders found for this table'
      };
    }

    // Prepare source table with order information
    const orderIds = new Set();
    for (const fulfillment of fulfillments) {
      orderIds.add(String(fulfillment.orderId));
    }

    const sourceTables = [{
      resourceId: sourceTableResource.id,
      name: sourceTableResource.name,
      position: sourceTableResource.position,
      capacity: sourceTableResource.capacity,
      orders: orderIds.size,
      hasActiveOrders: orderIds.size > 0
    }];

    // Find all tables in the same place
    const allTables = await Resource.find({
      where: {
        placeId: sourceTableResource.placeId,
        kind: 'TABLE'
      }
    });

    // Find tables with no active orders (basic availability check)
    // Note: This is not reliable as the system has no visibility of actual table availability
    const tablesWithNoOrders = await Promise.all(
      allTables
        .filter(table => table.id !== resourceId) // Exclude source table
        .map(async table => {
          const tableOrders = await Order.app.models.Fulfillment.find({
            where: {
              status: 'OPEN',
              'destination.position.key': 'table',
              'destination.position.value': table.id
            }
          });

          return {
            table,
            hasOrders: tableOrders.length > 0
          };
        })
    );

    // Filter to tables with no active orders
    const availableTables = tablesWithNoOrders
      .filter(item => !item.hasOrders)
      .map(item => item.table);

    // Prepare available tables with basic information
    const availableTablesWithInfo = availableTables.map(table => ({
      resourceId: table.id,
      name: table.name,
      position: table.position,
      capacity: table.capacity,
      manualVerificationRequired: true // Flag indicating staff must verify availability
    }));

    // Estimate party size from source table orders
    const estimatedPartySize = Math.min(
      fulfillments.reduce((max, f) => Math.max(max, f.partySize || 1), 1),
      sourceTableResource.capacity
    );

    return {
      bookingEnabled: false,
      manualTableManagement: true,
      warnings: [
        'Table management is completely manual - system has no visibility of actual table availability',
        'Staff must visually verify tables are unoccupied before selecting destination tables',
        'System cannot prevent double-booking or conflicts'
      ],
      sourceTables,
      estimatedPartySize,
      availableTables: availableTablesWithInfo
    };
  }
};
```

### Commit Endpoint

The `staffRelocateTableCommit` method in `StaffWidgetApi.js` handles the execution phase:

```javascript
/**
 * Execute table relocation with selected source and destination tables
 * @param {Object} options - Relocation options
 * @param {String[]} options.sourceTableIds - IDs of tables to relocate from
 * @param {String[]} options.destinationTableIds - IDs of tables to relocate to
 * @param {Boolean} [options.notifyKitchen=true] - Whether to notify kitchen about relocation
 * @param {String} [options.reservationId] - ID of the reservation (required when booking is enabled)
 * @param {Boolean} [options.preserveNotes=true] - Whether to preserve booking notes (when booking is enabled)
 * @param {Boolean} [options.manuallyVerified] - Confirmation that staff has verified destination tables (required when booking is disabled)
 * @param {Boolean} [options.acknowledgeMissingValidation] - Acknowledgment of limited validation (required when booking is disabled)
 * @returns {Object} Result of the relocation operation
 */
Product.staffRelocateTableCommit = async function(options) {
  const { app } = Product;
  const { Resource, Booking, Order, Setting } = app.models;

  const {
    sourceTableIds,
    destinationTableIds,
    notifyKitchen = true,
    reservationId,
    preserveNotes = true,
    manuallyVerified,
    acknowledgeMissingValidation
  } = options || {};

  // Generate a transaction ID for tracking
  const transactionId = `tx-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

  // Validate common inputs
  if (!Array.isArray(sourceTableIds) || sourceTableIds.length === 0) {
    throw {
      statusCode: 400,
      code: 'INVALID_SOURCE_TABLES',
      message: 'Source table IDs are required'
    };
  }

  if (!Array.isArray(destinationTableIds) || destinationTableIds.length === 0) {
    throw {
      statusCode: 400,
      code: 'INVALID_DESTINATION_TABLES',
      message: 'Destination table IDs are required'
    };
  }

  // Find all source and destination table resources
  const [sourceTableResources, destTableResources] = await Promise.all([
    Resource.find({ where: { id: { inq: sourceTableIds } } }),
    Resource.find({ where: { id: { inq: destinationTableIds } } })
  ]);

  // Validate that all tables exist
  if (sourceTableResources.length !== sourceTableIds.length) {
    throw {
      statusCode: 404,
      code: 'SOURCE_TABLES_NOT_FOUND',
      message: 'One or more source tables not found'
    };
  }

  if (destTableResources.length !== destinationTableIds.length) {
    throw {
      statusCode: 404,
      code: 'DESTINATION_TABLES_NOT_FOUND',
      message: 'One or more destination tables not found'
    };
  }

  // Check if Booking feature is enabled
  const bookingEnabled = await Setting.findOne({
    where: { key: 'enableBooking', value: 'true' }
  });

  if (bookingEnabled) {
    // ===== BOOKING ENABLED FLOW =====

    // Validate reservation ID
    if (!reservationId) {
      throw {
        statusCode: 400,
        code: 'INVALID_RESERVATION',
        message: 'Reservation ID is required when booking is enabled'
      };
    }

    // Find the reservation and validate
    const bookings = await Booking.find({
      where: {
        reservationId,
        resourceId: { inq: sourceTableIds },
        status: { inq: ['SUCCESS', 'OPEN'] }
      }
    });

    if (bookings.length === 0) {
      throw {
        statusCode: 404,
        code: 'RESERVATION_NOT_FOUND',
        message: 'No active bookings found for this reservation and tables'
      };
    }

    // Get reservation details from the first booking
    const { partySize, startTime, endTime, note, deposit, personId, membershipId, orderId } = bookings[0];

    // Check if destination tables are available
    const now = new Date();
    const duration = Math.ceil((endTime - now) / (60 * 1000)); // remaining minutes

    const availableTables = await Resource.available(
      bookings[0].placeId,
      'TABLE',
      partySize,
      now,
      duration,
      true // allow combined tables
    );

    const availableTableIds = availableTables.map(t => t.id);

    // Verify all destination tables are available
    const unavailableTables = destinationTableIds.filter(
      id => !availableTableIds.includes(id)
    );

    if (unavailableTables.length > 0) {
      throw {
        statusCode: 409,
        code: 'TABLES_NOT_AVAILABLE',
        message: 'One or more destination tables are no longer available',
        details: { unavailableTables }
      };
    }

    // Begin transaction
    try {
      // 1. Create new bookings for destination tables
      const newBookings = [];

      for (const destTableId of destinationTableIds) {
        const destTable = destTableResources.find(t => t.id === destTableId);
        const newBooking = await Booking.create({
          kind: 'TABLE',
          status: 'SUCCESS',
          startTime,
          endTime,
          quantity: 1,
          capacity: destTable.capacity,
          partySize,
          note: preserveNotes ? note : undefined,
          arrivedAt: now,
          resourceId: destTableId,
          productId: destTable.productId,
          placeId: bookings[0].placeId,
          reservationId,
          personId,
          membershipId,
          orderId,
          deposit
        });

        newBookings.push(newBooking);
      }

      // 2. Prepare source and destination tables for order relocation
      const fromTables = sourceTableResources.map(table => ({
        resourceId: table.id,
        name: table.name,
        position: [{ key: 'table', value: table.id }]
      }));

      const toTables = destTableResources.map(table => ({
        resourceId: table.id,
        name: table.name,
        position: [{ key: 'table', value: table.id }]
      }));

      // 3. Relocate orders
      const relocateResult = await Order.relocateTableFulfillments(fromTables, toTables)

      // 4. End original bookings
      await Promise.all(
        bookings.map(booking =>
          booking.updateAttributes({
            status: 'ENDED',
            departedAt: now
          })
        )
      );

      // 5. Return success response
      return {
        success: true,
        bookingEnabled: true,
        transactionId,
        originalTables: fromTables,
        newTables: toTables,
        relocatedOrders: relocateResult.relocatedOrders,
        kitchenNotified: notifyKitchen
      };
    } catch (error) {
      // Log the error for debugging
      console.error(`[staffRelocateTableCommit] Transaction failed:`, error);

      // Attempt to rollback (in a real implementation, we would use a proper
      // transaction mechanism provided by the database)
      try {
        // Rollback new bookings if they were created
        // This is a simplified version - in a real implementation,
        // we would use database transactions

        throw {
          statusCode: 500,
          code: 'TRANSACTION_FAILED',
          message: 'Table relocation failed',
          details: {
            error: error.message || 'Unknown error',
            transactionId
          }
        };
      } catch (rollbackError) {
        console.error(`[staffRelocateTableCommit] Rollback failed:`, rollbackError);

        throw {
          statusCode: 500,
          code: 'ROLLBACK_FAILED',
          message: 'Table relocation failed and rollback was unsuccessful',
          details: {
            error: error.message || 'Unknown error',
            rollbackError: rollbackError.message || 'Unknown rollback error',
            transactionId
          }
        };
      }
    }
  } else {
    // ===== BOOKING DISABLED FLOW =====

    // Validate manual verification
    if (!manuallyVerified) {
      throw {
        statusCode: 400,
        code: 'MANUAL_VERIFICATION_REQUIRED',
        message: 'Staff must manually verify that destination tables are available'
      };
    }

    // Validate acknowledgment
    if (!acknowledgeMissingValidation) {
      throw {
        statusCode: 400,
        code: 'ACKNOWLEDGMENT_REQUIRED',
        message: 'Staff must acknowledge that the system cannot validate table availability'
      };
    }

    // When booking is disabled, we only support single table to single table relocation
    if (sourceTableIds.length !== 1 || destinationTableIds.length !== 1) {
      throw {
        statusCode: 400,
        code: 'SINGLE_TABLE_ONLY',
        message: 'Only single table to single table relocation is supported when booking is disabled'
      };
    }

    const sourceTableId = sourceTableIds[0];
    const destTableId = destinationTableIds[0];

    // Find all orders associated with the source table
    const fulfillments = await Order.app.models.Fulfillment.find({
      where: {
        status: 'OPEN',
        'destination.position.key': 'table',
        'destination.position.value': sourceTableId
      }
    });

    if (fulfillments.length === 0) {
      throw {
        statusCode: 400,
        code: 'NO_ACTIVE_ORDERS',
        message: 'No active orders found for the source table'
      };
    }

    // Begin simple transaction
    try {
      // 1. Prepare source and destination tables for order relocation
      const fromTable = {
        resourceId: sourceTableId,
        name: sourceTableResources[0].name,
        position: [{ key: 'table', value: sourceTableId }]
      };

      const toTable = {
        resourceId: destTableId,
        name: destTableResources[0].name,
        position: [{ key: 'table', value: destTableId }]
      };

      // Use the same relocateTableFulfillments API for consistency
      const relocateResult = await Order.relocateTableFulfillments([fromTable], [toTable]);

      // 2. Return success response with warnings
      return {
        success: true,
        bookingEnabled: false,
        manualTableManagement: true,
        warnings: [
          'Table management is completely manual - system has no visibility of actual table availability',
          'Staff must ensure destination table is actually available to prevent conflicts'
        ],
        transactionId,
        originalTables: [fromTable],
        newTables: [toTable],
        relocatedOrders: relocateResult.relocatedOrders,
        kitchenNotified: notifyKitchen
      };
    } catch (error) {
      // Log the error for debugging
      console.error(`[staffRelocateTableCommit] Transaction failed:`, error);

      throw {
        statusCode: 500,
        code: 'TRANSACTION_FAILED',
        message: 'Table relocation failed',
        details: {
          error: error.message || 'Unknown error',
          transactionId
        }
      };
    }
  }
};
```

## Implementation Phases

The implementation is divided into four progressive phases:

### Phase 1: Core Relocation Functionality (2 weeks)

1. **Implement Intent Endpoint**
   - Create `staffRelocateTableIntent` method in `StaffWidgetApi.js`
   - Implement booking feature detection to handle both scenarios
   - Add validation for relocation feasibility

2. **Implement Commit Endpoint**
   - Create `staffRelocateTableCommit` method in `StaffWidgetApi.js`
   - Implement atomic operations with transaction management in the correct sequence:
     1. Create new bookings for destination tables (when booking is enabled)
     2. Relocate orders from source tables to destination tables
     3. End original bookings for source tables (when booking is enabled)
   - Add comprehensive error handling and rollback mechanisms

### Phase 2: Kitchen Notification and UI Enhancements (1-2 weeks)

1. **Enhance Kitchen Notifications**
   - Implement detailed context about table changes
   - Add source table information to kitchen tickets
   - Add visual indicators for relocated orders in kitchen displays

2. **Improve UI/UX**
   - Enhance API responses with detailed information
   - Include order counts and details with table information
   - Add suitability scores for destination tables

### Phase 3: Advanced Table Management (1-2 weeks)

1. **Enhance Table Selection**
   - Implement logical grouping of tables
   - Add adjacency information for better visualization
   - Include capacity optimization suggestions

2. **Improve Partial Relocation**
   - Enhance support for partial relocations
   - Maintain relationships between tables in the same reservation
   - Implement smarter order distribution for partial relocations

### Phase 4: Monitoring and Advanced Features (1-2 weeks)

1. **Add Monitoring and Reporting**
   - Implement metrics collection for relocation operations
   - Add performance monitoring for transaction times
   - Create staff activity reports for relocations

2. **Scheduled and Conditional Relocations**
   - Allow staff to schedule relocations for future times
   - Implement notification system for upcoming relocations
   - Add support for conditional relocations based on rules
