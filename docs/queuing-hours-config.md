# Queuing Hours Configuration Guide

This guide provides detailed configuration examples and usage scenarios for the table queuing hours validation feature.

## Configuration Examples

### Option 1: Use Opening Hours (Default Behavior)

```json
{
  "openingHours": {
    "periods": [
      {
        "open": { "day": 1, "time": "1130" },
        "close": { "day": 1, "time": "1430" }
      },
      {
        "open": { "day": 1, "time": "1730" },
        "close": { "day": 1, "time": "2130" }
      }
    ]
  }
}
```

**Result**: Queue requests are only accepted during restaurant operating hours (11:30-14:30 and 17:30-21:30).

### Option 2: Extended Queuing Hours

```json
{
  "openingHours": {
    "periods": [
      {
        "open": { "day": 1, "time": "1130" },
        "close": { "day": 1, "time": "2130" }
      }
    ]
  },
  "queuing": {
    "hours": {
      "periods": [
        {
          "open": { "day": 1, "time": "1100" },
          "close": { "day": 1, "time": "2200" }
        }
      ]
    }
  }
}
```

**Result**: Customers can join the queue from 11:00-22:00, even though the restaurant only operates 11:30-21:30. This allows:
- Early queue joining (30 minutes before opening)
- Late queue joining (30 minutes after closing)

### Option 3: 24/7 Queue Acceptance

```json
{
  "openingHours": {
    "periods": [
      {
        "open": { "day": 1, "time": "1130" },
        "close": { "day": 1, "time": "2130" }
      }
    ]
  },
  "queuing": {
    "hours": {
      "periods": [
        {
          "open": { "day": 0, "time": "0000" },
          "close": { "day": 6, "time": "2359" }
        }
      ]
    }
  }
}
```

**Result**: Queue requests are accepted at any time (restores original behavior before validation was added).

## Usage Scenarios

### Scenario 1: Restaurant with Standard Hours

- **Opening Hours**: 11:30-14:30, 17:30-21:30
- **Queuing Hours**: Not configured (uses opening hours)
- **Result**: Queue requests only accepted during operating hours

**Example Request at 22:26 (outside hours):**
```json
{
  "error": {
    "statusCode": 400,
    "message": "OUTSIDE_QUEUING_HOURS",
    "details": {
      "currentTime": "2025-08-01T14:26:05.928Z",
      "nextQueueingAt": "2025-08-02T03:30:00.000Z",
      "timeZone": "Asia/Taipei"
    }
  }
}
```
*Customer informed they can try again tomorrow at 11:30 AM*

### Scenario 2: Restaurant with Extended Queue Hours

- **Opening Hours**: 11:30-21:30
- **Queuing Hours**: 11:00-22:00
- **Result**: Queue requests accepted 30 minutes before opening and 30 minutes after closing

**Example Request at 11:15 (during extended queue hours):**
- ✅ **Success**: Queue request accepted even though restaurant not yet open
- Customer can join queue 15 minutes before restaurant opens

**Example Request at 22:30 (outside extended hours):**
```json
{
  "error": {
    "statusCode": 400,
    "message": "OUTSIDE_QUEUING_HOURS",
    "details": {
      "currentTime": "2025-08-01T14:30:00.000Z",
      "nextQueueingAt": "2025-08-02T03:00:00.000Z",
      "timeZone": "Asia/Taipei"
    }
  }
}
```
*Customer informed they can try again tomorrow at 11:00 AM*

### Scenario 3: 24/7 Queue Acceptance

- **Opening Hours**: 11:30-21:30
- **Queuing Hours**: 24/7 periods configured
- **Result**: Queue requests always accepted (original behavior)
- **nextQueueingAt**: Always null since queuing never closes

## Client Integration

Client applications can handle the enhanced error response to provide better user experience:

```javascript
async function handleQueueRequest(customer, placeId, partySize) {
    try {
        const result = await requestTableQueue(customer, placeId, partySize)
        return { success: true, data: result }
    } catch (error) {
        if (error.message === 'OUTSIDE_QUEUING_HOURS') {
            const { details } = error
            const { nextQueueingAt, timeZone } = details
            
            if (nextQueueingAt) {
                const nextTime = dayjs(nextQueueingAt).tz(timeZone)
                return {
                    success: false,
                    message: `Queue requests will be available at ${nextTime.format('HH:mm')}`,
                    retryAt: nextQueueingAt,
                    canRetry: true
                }
            } else {
                return {
                    success: false,
                    message: 'Queue requests are not currently available',
                    canRetry: false
                }
            }
        }
        throw error
    }
}
```

## Implementation Details

### Code Location
- **File**: `server/mixins/TableQueuing.js`
- **Function**: `Product.requestTableQueue`
- **Validation**: Added after existing party size validation, before resource availability check

### Dependencies
- **@perkd/utils**: `isOpen` and `nextOpen` functions for hours validation and next time calculation
- **Place Model**: Access to `openingHours` and `queuing.hours` configuration
- **dayjs**: Timezone-aware date handling

### Timezone Handling
The validation properly handles timezone conversion using the configured `LOCALE.timeZone` setting (typically `Asia/Taipei`), ensuring accurate time comparisons regardless of server timezone.

## Related Issues

This implementation addresses validation gaps identified in the queue scheduling system:
1. ✅ **Customers could join queues at any time** (now fixed with time-based validation)
2. ⚠️ **Queue scheduling ignores business hours** (separate issue in `Queue.schedule` function, still needs fixing)
3. ✅ **No separation between service hours and queue acceptance hours** (now available through `queuing.hours` configuration)
