# API Documentation - Product Service (CRM)

## Table of Contents
1.  [Product Management](#product-management)
2.  [Variant Management](#variant-management)
3.  [Order Management](#order-management)
4.  [Shop Widget APIs](#shop-widget-apis)
5.  [Table Widget APIs](#table-widget-apis)
    - [Table Booking APIs](#table-booking-apis)
    - [Table Queuing APIs](#table-queuing-apis)
6.  [Table Booking Website APIs](#table-booking-website-apis)
7.  [Staff Widget APIs](#staff-widget-apis)
    - [Product Management APIs](#product-management-apis)
    - [Table Management APIs](#table-management-apis)
    - [Table Relocation APIs](#table-relocation-apis)
8.  [AppLink Management](#applink-management)
9.  [External Provider Integration](#external-provider-integration)
10. [Service Management](#service-management)
11. [Validation and Error Responses](#validation-and-error-responses)

## Base URL
The base URL for all API endpoints is your server's domain.

## Product Management

### Product Operations
- **GET** `/Products/findByIds`
  - Find products by ID list
  - Parameters:
    ```json
    {
      "ids": ["string"],
      "query": {
        "fields": ["string"],     // Optional - specific fields to return
        "includeDeleted": "boolean" // Optional - include soft deleted products
      }
    }
    ```
  - Response: Array of product objects with structure:
    ```json
    {
      "id": "string",
      "title": "string",
      "description": "string",
      "brand": "string",
      "availability": "string",
      "availabilityDate": "date",
      "priceRange": {
        "min": "number",
        "max": "number",
        "currency": "string"
      },
      "isLowQuantity": "boolean",
      "isSoldOut": "boolean",
      "isBackOrder": "boolean",
      "attributes": "object",
      "external": {
        "source": "string",
        "id": "string",
        "shopId": "string"
      },
      "tags": ["string"],
      "variants": ["string"]  // Array of variant IDs
    }
    ```

## Product Provisioning

- **POST** `/Products/provision/storedvalue`
  - Create Product & Variants for Stored Value
  - Parameters:
    ```json
    {
      "type": "string",           // Required, enum: ["topup", "preload"]
      "currency": {               // Required
        "code": "string",         // e.g. "SGD"
        "symbol": "string",       // e.g. "$"
        "precision": "number",    // e.g. 2
        "exchangeRate": "number"  // e.g. 1.0
      },
      "denominations": ["number"], // Optional, fixed amounts e.g. [10,20,50]
      "variable": {               // Optional, for variable amounts
        "min": "number",          // Required if variable
        "max": "number",          // Optional max limit
        "increment": "number"     // Optional increment value
      },
      "metadata": "object"        // Optional additional data
    }
    ```
  - Response: Created product object with variants

### Digital Fulfillment
- **POST** `/Products/fulfillments/digital`
  - Process digital product fulfillment
  - Parameters:
    ```json
    {
      "orderId": "string",      // Required - Order ID
      "items": [{               // Required - Array of items to fulfill
        "itemId": "string",     // Required - Order item ID
        "variantId": "string",  // Required - Product variant ID
        "quantity": "number"    // Required - Quantity to fulfill
      }]
    }
    ```
  - Response: Array of fulfilled items with structure:
    ```json
    [{
      "itemId": "string",
      "status": "string",      // enum: ["fulfilled", "failed"]
      "code": "string",        // Error code if failed
      "message": "string",     // Status message
      "fulfillmentData": {     // Optional fulfillment details
        "codes": ["string"],   // e.g. PIN codes, serial numbers
        "expiresAt": "date"    // Optional expiration date
      }
    }]
    ```

- **POST** `/Products/fulfillments/digital/cancel`
  - Cancel digital fulfillment
  - Parameters:
    ```json
    {
      "orderId": "string",     // Required - Order ID
      "items": [{              // Required - Items to cancel
        "itemId": "string",    // Required - Order item ID
        "reason": "string"     // Optional - Cancellation reason
      }]
    }
    ```
  - Response: Array of cancelled items with status


## Variant Management

### Fulfillment Management
- **POST** `/Variants/fulfillments/digital`
  - Digital product fulfillment at variant level
  - Parameters:
    ```json
    {
      "orderId": "string",
      "items": [{
        "itemId": "string",
        "variantId": "string",
        "quantity": "number",
        "options": {           // Optional fulfillment options
          "immediate": "boolean",
          "notifyCustomer": "boolean"
        }
      }]
    }
    ```
  - Response: Array of fulfilled items with details

- **POST** `/Variants/fulfillments/digital/cancel`
  - Cancel digital variant fulfillment
  - Parameters:
    ```json
    {
      "orderId": "string",
      "items": [{
        "itemId": "string",
        "variantId": "string",
        "reason": "string",
        "refund": "boolean"    // Optional - trigger refund
      }]
    }
    ```
  - Response: Array of cancelled items with status

- **POST** `/Variants/fulfillments/status`
  - Check fulfillment status
  - Parameters:
    ```json
    {
      "orderId": "string",    // Required - Order ID
      "items": ["string"]     // Optional - Specific item IDs to check
    }
    ```
  - Response:
    ```json
    {
      "status": "string",     // Overall status
      "items": [{
        "itemId": "string",
        "status": "string",   // enum: ["pending", "processing", "fulfilled", "failed", "cancelled"]
        "updatedAt": "date",
        "fulfillmentData": "object"
      }]
    }
    ```

- **POST** `/Variants/fulfillments/request`
  - Send fulfillment request to provider
  - Parameters:
    ```json
    {
      "orderId": "string",    // Required - Order ID
      "provider": "string",   // Required - Provider identifier
      "items": [{            // Required - Items to fulfill
        "itemId": "string",
        "variantId": "string",
        "quantity": "number"
      }],
      "options": {           // Optional provider-specific options
        "immediate": "boolean",
        "priority": "string"
      }
    }
    ```
  - Response:
    ```json
    {
      "requestId": "string",
      "status": "string",
      "items": [{
        "itemId": "string",
        "status": "string",
        "message": "string"
      }]
    }
    ```

- **POST** `/Variants/fulfillments/delivered`
  - Mark fulfillment as delivered
  - Parameters:
    ```json
    {
      "orderId": "string",     // Required - Order ID
      "items": [{              // Required - Items delivered
        "itemId": "string",
        "deliveredAt": "date", // Optional - Delivery timestamp
        "proof": "object"      // Optional - Delivery proof
      }]
    }
    ```
  - Response:
    ```json
    {
      "success": "boolean",
      "items": [{
        "itemId": "string",
        "status": "string",
        "deliveryDetails": "object"
      }]
    }
    ```

- **POST** `/Variants/fulfillments/cancel`
  - Cancel fulfillment of items
  - Parameters:
    ```json
    {
      "orderId": "string",     // Required - Order ID
      "items": [{              // Required - Items to cancel
        "itemId": "string",
        "reason": "string",    // Required - Cancellation reason
        "refund": "boolean"    // Optional - Trigger refund
      }],
      "notify": "boolean"      // Optional - Notify customer
    }
    ```
  - Response:
    ```json
    {
      "success": "boolean",
      "cancelled": [{
        "itemId": "string",
        "status": "string",
        "refundStatus": "string"
      }]
    }
    ```

### Resource Operations

- **POST** `/Resources/availableby` – Find available resources by IDs and time period
  - Parameters:
    ```json
    {
      "ids": ["string"],       // Required - Array of resource IDs
      "from": "date",          // Required - Start time
      "to": "date",            // Required - End time
      "quantity": "number",    // Required - Number of units needed
      "price": "number"        // Optional - Price match
    }
    ```
  - Response: Array of available resource objects

- **POST** `/Resources/{id}/balance` – Get resource capacity balance for time period
  - Parameters:
    ```json
    {
      "from": "date",          // Required - Start time
      "to": "date"             // Required - End time
    }
    ```
  - Response:
    ```json
    {
      "available": "number",   // Available capacity
      "reserved": "number",    // Reserved capacity
      "total": "number"        // Total capacity
    }
    ```


### Order Management

- **POST** `/Variants/order`
  - Create order without payment
  - Parameters:
    ```json
    {
      "userId": "string",          // Optional - Perkd personId
      "order": {                   // Required - Order details
        "items": [{                // Required - Array of order items
          "variantId": "string",   // Required - Variant ID
          "quantity": "number",    // Required - Quantity
          "options": "object",     // Optional - Item-specific options
          "metadata": "object"     // Optional - Additional item data
        }],
        "currency": "string",      // Required - Order currency
        "shipping": "object",      // Optional - Shipping details
        "billing": "object"        // Optional - Billing details
      },
      "pricings": [{              // Required - Pricing details
        "variantId": "string",
        "price": "number",
        "currency": "string",
        "discounts": [{
          "type": "string",
          "amount": "number",
          "code": "string"
        }]
      }],
      "options": {                // Optional - Order options
        "reservation": "boolean", // Hold inventory without payment
        "through": "string",     // Order source/channel
        "cardId": "string",      // Associated card ID
        "description": "string", // Order description
        "metadata": "object",    // Additional order metadata
        "notification": "boolean", // Send notifications
        "noNotify": "boolean"    // Suppress notifications
      }
    }
    ```
  - Response:
    ```json
    {
      "orderId": "string",
      "status": "string",       // Order status
      "items": [{              // Ordered items with status
        "itemId": "string",
        "variantId": "string",
        "status": "string",
        "quantity": "number",
        "pricing": "object"
      }],
      "fulfilled": [{          // Any immediately fulfilled items
        "itemId": "string",
        "fulfillmentData": "object"
      }],
      "total": {
        "amount": "number",
        "currency": "string"
      }
    }
    ```

- **POST** `/Variants/order/commit`
  - Commit pending payment for order
  - Parameters:
    ```json
    {
      "orderId": "string",     // Required - Order ID
      "payment": {             // Required - Payment details
        "method": "string",    // Payment method
        "amount": "number",    // Payment amount
        "currency": "string",  // Payment currency
        "reference": "string", // Payment reference
        "metadata": "object"   // Additional payment data
      }
    }
    ```
  - Response:
    ```json
    {
      "success": "boolean",
      "status": "string",     // Payment status
      "transaction": {        // Transaction details
        "id": "string",
        "status": "string",
        "amount": "number",
        "currency": "string",
        "timestamp": "date"
      }
    }
    ```

- **POST** `/Variants/order/items/complete`
  - Complete order items with full details
  - Parameters:
    ```json
    {
      "orderId": "string",     // Required - Order ID
      "items": [{              // Required - Items to complete
        "itemId": "string",    // Required - Item ID
        "status": "string",    // Required - New status
        "metadata": "object",  // Optional - Additional completion data
        "fulfillmentData": "object" // Optional - Fulfillment details
      }]
    }
    ```
  - Response:
    ```json
    {
      "success": "boolean",
      "items": [{
        "itemId": "string",
        "status": "string",
        "completedAt": "date",
        "metadata": "object"
      }]
    }
    ```

- **POST** `/Variants/order/external/transform`
  - Transform external order to internal format
  - Parameters:
    ```json
    {
      "provider": "string",    // Required - External provider ID
      "order": {              // Required - External order data
        "id": "string",       // External order ID
        "items": [{           // Order items
          "id": "string",     // External item ID
          "sku": "string",    // Product SKU
          "quantity": "number",
          "price": "number",
          "metadata": "object"
        }],
        "customer": "object", // Customer information
        "shipping": "object", // Shipping details
        "payment": "object"   // Payment information
      }
    }
    ```
  - Response:
    ```json
    {
      "orderId": "string",    // Internal order ID
      "items": [{            // Transformed items
        "itemId": "string",
        "variantId": "string",
        "quantity": "number",
        "pricing": "object",
        "external": {        // Original external data
          "id": "string",
          "metadata": "object"
        }
      }],
      "metadata": {          // Additional order metadata
        "provider": "string",
        "externalId": "string",
        "originalOrder": "object"
      }
    }
    ```

- **POST** `/Variants/order/pay`
  - Create order with payment
  - Parameters:
    ```json
    {
      "userId": "string",          // Optional - User ID
      "payments": [{               // Required - Payment details
        "method": "string",        // Payment method
        "amount": "number",        // Amount to charge
        "currency": "string",      // Payment currency
        "reference": "string",     // Payment reference
        "metadata": "object"       // Additional payment data
      }],
      "order": {                   // Required - Order details
        "items": [{                // Required - Order items
          "variantId": "string",   // Required - Variant ID
          "quantity": "number",    // Required - Quantity
          "options": "object"      // Optional - Item options
        }],
        "currency": "string",      // Required - Order currency
        "shipping": "object",      // Optional - Shipping details
        "billing": "object"        // Optional - Billing details
      },
      "pricings": [{              // Required - Item pricing details
        "variantId": "string",
        "price": "number",
        "currency": "string",
        "discounts": [{
          "type": "string",
          "amount": "number",
          "code": "string"
        }]
      }],
      "options": {                // Optional - Order options
        "immediate": "boolean",   // Process immediately
        "through": "string",      // Order source
        "metadata": "object",     // Additional metadata
        "notification": "boolean" // Send notifications
      }
    }
    ```
  - Response:
    ```json
    {
      "orderId": "string",
      "status": "string",
      "payment": {               // Primary payment result
        "id": "string",
        "status": "string",
        "amount": "number",
        "currency": "string"
      },
      "payments": [{            // All payment results
        "id": "string",
        "method": "string",
        "status": "string",
        "amount": "number",
        "currency": "string",
        "timestamp": "date"
      }],
      "fulfilled": [{          // Any fulfilled items
        "itemId": "string",
        "fulfillmentData": "object"
      }]
    }
    ```

### Shop Widget APIs
- **GET** `/Variants/app/products`
  - Get variants for custom storefronts
  - Query Parameters:
    ```json
    {
      "shopId": "string",      // Optional - Filter by shop
      "categoryId": "string",  // Optional - Filter by category
      "search": "string",      // Optional - Search term
      "sort": "string",        // Optional - Sort field
      "order": "string",       // Optional - Sort order (asc/desc)
      "limit": "number",       // Optional - Results per page
      "offset": "number"       // Optional - Pagination offset
    }
    ```
  - Response:
    ```json
    {
      "total": "number",       // Total matching variants
      "variants": [{
        "variantId": "string",
        "subtitle": "string",
        "productId": "string",
        "kind": "string",      // Product type/category
        "gtin": {              // Global Trade Item Number
          "code": "string",    // GTIN type (UPC, EAN, etc)
          "value": "string"    // Actual code value
        },
        "sku": "string",       // Stock Keeping Unit
        "mpn": "string",       // Manufacturer Part Number
        "weight": {
          "value": "number",
          "unit": "string",    // e.g. "g", "kg"
          "count": "number"    // Items per unit
        },
        "prices": [{
          "value": "number",
          "currency": "string",
          "conditions": {      // Price conditions
            "quantity": "number",
            "dates": {
              "start": "date",
              "end": "date"
            }
          },
          "paymentMethods": ["string"]
        }],
        "taxable": "boolean",
        "taxCode": "string",   // Tax classification code
        "variations": [{       // Product variations
          "id": "string",
          "name": "string",    // Variation name
          "label": "string",   // Display label
          "type": "string",    // Data type
          "required": "boolean",
          "validation": {      // Input validation rules
            "min": "number",
            "max": "number",
            "pattern": "string"
          },
          "allowedValues": ["string"],
          "attributes": "object"
        }],
        "options": [{          // Product options
          "id": "string",
          "value": "string",
          "label": "string",
          "position": "number",
          "metadata": "object",
          "isDefault": "boolean",
          "isDisabled": "boolean",
          "pricing": {         // Option-specific pricing
            "adjustment": "number",
            "type": "string"   // e.g. "fixed", "percent"
          },
          "inventory": {       // Option-specific inventory
            "quantity": "number",
            "reserved": "number"
          }
        }],
        "inventory": {
          "management": "string", // Inventory tracking method
          "policy": "string",     // Out of stock policy
          "quantity": "number",   // Available quantity
          "lowQuantityWarningThreshold": "number"
        },
        "imageUrls": ["string"],
        "status": "string",      // e.g. "active", "disabled"
        "metadata": "object"     // Additional variant data
      }],
      "pagination": {
        "limit": "number",
        "offset": "number",
        "hasMore": "boolean"
      }
    }
    ```

--------------------------------
## Table Widget APIs
For table booking and queuing via Widgets. Includes both booking and queuing functionality.

### Table Booking APIs

- **POST** `/Products/app/booking/tables/availability`
  - Check table availability at place
  - Parameters:
    ```json
    {
      "placeId": "string",        // Required - Place ID
      "partySize": "number",      // Required - Number of guests
      "from": "date",             // Required - Start time
      "allowCombined": "boolean"  // Optional - Accept combined tables, default: true
    }
    ```
  - Response: Array of available table resources with structure:
    ```json
    [
      {
        "id": "string",           // Resource ID
        "name": "string",         // Table name/number
        "position": "string",     // Table position/location
        "capacity": "number",     // Table capacity
        "kind": "string",         // Always "table"
        "metadata": "object"      // Additional table data
      }
    ]
    ```

- **POST** `/Products/app/booking/tables`
  - Request table booking at place for members
  - Parameters:
    ```json
    {
      "digitalCard": {            // Required - Digital card information
        "id": "string"            // Required - Card ID of member
      },
      "placeId": "string",        // Required - Place ID
      "partySize": "number",      // Required - Number of guests
      "from": "date",             // Required - Start time
      "allowCombined": "boolean", // Optional - Accept combined tables, default: true
      "adjacentOnly": "boolean",   // Optional - Adjacent tables only, default: true
      "note": "string"            // Optional - Special request from customer
    }
    ```
  - Response: Array of assigned tables:
    ```json
    [{
      "name": "string",         // Table name/number
      "position": "number",     // Table position/location
      "capacity": "number",     // Table capacity
      "startTime": "date",      // Booking start time
      "endTime": "date",        // Booking end time
      "resourceId": "string"    // Resource ID
    }]
    ```

### Table Queuing APIs

- **POST** `/Products/app/queuing/checkin`
  - Check-in for tables with Smart Release and Reassign approach
  - Parameters:
    ```json
    {
      "digitalCard": {            // Required - Digital card information
        "id": "string"            // Required - Card ID of member
      },
      "placeId": "string",        // Required - Place ID
      "reservationId": "string"    // Optional - Specific reservation ID
    }
    ```
  - Response: Check-in result with structure:
    ```json
    {
      "kind": "table",          // Resource type, always "table" for this endpoint
      "status": "string",       // Status: "assigned", "reassigned", "queued", "none", or current status if existing
      "queueId": "string",      // Queue entry ID (if queued)
      "scheduledAt": "date",    // Estimated service time (if queued)
      "endTime": "date",        // Time after which entry is considered abandoned (if queued)
      "placeId": "string",      // Place ID
      "resources": [{           // Array of resources (if assigned or reassigned)
        "name": "string",      // Table name/number
        "position": "string",   // Table position/location
        "capacity": "number",   // Table capacity
        "resourceId": "string", // Resource ID
        "bookingId": "string"   // Booking ID
      }],
      "bookings": [{            // Original bookings (if assigned)
        "id": "string",        // Booking ID
        "resourceId": "string", // Resource ID
        "reservationId": "string" // Reservation ID
      }],
      "originalBookings": [{    // Original bookings (if reassigned or queued)
        "id": "string",        // Booking ID
        "resourceId": "string", // Resource ID
        "reservationId": "string" // Reservation ID
      }],
      "existing": "boolean"     // True if customer is already in queue
    }
    ```

- **POST** `/Products/app/queuing/tables`
  - Request to join the queue for tables at place for members
  - Parameters:
    ```json
    {
      "digitalCard": {            // Required - Digital card information
        "id": "string"            // Required - Card ID of member
      },
      "placeId": "string",        // Required - Place ID
      "partySize": "number",      // Required - Number of guests
      "allowCombined": "boolean", // Optional - Accept combined tables, default: true
      "adjacentOnly": "boolean"   // Optional - Adjacent tables only, default: true
    }
    ```
  - Response: Queue information with structure:
    ```json
    {
      "kind": "table",          // Resource type, always "table" for this endpoint
      "status": "string",       // Status: "waiting", "assigned", or current status if existing
      "queueId": "string",      // Queue entry ID (if waiting)
      "scheduledAt": "date",    // Estimated service time (if waiting)
      "endTime": "date",        // Time after which entry is considered abandoned
      "placeId": "string",      // Place ID
      "resources": [{           // Array of resources (if assigned immediately)
        "name": "string",      // Table name/number
        "position": "string",   // Table position/location
        "capacity": "number",   // Table capacity
        "resourceId": "string", // Resource ID
        "bookingId": "string"   // Booking ID
      }],
      "existing": "boolean"     // True if customer is already in queue
    }
    ```

- **DELETE** `/Products/app/queuing/tables/:id`
  - Cancel queue for tables
  - Parameters:
    - `id` (string, required): Queue ID in path
  - Response: Cancellation confirmation object

--------------------------------
## Table Booking Website APIs

- **GET** `/Products/web/booking/tables/availability`
  - Get comprehensive table availability matrix for website calendars
  - Query Parameters:
    ```
    placeId: string        // Required - Place ID
    ```
  - Response:
    ```json
    {
      "placeId": "string",
      "lastUpdated": "date",
      "dateRange": {
        "start": "string",        // Format: YYYY-MM-DD
        "end": "string"           // Format: YYYY-MM-DD
      },
      "config": {
        "operatingHours": {
          "monday": { "open": "string", "close": "string" },
          "tuesday": { "open": "string", "close": "string" },
          // ...other days of week
        },
        "timeSlotDuration": "number",        // In minutes
        "maxPartySize": "number",
        "minPartySize": "number",
        "leadTimeMinutes": "number",
        "maxAdvanceBookingDays": "number",
        "partySizeRanges": [
          { "min": "number", "max": "number", "label": "string" }
          // Example: { "min": 1, "max": 2, "label": "1-2" }
        ]
      },
      "availability": [
        {
          "date": "string",                  // Format: YYYY-MM-DD
          "dayOfWeek": "string",             // Example: "Monday"
          "isToday": "boolean",
          "timeSlots": [
            {
              "time": "string",              // Format: HH:MM
              "timestamp": "date",
              "partySizeAvailability": {
                "1-2": "string",             // Values: "high", "medium", "low", "none"
                "3-4": "string",
                "5-8": "string",
                "9+": "string"
              },
              "exactAvailability": {
                "1-2": "number",             // Number of available tables
                "3-4": "number",
                "5-8": "number",
                "9+": "number"
              }
            }
            // ...more time slots
          ]
        }
        // ...more dates
      ]
    }
    ```

- **POST** `/Products/web/booking/tables`
  - Request table booking at place for non-members
  - Parameters:
    ```json
    {
      "placeId": "string",        // Required - Place ID
      "name": "string",           // Required - customer name
      "mobile": {
        "countryCode": "string",  // Required - Country code
        "number": "string"        // Required - Mobile number
      },
      "partySize": "number",      // Required - Number of guests
      "from": "date",             // Required - Start time
      "allowCombined": "boolean", // Optional - Accept combined tables, default: true
      "adjacentOnly": "boolean",   // Optional - Adjacent tables only, default: true
      "note": "string"            // Optional - Special request from customer
    }
    ```
  - Response:
    ```json
    {
      "reservationId": "string", // Unique identifier linking multiple bookings
      "member": "boolean",       // is customer a member
      "ttl": "number",           // on hold for milliseconds, undefined if confirmed (member & no deposit)
      "deposit": {
        "value": "number",      // amount
        "currency": "string",   // ISO 4217
        "formatted": "string"   // eg. NT$100
      },
      "welcome": {
        "value": "number",      // amount
        "currency": "string",   // ISO 4217
        "formatted": "string"   // eg. NT$100
      }
    }
    ```

--------------------------------
## Staff Widget APIs

### Product Management APIs
- **POST** `/Products/staff/products/:variantId/availability`
  - Set product availability
  - Parameters:
    - `variantId` (string, required): Variant ID
    ```json
    {
      "available": "boolean",     // Required - Availability status
      "reason": "string",         // Optional - Reason for change
      "availableFrom": "date",    // Optional - Future availability
      "availableUntil": "date"    // Optional - Availability end
    }
    ```
  - Response:
    ```json
    {
      "success": "boolean",
      "variant": {
        "id": "string",
        "available": "boolean",
        "availabilityDetails": {
          "status": "string",
          "reason": "string",
          "dates": {
            "from": "date",
            "until": "date"
          }
        },
        "updatedAt": "date"
      }
    }
    ```

- **POST** `/Products/staff/variants/:id/status`
  - Update variant status
  - Parameters:
    - `id` (string, required): Variant ID
    ```json
    {
      "status": "string",        // Required - New status
      "reason": "string",        // Optional - Status change reason
      "metadata": "object"       // Optional - Additional status data
    }
    ```
  - Response:
    ```json
    {
      "success": "boolean",
      "variant": {
        "id": "string",
        "status": "string",
        "statusHistory": [{
          "status": "string",
          "reason": "string",
          "timestamp": "date"
        }],
        "updatedAt": "date"
      }
    }
    ```

- **POST** `/Products/staff/menu/refresh`
  - Request provider menu sync
  - Parameters:
    ```json
    {
      "provider": "string",     // Required - Provider ID
      "shopId": "string",       // Optional - Specific shop to sync
      "options": {              // Optional - Sync options
        "force": "boolean",     // Force full sync
        "categories": ["string"], // Specific categories
        "immediate": "boolean"  // Skip queue
      }
    }
    ```
  - Response:
    ```json
    {
      "success": "boolean",
      "syncId": "string",      // Sync job ID
      "status": "string",      // Initial sync status
      "estimatedTime": "number" // Estimated completion time
    }
    ```

### Table Management APIs
- **GET** `/Products/staff/tables/timings`
  - Get table resource timings for staff
  - Query Parameters:
    ```
    placeId: string        // Required - Place ID
    ```
  - Response: Array of table resources with timing information:
    ```json
    [
      {
        "id": "string",           // Resource ID
        "name": "string",         // Table name/number
        "capacity": "number",     // Table capacity
        "position": [             // Table position information
          {
            "key": "string",
            "value": "string"
          }
        ],
        "status": "string",       // Current status: "available", "reserved-soon", "occupied"
        "availableAt": "date",    // Earliest time when table becomes available
        "nextUnavailableAt": "date", // When the available window ends (if any)
        "currentOrders": [],      // Active orders with estimatedEndsAt
        "currentBookings": [],    // Ongoing bookings (started but not ended)
        "futureBookings": [],     // Upcoming bookings
        "timeZone": "string"      // Table's timezone
      }
    ]
    ```

- **POST** `/Products/staff/tables/open`
  - Open a table for use, ending active bookings
  - Parameters:
    ```json
    {
      "table": {
        "resourceId": "string"    // Required - ID of the table to open
      }
    }
    ```
  - Response:
    ```json
    {
      "resource": {
        "id": "string",
        "name": "string",
        "position": ["object"]
      }
    }
    ```

- **POST** `/Products/staff/tables/close`
  - Take a table offline for a period of time
  - Parameters:
    ```json
    {
      "table": {
        "resourceId": "string"    // Required - ID of the table to close
      },
      "from": "date",             // Required - Start time of closure
      "to": "date",               // Required - End time of closure
      "reason": "string"          // Optional - Reason for closure, default: "maintenance"
    }
    ```
  - Response: Empty object on success
  - Notes:
    - Creates bookings with `reserved` status that respect the place's business hours
    - Tables with `reserved` status can only be released using the `staffOpenTable` API
    - Returns 400 error if the table is already booked during any part of the requested period

### Table Relocation APIs
- **POST** `/Products/staff/tables/relocate/intent`
  - Express intent to relocate table(s) and retrieve available destination tables
  - Parameters:
    ```json
    {
      "from": {                   // Required - Source table information
        "type": "string",         // Required - Type of the source table (e.g., "table")
        "name": "string",         // Required - Name of the source table
        "placeId": "string",      // Required - ID of the place where the table is located
        "resourceId": "string",   // Required - ID of the source table
        "position": [             // Required - Position of the source table
          {
            "key": "string",
            "value": "string"
          }
        ]
      }
    }
    ```
  - Response:
    ```json
    {
      "sourceTables": [{            // Source tables with order information
        "resourceId": "string",
        "name": "string",
        "position": "object",
        "capacity": "number",
        "orders": "number",
        "hasActiveOrders": "boolean"
      }],
      "availableTables": [{         // Available destination tables
        "resourceId": "string",
        "name": "string",
        "position": "object",
        "capacity": "number",
        "suitabilityScore": "number",
        "isAdjacent": "boolean",
        "isCombined": "boolean",
        "totalCapacity": "number"
      }],
      "reservation": {              // Reservation details (when booking is enabled)
        "id": "string",
        "partySize": "number",
        "startTime": "date",
        "endTime": "date",
        "note": "string",
        "deposit": "object"
      },
      "suggestedCombinations": [{   // Suggested table combinations (when booking is enabled)
        "tables": ["string"],
        "totalCapacity": "number",
        "suitabilityScore": "number",
        "isAdjacent": "boolean"
      }]
    }
    ```

- **POST** `/Products/staff/tables/relocate/commit`
  - Execute table relocation with selected source and destination tables
  - Parameters:
    ```json
    {
      "sourceIds": ["string"],      // Required - IDs of tables to relocate from
      "destinationIds": ["string"],  // Required - IDs of tables to relocate to
      "reservationId": "string",    // Required when booking is enabled - ID of the reservation
    }
    ```
  - Response:
    ```json
    {
      "originalTables": [{          // Details of source tables
        "resourceId": "string",
        "name": "string",
        "position": ["object"]
      }],
      "newTables": [{               // Details of destination tables
        "resourceId": "string",
        "name": "string",
        "position": ["object"]
      }],
      "relocatedOrders": [{         // List of relocated orders
        "id": "string",
        "from": "string",
        "to": "string"
      }]
    }
    ```

## AppLink Management

- **POST** `/Products/applink/refresh`
  - Refresh product applinks
  - Parameters:
    ```json
    {
      "productIds": ["string"],   // Required - Products to refresh
      "force": "boolean",         // Optional - Force refresh
      "options": {                // Optional - Refresh options
        "platforms": ["string"],  // Specific platforms to refresh
        "immediate": "boolean"    // Skip queue
      }
    }
    ```
  - Response:
    ```json
    {
      "success": "boolean",
      "refreshed": ["string"],    // Successfully refreshed product IDs
      "failed": [{                // Failed refresh attempts
        "productId": "string",
        "error": "string"
      }],
      "jobId": "string"          // Background job ID if queued
    }
    ```

### Add to Bag Operations
- **POST** `/Products/applink/addtobag`
  - Create Add-to-Bag product promotion page
  - Parameters:
    ```json
    {
      "data": {
        "title": "string",        // Required - Page title
        "description": "string",  // Optional - Page description
        "products": [{            // Required - Products to include
          "productId": "string",
          "variantId": "string", // Optional - Specific variant
          "quantity": "number",  // Optional - Default quantity
          "options": "object"    // Optional - Product options
        }],
        "style": {               // Optional - Page styling
          "theme": "string",
          "colors": "object",
          "layout": "string"
        },
        "expiry": {             // Optional - Page expiration
          "date": "date",
          "action": "string"    // Action after expiry
        },
        "tracking": {           // Optional - Analytics tracking
          "campaign": "string",
          "source": "string",
          "medium": "string"
        }
      }
    }
    ```
  - Response:
    ```json
    {
      "micrositeId": "string",  // Generated microsite ID
      "url": "string",         // Public URL
      "shortUrl": "string",    // Shortened URL if requested
      "qrCode": "string",      // QR code URL if requested
      "analytics": {           // Analytics tracking info
        "pageId": "string",
        "trackingCode": "string"
      },
      "expiry": {             // Expiration details
        "expiresAt": "date",
        "status": "string"
      }
    }
    ```

- **POST** `/Products/applink/addtobag/:micrositeId/deploy`
  - Generate Add-to-Bag product promotion page
  - Parameters:
    - `micrositeId` (string, required): Microsite ID
    ```json
    {
      "productIds": ["string"],   // Required - Products to include
      "fallbackActionId": "string", // Optional - Action if products unavailable
      "start": "date",           // Optional - Start date
      "end": "date",             // Optional - End date
      "shorten": "boolean",      // Optional - Generate short URL
      "options": {               // Optional - Deployment options
        "generateQR": "boolean", // Generate QR code
        "customDomain": "string", // Use custom domain
        "password": "string"     // Password protect page
      }
    }
    ```
  - Response:
    ```json
    {
      "success": "boolean",
      "deployment": {
        "id": "string",
        "status": "string",
        "urls": {
          "page": "string",
          "short": "string",
          "qr": "string"
        },
        "analytics": {
          "pageId": "string",
          "trackingCode": "string"
        }
      }
    }
    ```

- **POST** `/Products/applink/addtobag/:micrositeId/undeploy`
  - Remove Add-to-Bag promotion page and assets
  - Parameters:
    - `micrositeId` (string, required): Microsite ID
    ```json
    {
      "cleanup": "boolean",     // Optional - Remove all assets
      "reason": "string"       // Optional - Undeploy reason
    }
    ```
  - Response:
    ```json
    {
      "success": "boolean",
      "undeployed": {
        "micrositeId": "string",
        "timestamp": "date",
        "cleanupStatus": "string"
      }
    }
    ```

- **DELETE** `/Products/applink/addtobag/:micrositeId`
  - Delete Add-to-Bag promotion page
  - Parameters:
    - `micrositeId` (string, required): Microsite ID
  - Response:
    ```json
    {
      "success": "boolean",
      "deleted": {
        "micrositeId": "string",
        "timestamp": "date"
      }
    }
    ```

## External Provider Integration

### Shopify Integration
- **POST** `/shopify/sync`
  - Sync products and variants with Shopify
  - Parameters:
    ```json
    {
      "shop": "string",
      "force": "boolean"
    }
    ```
  - Response: Object with sync status

### Grab Food Integration
- **GET** `/grab/food/merchant/menu`
  - Get GrabFood menu for outlet
  - Parameters:
    - `merchantID` (string, required): Store ID
  - Response:
    ```json
    {
      "currency": "string",
      "sellingTimes": ["object"],
      "categories": ["object"]
    }
    ```

### Grab Mart Integration
- **GET** `/grab/mart/merchant/menu`
  - Get GrabMart menu for outlet
  - Parameters:
    - `merchantID` (string, required): Store ID
  - Response:
    ```json
    {
      "currency": "string",
      "sellingTimes": ["object"],
      "categories": ["object"]
    }
    ```

### GrabFood/MartMenu Refresh
- **POST** `/grab/refresh/menu`
  - Notify Grab to refresh menu
  - Parameters:
    ```json
    {
      "provider": "string", // "grabfood" or "grabmart"
      "shop": "string" // Optional shop name
    }
    ```
  - Response: Object with refresh status

### UberEats Integration
- **POST** `/ubereats/sync/menu`
  - Push menu to UberEats
  - Parameters:
    ```json
    {
      "shop": "string",
      "force": "boolean"
    }
    ```
  - Response: Status of sync operation

- **POST** `/ubereats/app/order`
  - Transform UberEats order to app format
  - Parameters:
    ```json
    {
      "order": {}
    }
    ```
  - Response: Transformed order object


## Service Management

### Service Status
- **GET** `/Service/status`
  - Returns service status information
  - Response:
    ```json
    {
      "status": "string"
    }
    ```


## Validation and Error Responses

### Validation Rules
- Product validation:
  - Required fields: title, type
  - Valid price ranges
  - Unique SKU/GTIN
- Variant validation:
  - Valid product reference
  - Inventory policy consistency
  - Price point validation
- Order validation:
  - Valid product/variant references
  - Inventory availability
  - Payment method compatibility

### Error Responses
- Standardized error response format:
```json
{
  "error": {
    "statusCode": "number",   // HTTP status code
    "name": "string",         // Error type name
    "message": "string",      // Human readable error message
    "code": "string",         // Error code for client handling
    "details": "object"       // Additional error context
  }
}
```
