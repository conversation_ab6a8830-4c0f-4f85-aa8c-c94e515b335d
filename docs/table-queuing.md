# Table Queuing System

The Table Queuing System is part of the Product Service in the CRM platform that provides a streamlined approach to managing walk-in customers and reservations through an integrated self-service and staff-assisted system.

## Table of Contents
1. [Overview](#overview)
2. [Customer Entry Processing](#customer-entry-processing)
3. [Reservation Check-in Process](#reservation-check-in-process)
4. [Queue Management](#queue-management)
5. [Table Assignment](#table-assignment)
6. [Integration](#integrations)
    - [Event Bus Integration](#event-bus-integration)
    - [Booking Integration](#booking-integration)
    - [Table Relocation Integration](#table-relocation-integration)
7. [API Endpoints](#api-endpoints)
8. [Table-Specific Settings](#table-specific-settings)

For generic queue management and priority calculation, see [Resource Queuing](./resource-queuing.md).
For table booking and allocation strategies, see [Table Booking](./table-booking.md).

## Overview

The Table Queuing System provides an intelligent approach to managing walk-in customers and table assignments through a unified platform that handles both self-service and staff-assisted scenarios.

The system combines real-time table management, dynamic queue prioritization, and smart allocation strategies to optimize restaurant operations while maintaining customer satisfaction.

### System Architecture

```mermaid
flowchart TD
    subgraph "Customer Layer"
        A[Self-Service]
        B[Staff-Assisted]
    end

    subgraph "Application Layer"
        C[Entry Processing]
        D[Queue Management]
        E[Table Assignment]
    end

    subgraph "Data Layer"
        F[Queue Entries]
        G[Resources]
        H[Bookings]
        I[Customers]
    end

    A --> C
    B --> C
    C --> D
    D --> E

    C <--> F
    C <--> I
    D <--> F
    D <--> G
    E <--> G
    E <--> H
```

### Data Flow

```mermaid
sequenceDiagram
    actor Customer
    participant Entry Processor
    participant Queuing
    participant Table Assigner
    participant Resource
    participant Booking

    Customer->>Entry Processor: Check-in/Join Queue
    Entry Processor->>Booking: Check Reservation Status
    Booking-->>Entry Processor: Reservation Details

    alt Has Reservation
        Entry Processor->>Resource: Check Original Tables
        Resource-->>Entry Processor: Availability Status

        alt Original Tables Available
            Entry Processor->>Booking: Update to Checked-in
            Booking-->>Customer: Assigned Tables
        else Original Tables Unavailable
            Entry Processor->>Booking: Release Original Tables
            Entry Processor->>Resource: Find Alternative Tables
            Resource-->>Entry Processor: Alternative Tables

            alt Alternatives Available
                Entry Processor->>Booking: Create New Bookings
                Booking-->>Customer: Assigned Tables
            else No Tables Available
                Entry Processor->>Queuing: Join with Priority
                Queuing-->>Customer: Queue Position
            end
        end
    else No Reservation
        Entry Processor->>Resource: Check Immediate Availability
        Resource-->>Entry Processor: Availability Status

        alt Tables Available Now
            Entry Processor->>Table Assigner: Assign Tables
            Table Assigner->>Booking: Create Bookings
            Booking-->>Customer: Assigned Tables
        else No Tables Available
            Entry Processor->>Queuing: Join Regular Queue
            Queuing-->>Customer: Queue Position
        end
    end

    loop Queue Processing
        Queuing->>Resource: Check for Available Tables
        Resource-->>Queuing: Available Resources
        Queuing->>Table Assigner: Assign to Highest Priority
        Table Assigner->>Booking: Create Bookings
        Booking-->>Customer: Table Assignment
    end
```

The system operates through three main layers:

```mermaid
graph LR
    subgraph Customer Touchpoints
        A[Self Service]
        B[Staff Assisted]
    end

    subgraph Core Components
        C[Entry Processing]
        D[Queue Management]
        E[Table Assignment]
    end

    subgraph Resource Layer
        F[Resource Management]
    end

    A --> C
    B --> C
    C --> D
    D --> E
    E --> F
```

1. **Customer Touchpoints**: Handles customer entry through either self-service (Perkd App) or staff assistance
2. **Core Components**: Processes entries, manages queues, and handles table assignments using priority algorithms
3. **Resource Layer**: Manages physical table resources, combinations, and availability

### Key Features
- Integrated self-service and staff-assisted queue management
- Smart Release and Reassign approach for reservation check-ins
- Smart table combination handling for larger parties
- Real-time table allocation with capacity optimization
- Table-specific validation rules and constraints
- Dynamic wait time calculations and queue prioritization
- Elimination of "zombie resources" through intelligent table management
- Duplicate entry prevention with current queue status feedback

### Core Components

1. **Entry Processing**
   - Validates customer information and party size
   - Checks if customer is already in queue to prevent duplicates
   - Checks for existing reservations
   - Implements Smart Release and Reassign for customers with reservations
   - Determines immediate seating availability
   - Routes to appropriate queue or direct seating

2. **Queue Management**
   - Implements multi-tier priority system based on:
     * Customer membership status
     * Party size optimization
     * Wait time fairness
   - Provides real-time wait time estimates
   - Handles dynamic queue reordering based on table availability
   - For details, see [Queue Management](./resource-queuing.md)

3. **Table Assignment**
   - Smart table combination algorithms for larger parties
   - Capacity optimization to minimize unused seats
   - Real-time validation of table availability
   - Integration with booking system to prevent conflicts
   - For details, see [Resource Allocation](./resources.md#resource-allocation)

### Integration Points
- Perkd App for self-service queue management
- Table Booking System for reservation coordination
- Resource Management System for table tracking
- Customer Notification System for status updates


## Customer Entry Processing

```mermaid
graph TD
    A[Customer Entry] --> B{Entry Method?}
    B -->|Self-Service| C[NFC Tap]
    B -->|Staff-Assisted| D[Queue Widget]

    C --> E[Perkd App]
    E --> F{Has Reservation?}

    D --> L[Collect Party Size]
    F -->|No| L
    F -->|Yes| G{Original Tables Available?}

    G -->|Yes| Z[Seated at Original Tables]
    G -->|No| H[Release Original Tables]

    H --> I{Alternative Tables Available?}
    I -->|Yes| J[Seated at Alternative Tables]
    I -->|No| K[Priority Queue]

    L --> M[Walk-in Flow]
    M --> O{Immediate Seating Available?}
    O -->|Yes| Y[Table Assignment]
    O -->|No| P[Regular Queue]

    Y --> Z
    J --> Z
    K --> Z
    P --> Z
```

### Self-Service Entry (Perkd App Members)
- Tap NFC tag at restaurant entrance
- Perkd App automatically identifies member
- System checks for existing reservation
- If reservation exists:
  - System checks if original tables are available
  - If original tables available, customer is seated there
  - If original tables unavailable, system releases them and looks for alternatives
  - If alternatives available, customer is seated at alternative tables
  - If no tables available, customer enters priority queue
- If no reservation, enters walk-in flow with party size collection

### Staff-Assisted Entry (Walk-ins)
- Staff collects party size using Table Queue Widget
- Checks immediate seating availability
- Directs to table or queue as appropriate

## Reservation Check-in Process

The system implements a Smart Release and Reassign approach for handling customers with reservations at check-in:

```mermaid
graph TD
    A[Customer with Reservation Checks In] --> B{Original Tables Available?}
    B -->|Yes| C[Seat at Original Tables]
    B -->|No| D[Release Original Tables]
    D --> E[Search for Alternative Tables]
    E --> F{Alternatives Available?}
    F -->|Yes| G[Seat at Alternative Tables]
    F -->|No| H[Queue with High Priority]

    C --> I[Update Booking Status]
    G --> I
    H --> J[Wait in Priority Queue]
    J --> K[Table Becomes Available]
    K --> L[Seat Customer]
```

### Smart Release and Reassign Process
1. **Check Original Tables**: When a customer with a reservation checks in, the system first checks if their originally reserved tables are currently available.
   - For customers checking in within grace period, the system calculates an adjusted duration based on how late they are to check table availability.
2. **If Original Tables Available**: The customer is seated at their originally reserved tables, and the booking status is updated to reflect check-in.
3. **If Original Tables Unavailable**:
   - The original booking is canceled to release those tables back to the available pool
   - The system immediately searches for alternative tables that can accommodate the party
4. **If Alternative Tables Available**: The customer is seated at these alternative tables with new booking records created
5. **If No Tables Available**: The customer is placed in the queue with ASAP priority status

This approach ensures:
- No "zombie resources" where tables remain blocked by bookings but aren't being used
- Customers with reservations maintain priority status
- Overall table utilization is maximized
- The system remains efficient and straightforward

### Benefits for Customers
- Customers with reservations are either seated immediately or given highest priority in the queue
- Wait times are minimized through efficient table allocation
- The value of making a reservation is preserved

### Benefits for Operations
- Improved table utilization
- Reduced overall wait times
- Simplified queue management
- Clear, deterministic system behavior

### Implementation Details

The Smart Release and Reassign approach is implemented through several key components that work together to provide a seamless experience for customers with reservations.

#### 1. Table Availability Decision Flow

```mermaid
graph TD
    A[Check Table Availability] --> B{Scheduled Bookings?}
    B -->|Yes| C[Tables Unavailable]
    B -->|No| D{Overstaying Customers?}
    D -->|Yes| C
    D -->|No| E{Tables in Maintenance?}
    E -->|Yes| C
    E -->|No| F[Tables Available]

    subgraph "Availability Factors"
        G[Time Range] --> B
        H[Physical Status] --> E
        I[Booking Status] --> B
        J[Customer Presence] --> D
    end
```

The system performs several critical checks when determining table availability:

1. **Scheduled Bookings**: Checks if any tables are already booked for the requested time period
2. **Overstaying Customers**: Identifies tables that might be physically occupied by customers who haven't departed yet
3. **Customer's Own Bookings**: Excludes the customer's own bookings from the availability check to prevent false negatives
4. **Physical Availability**: Verifies that all tables exist and are in an available state (not in maintenance, etc.)

#### 2. Booking Lifecycle Management

```mermaid
stateDiagram-v2
    [*] --> OPEN: Create Reservation
    OPEN --> SUCCESS: Check-in
    OPEN --> CANCELED: Cancel

    state "Smart Release & Reassign" as SRR
    OPEN --> SRR: Customer Arrives
    SRR --> SUCCESS: Original Tables Available
    SRR --> CANCELED: Original Tables Unavailable
    SRR --> SUCCESS: Alternative Tables Available
    SRR --> QUEUED: No Tables Available

    QUEUED --> SUCCESS: Tables Become Available
    SUCCESS --> COMPLETED: Customer Departs
    CANCELED --> [*]
    COMPLETED --> [*]
```

The booking lifecycle during the Smart Release and Reassign process includes:

1. **Cancellation of Original Bookings**: When original tables are unavailable, the system cancels those bookings to release the tables back to the available pool
2. **Status Updates**: When tables are assigned, the system updates booking status to SUCCESS and sets arrivedAt timestamp
3. **Event Notifications**: The system emits events to notify other components about status changes
4. **Reservation History Tracking**: The original reservation ID is preserved for tracking and priority purposes

#### 3. Queue Enhancement for Reservation History

```mermaid
graph TD
    A[Customer with Reservation History] --> B[Join Queue]
    B --> C[Add Reservation Metadata]
    C --> D[Apply Priority Bonus]
    D --> E[Position in Queue]

    subgraph "Priority Calculation"
        F[Base Tier Priority] --> I[Total Priority Score]
        G[Wait Time Bonus] --> I
        H[Reservation Bonus] --> I
    end

    I --> E
```

When a customer with a reservation cannot be immediately seated, the queue system is enhanced to:

1. **Track Reservation History**: The system adds `hadReservation: true` and `originalReservationId` to the customer object
2. **Apply Priority Bonus**: Customers with reservation history receive additional priority points
3. **Preserve Context**: The original reservation details are maintained for reporting and analysis

#### 4. Complete Smart Release and Reassign Flow

```mermaid
flowchart TD
    A[Customer Check-in] --> B[Get Customer Info]
    B --> C[Retrieve Original Booking]
    C --> D{Booking Exists?}
    D -->|No| E[Return Waiting Status]
    D -->|Yes| F[Extract Party Size & Preferences]

    F --> G{Original Tables Available?}
    G -->|Yes| H[Update to Checked-in Status]
    H --> I[Return Assigned Status]

    G -->|No| J[Release Original Tables]
    J --> K[Search for Alternative Tables]
    K --> L{Alternatives Available?}

    L -->|Yes| M[Assign Alternative Tables]
    M --> N[Emit Reassigned Event]
    N --> O[Return Reassigned Status]

    L -->|No| P[Join Queue with Reservation History]
    P --> Q[Return Queued Status]
```

This flow diagram illustrates the complete Smart Release and Reassign process, from customer check-in through the decision points to the final outcomes.

#### 5. Benefits

The Smart Release and Reassign approach provides a straightforward solution to the zombie resource problem while maintaining the priority status for customers with reservations. It optimizes table utilization, simplifies the implementation, and improves the overall customer experience.

## Queue Management

The table queuing system extends the base resource queuing functionality with table-specific features and optimizations. It manages walk-in customers through an intelligent priority-based system that ensures fair and efficient table allocation.

```mermaid
graph TD
    A[Queue Entry] --> B[Priority Calculation]
    B --> C[Schedule Time]
    C --> D[Wait in Queue]
    D --> E{Resources Available?}
    E -->|Yes| F[Table Assignment]
    E -->|No| G[Update Priority]
    G --> D
    F --> H[Auto Check-in]
    H --> I[Customer Notification]
```

### Priority System
The system uses a multi-tier priority calculation that considers:
- Customer membership tier (VIP, Regular)
- Party size optimization
- Wait time fairness
- Source type (Walk-in, No-show, Overflow)
- Special requirements (Combined tables, Adjacent only)
- Reservation history (whether customer had a reservation)

#### Priority Calculation System

```mermaid
graph LR
    subgraph "Priority Components"
        A[Base Tier Priority] --> D[Total Priority]
        B[Wait Time Bonus] --> D
        C[Reservation History] --> D
    end

    subgraph "Base Tier Values"
        E[ASAP: 100]
        F[VIP: 10]
        G[Regular: 0]
    end

    subgraph "Wait Time Calculation"
        H[Minutes Waiting] --> I[factor × Minutes]
        I --> J{\> maxBonus?}
        J -->|Yes| K[maxBonus]
        J -->|No| L[Calculated Value]
    end

    subgraph "Reservation Bonus"
        M{Had Reservation?} -->|Yes| N[+10 Points]
        M -->|No| O[+0 Points]
    end
```

The priority calculation combines multiple factors to determine a customer's position in the queue:

- **Base Tier Priority**: ASAP (100), VIP (10), Regular (0)
- **Wait Time Bonus**: Increases linearly with waiting duration (minutes × factor), capped at maxBonus (default: 5)
- **Reservation History Bonus**: Customers who had reservations receive an additional 10 points

The combined score determines queue position and processing order, ensuring fair and efficient table allocation.

For more details, see [Priority](./resource-queuing.md#priority-system)

### Queue Processing
1. **Entry Creation**
   - Checks if customer is already in queue to prevent duplicates
   - Validates customer information and party size
   - Calculates initial priority
   - Determines scheduled time based on:
     * Current queue state
     * Table availability
     * Other waiting entries with higher priority

2. **Continuous Processing**
   - System runs periodic checks (every 1-5 seconds)
   - Updates priorities for all waiting entries
   - Processes highest priority entry when tables become available
   - Prevents race conditions through sequential processing
   - Automatically checks in customers when tables are immediately available

3. **Wait Time Management**
   - Dynamically calculates expected wait times
   - Considers current table occupancy
   - Accounts for future reservations
   - Updates as queue conditions change

### Immediate Seating Flow
When tables are immediately available for walk-in customers:
1. System assigns appropriate tables based on party size and preferences
2. Creates booking records for assigned tables
3. Automatically checks in the customer (status: SUCCESS)
4. Sets arrivedAt timestamp to current time
5. Notifies relevant systems of the assignment

### Integration Features
1. **Booking System Integration**
   - Coordinates with reservation system
   - Handles booking-related events:
     * `sales.booking.ended`
     * `sales.booking.deleted`
   - Triggers queue processing when tables become available

2. **Resource Management**
   - Real-time table availability tracking
   - Smart table combination handling
   - Capacity optimization
   - Operating hours compliance

### Queue Entry Lifecycle

```mermaid
stateDiagram-v2
    [*] --> waiting: Join Queue

    waiting --> assigned: Tables Available
    waiting --> left: Customer Leaves
    waiting --> skipped: Customer Bypassed

    assigned --> notified: Send Notification
    notified --> confirmed: Customer Accepts
    notified --> left: Customer Declines/Timeout

    confirmed --> completed: Service Finished

    completed --> [*]
    left --> [*]
    skipped --> [*]

    state waiting {
        [*] --> active
        active --> prioritized: Wait Time Increases
        prioritized --> active: Higher Priority Entry Arrives
    }

    state assigned {
        [*] --> pending
        pending --> locked: Tables Reserved
    }
```

| State | Description | Triggers | Next States |
|-------|-------------|----------|-------------|
| `waiting` | Initial queue entry state | Queue join, reservation release | `assigned`, `left`, `skipped` |
| `assigned` | Tables allocated | Resource availability | `notified` |
| `notified` | Customer informed | Notification sent | `confirmed`, `left` |
| `confirmed` | Customer accepted | Customer confirmation | `completed` |
| `completed` | Service finished | Customer departure | - |
| `left` | Customer left/abandoned | Timeout, explicit leave | - |
| `skipped` | Entry bypassed | Staff decision | - |

### Queue Entry Properties

Each queue entry contains the following key properties:

| Property | Description |
|----------|-------------|
| `kind` | Always 'table' for table queuing, indicates the resource type |
| `capacity` | Party size (number of people) |
| `tier` | Customer tier: 'regular', 'vip', or 'asap' |
| `priority` | Calculated priority score, higher is more important |
| `status` | Current state in the lifecycle (see above) |
| `scheduledAt` | Expected service time |
| `endTime` | Time after which the entry is considered abandoned |
| `hadReservation` | Whether this entry had a reservation that couldn't be fulfilled immediately |
| `originalReservationId` | The original reservation ID if this entry had a reservation |


## Table Assignment
The table assignment system extends the base resource allocation system with table-specific features.

Details in the core [resource allocation system](./resources.md#resource-allocation), including [scoring algorithms](./resources.md#scoring-system) and [availability management](./resources.md#validation-rules).

```mermaid
graph TD
    subgraph "Table Assignment Flow"
        A1[Queue Entry] --> B1[Get Party Size]
        B1 --> C1[Generate Candidates]
        C1 --> D1[Score Candidates]
        D1 --> E1[Filter Valid Options]
        E1 --> F1[Sort by Score]
        F1 --> G1[Try Assignment]
        G1 --> H1[Create Booking]
    end
```

**Note on Booking Creation**: The final step (`Create Booking`) involves creating the necessary `Booking` records in the Sales Service. If the table assignment involves combining multiple tables to accommodate the party, the system will generate multiple `Booking` records (one for each assigned table/resource). These records will all share the same `reservationId`, linking them back to the single queue entry or initial request.

### Table-Specific Features

1. **Section Management**
   - Tables are organized into sections
   - Combined tables must be in the same section
   - Section assignments affect server allocation

2. **Table Combinations**
   - Generated only for larger parties (>= `minSize`)
   - Uses physical layout for adjacency detection
   - Considers table shapes and arrangements
   - Optimizes for customer comfort

3. **Customer Experience**
   - Prioritizes single table assignments when possible
   - Considers table position and views
   - Optimizes for dining comfort and service efficiency
   - Maintains consistent section assignments

### Table-Specific Rules

1. **Layout Constraints**
   - Tables must be physically adjacent to combine
   - Maximum of `maxCombined` tables (default: 3)
   - All tables in combination must:
     * Share the same section
     * Share the same timezone
     * Be compatible for combining

2. **Service Optimization**
   - Balances section workload
   - Maintains server efficiency
   - Considers table proximity to service areas
   - Optimizes for kitchen workflow

For detailed information about scoring weights and validation rules, see [Resource Allocation](./resources.md#resource-allocation).

## Integrations

### System Integration Architecture

```mermaid
graph TD
    subgraph "Table Queuing System"
        TQ[Queue Manager]
        TA[Table Assignment]
        RM[Resource Manager]
    end

    subgraph "External Systems"
        BS[Booking System]
        NS[Notification System]
        PA[Perkd App]
        WS[Widget System]
    end

    subgraph "Event Bus"
        EB[Event Bus]
    end

    %% Subscribed Events
    BS -->|sales.booking.ended| EB
    BS -->|sales.booking.deleted| EB
    EB -->|sales.booking.ended| TQ
    EB -->|sales.booking.deleted| TQ

    %% Published Events
    TQ -->|product.resource.available| EB
    TA -->|booking.reservation.released| EB
    TA -->|booking.reservation.reassigned| EB

    %% Event Consumers
    EB -->|product.resource.available| BS
    EB -->|booking.reservation.released| BS
    EB -->|booking.reservation.reassigned| WS
    EB -->|queue.entry.updated| NS
    EB -->|queue.entry.assigned| PA

    %% Direct Integrations
    TQ <--> RM
    TA <--> RM
    TQ <--> TA
```

### Event Bus Integration
The table queuing system integrates with other services through the following events:

#### Subscribed Events
| Event | Source | Description | Action |
|-------|--------|-------------|--------|
| `sales.booking.ended` | Booking System | Triggered when a booking is completed | Process queue for newly available tables |
| `sales.booking.deleted` | Booking System | Triggered when a booking is canceled | Update queue and resource availability |
| `sales.booking.reservation.confirmed` | Booking System | Triggered when a reservation is confirmed | Update widget data for confirmed bookings |

#### Published Events
| Event | Target | Description | Payload |
|-------|--------|-------------|--------|
| `product.resource.available` | Booking System | Published when a table becomes available | `{ resourceId, placeId, kind, capacity }` |
| `booking.reservation.released` | Booking System | Emitted when original tables are released | `{ reservationId, reason: 'TABLE_REASSIGNMENT', bookings }` |
| `booking.reservation.reassigned` | Widget System | Emitted when customer is seated at alternative tables | `{ reservationId, originalBookings, newBookings }` |
| `queue.entry.updated` | Notification System | Emitted when queue entry status changes | `{ queueId, status, estimatedWait }` |
| `queue.entry.assigned` | Perkd App | Emitted when tables are assigned to queue entry | `{ queueId, resources, customer }` |

### Booking Integration

The table queuing system integrates with the table booking system to ensure coordinated management of walk-in queues and table reservations. This integration prevents conflicts between queued customers and booked tables while maximizing table utilization.

1. **Booking Availability Check**
   - Queries booking system for table availability before queue processing
   - Considers existing reservations within the `minDuration` window
   - Respects `leadTime` requirements for new bookings
   - Validates against venue's `operatingHours`

2. **Queue to Booking Conversion**
   - Automatically converts queue entries to bookings when tables become available
   - Inherits booking constraints from `table` settings:
     * Uses `minDuration` for reservation length
     * Applies `minPartySize` and `maxPartySize` validations
     * Follows table combination rules via `maxCombined`
   - Maintains booking state through the standard booking lifecycle

3. **Booking State Integration**
   - Monitors booking status changes via event subscriptions:
     * Processes cancellations to free up tables for queued customers
     * Handles no-shows based on configured grace period
     * Updates queue when bookings end earlier than expected
     * Handles reservation confirmations via `sales.booking.reservation.confirmed` event
     * Updates widget data for confirmed bookings
   - Prevents double allocation of tables between bookings and queue
   - Skips widget data updates for bookings with source 'queuing'

4. **Table Availability Management**
   - Synchronizes with booking system's table inventory
   - Considers both immediate and future availability
   - Respects booking blocks and maintenance periods
   - Handles table combinations consistently across systems

Key Integration Points:
- Booking system is the source of truth for table availability
- Queue entries convert to confirmed bookings when tables are assigned
- Booking lifecycle events trigger queue processing for newly available tables
- Table combination rules are consistently applied across both systems
- All operations honor venue timezone and operating hours

For queue management and priority settings, refer to [Resource Queuing](./resource-queuing.md).

### Table Relocation Integration

The table queuing system integrates with the Table Relocation feature to provide a complete table management solution:

```mermaid
graph TD
    A[Table Queuing System] -->|Queue Status| B[Table Relocation]
    B -->|Table Availability| A

    subgraph "Shared Components"
        C[Resource Management]
        D[Order Management]
    end

    A <--> C
    B <--> C
    B <--> D
```

1. **Queue Status Awareness**
   - Table Relocation considers current queue status when suggesting destination tables
   - Prevents relocating to tables that might be needed for high-priority queued customers
   - Maintains queue integrity during table relocations

2. **Resource Coordination**
   - Both systems share the same resource management layer
   - Ensures consistent table availability information
   - Prevents conflicts between queued customers and table relocations

3. **Manual Override Support**
   - Staff can override queue assignments for urgent relocations
   - Queue system adapts to table changes made through relocation
   - Provides flexibility while maintaining system integrity

For detailed implementation, see [Table Relocation](./table-relocation.md).

## API Endpoints
See [API Documentation](./API.md) for details.

#### Table Queuing Widget APIs
- `POST /Products/app/queuing/tables` - Queue for table for member
- `POST /Products/app/queuing/checkin` - Check-in for tables with Smart Release and Reassign
- `DELETE /Products/app/queuing/tables/:id` - Cancel queue for tables

### Queue-only for Tables API
`POST /Products/app/queuing/tables`

Handles queue requests for members without reservations.

**Parameters:**
- `cardId` (string, required): Customer's card ID
- `placeId` (string, required): Place ID
- `partySize` (number, required): Number of people in the party
- `allowCombined` (boolean, optional, default: true): Whether to allow combined tables
- `adjacentOnly` (boolean, optional, default: true): Whether tables must be adjacent

**Returns:**
- If customer is already in queue: `{ status: [current status], queueId, scheduledAt, endTime, partySize, placeId, existing: true }`
- If tables immediately available: `{ kind: 'table', status: 'assigned', resources, placeId }`
- If no tables available: `{ kind: 'table', status: 'waiting', queueId, scheduledAt, endTime, placeId }`

**Behavior:**
1. First checks if customer is already in the queue:
   - If found, returns their current queue status with `existing: true` flag
   - The `existing: true` flag indicates this is an existing queue entry, not a new one
   - This prevents duplicate queue entries for the same customer
2. If tables are immediately available:
   - Assigns tables to the customer
   - Returns assigned tables information
3. If no tables are available:
   - Adds customer to the queue
   - Returns queue position and estimated wait time

**Response Fields:**
- `kind`: Always 'table' for this endpoint, indicates the type of resource being queued for
- `status`: Current status of the queue entry or table assignment
- `existing`: When true, indicates the customer already has an active queue entry
- `partySize`: Number of people in the party (included in existing queue responses)

### Cancel Queue for Tables API
`DELETE /Products/app/queuing/tables/:id`

Allows customers to cancel their position in the queue.

**Parameters:**
- `id` (string, required): Queue ID

**Returns:**
- `true` if successfully canceled
- `false` if queue entry not found

### Check-in for Tables API
`POST /Products/app/queuing/checkin`

Handles check-in for customers with reservations, implementing the Smart Release and Reassign approach.

**Parameters:**
- `cardId` (string, required): Customer's card ID
- `placeId` (string, required): Place ID
- `reservationId` (string, optional): Reservation ID

**Returns:**
- If customer is already in queue: `{ kind: 'table', status: [current status], queueId, scheduledAt, endTime, placeId, existing: true }`
- If original tables available: `{ kind: 'table', status: 'assigned', resources, bookings }`
- If alternative tables available: `{ kind: 'table', status: 'reassigned', resources, originalBookings }`
- If no tables available: `{ kind: 'table', status: 'queued', queueId, scheduledAt, endTime, placeId, originalBookings }`
- If no reservation found: `{ kind: 'table', status: 'none' }`
- If customer is too early: `{ kind: 'table', status: 'early' }` (when arriving before earlyCheckinTime window)

**Behavior:**
1. First checks if customer is already in the queue:
   - If found, returns their current queue status with `existing: true` flag
   - The `existing: true` flag indicates this is an existing queue entry, not a new one
   - This prevents duplicate queue entries for the same customer
2. If customer has a reservation:
   - Validates timing: checks if customer is within acceptable check-in window (not too early, not too late)
   - If too late (past grace period), automatically queues customer with reservation history
   - If too early (before earlyCheckinTime window), returns early status
   - Checks if original tables are available using adjusted duration for late arrivals
   - If available, seats customer at original tables
   - If unavailable, releases original tables and looks for alternatives
   - If alternatives available, seats customer at alternative tables
   - If no tables available, places customer in priority queue with reservation history
3. If customer has no reservation:
   - Returns `{ kind: 'table', status: 'none' }`

**Response Fields:**
- `kind`: Always `table` for this endpoint, indicates the type of resource being queued for
- `status`: Current status of the queue entry or table assignment
   - `'assigned'`: Customer is seated at their original reserved tables
   - `'reassigned'`: Customer is seated at alternative tables
   - `'queued'`: Customer is placed in the queue with priority status
   - `'none'`: No reservation found for the customer
   - `'early'`: Customer arrived too early (before earlyCheckinTime window)
- `existing`: When true, indicates the customer already has an active queue entry
- `originalBookings`: Original booking records when tables are reassigned or customer is queued


## Table-Specific Settings
Configured under the `table` key in the `queuing` settings according to [Queue Settings](./resource-queuing.md#queue-settings).

| Setting | Value | Description |
|---------|-------|-------------|
| `holdTime` | 5 minutes | Time for customer to confirm a table assignment before it's released |
| `validity` | 3 hours | How long queue entries remain valid before expiring |
| `duration.default` | 90 minutes | Standard expected dining duration |
| `duration.peak` | 75 minutes | Expected dining duration during peak hours |
| `duration.offPeak` | 120 minutes | Expected dining duration during off-peak hours |
| `priority.asap` | 100 | Base score for immediate seating |
| `priority.vip` | 10 | Base score for VIP customers |
| `priority.regular` | 0 | Base score for regular customers |
| `priority.waitingTime.factor` | 1 | Multiplier applied to waiting time (in minutes) |
| `priority.waitingTime.maxBonus` | 5 | Maximum bonus points from waiting time |
| `priority.reservationBonus` | 10 | Bonus points for customers who had reservations |

These settings control the behavior of the table queuing system, including:

1. **Queue Management**
   - `holdTime`: How long a customer has to confirm a table assignment before it's released
   - `validity`: How long queue entries remain valid before expiring

2. **Resource Allocation**
   - `duration`: Expected dining durations for different time periods
   - These values affect capacity planning and wait time estimates

3. **Priority Calculation**
   - Base scores for different customer tiers
   - Wait time bonus calculation parameters
   - Reservation history bonus points

These settings can be adjusted to optimize the system for different business requirements and customer service goals.
