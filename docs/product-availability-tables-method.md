# Product.availabilityTables() Method Documentation

## Table of Contents
1. [Method Overview](#method-overview)
2. [Parameters](#parameters)
3. [Return Value](#return-value)
4. [Detailed Process Flow](#detailed-process-flow)
5. [Business Logic](#business-logic)
6. [Data Flow](#data-flow)
7. [Edge Cases](#edge-cases)
8. [Integration Points](#integration-points)
9. [<PERSON>rro<PERSON>ling](#error-handling)
10. [Code Examples](#code-examples)
11. [Performance Considerations](#performance-considerations)

## Method Overview

The `Product.availabilityTables()` method is the core function responsible for generating comprehensive table availability data for restaurant booking systems. It provides a detailed availability matrix organized by calendar dates, time slots, and party size ranges, enabling both widget and website booking interfaces to display real-time table availability.

### Purpose
- Generate real-time table availability data for a specific restaurant location
- Provide structured availability information for different party sizes and time slots
- Support both member (widget) and non-member (website) booking interfaces
- Enable intelligent table allocation decisions based on business rules and constraints

### Usage Context
This method is primarily used by:
- **Widget API** (`availableTablesWidget`): For member bookings through the Perkd app
- **Website API** (`availabilityTablesWebsite`): For non-member bookings through restaurant websites
- **Internal systems**: For availability checks and booking validation

## Parameters

### placeId (String, Required)
The unique identifier for the restaurant/place location.

**Validation:**
- Must be a valid Place ID that exists in the system
- The place must have `dinein.available` set to `true`
- The place must have at least one table resource configured

**Example:**
```javascript
const placeId = "507f1f77bcf86cd799439011";
```

## Return Value

Returns a comprehensive availability object with the following structure:

```javascript
{
  placeId: String,           // The requested place ID
  lastUpdated: String,       // ISO timestamp of when data was generated
  dateRange: {
    start: String,           // Start date (YYYY-MM-DD)
    end: String             // End date (YYYY-MM-DD)
  },
  config: {
    operatingHours: Object,  // Aggregated business hours
    timeSlotDuration: Number, // Duration of each time slot in minutes
    maxPartySize: Number,    // Maximum allowed party size
    minPartySize: Number,    // Minimum allowed party size
    leadTimeMinutes: Number, // Required advance notice in minutes
    maxAdvanceBookingDays: Number, // Maximum days in advance for booking
    partySizeRanges: Array,  // Party size categories
    cancelLeadTime: Number   // Cancellation lead time in minutes
  },
  availability: Array       // Availability matrix (see below)
}
```

### Availability Matrix Structure

Each element in the `availability` array represents a calendar date:

```javascript
{
  date: String,            // Calendar date (YYYY-MM-DD)
  dayOfWeek: String,       // Day name (e.g., "Monday")
  isToday: Boolean,        // Whether this is today's date
  timeSlots: Array         // Available time slots for this date
}
```

Each time slot contains:

```javascript
{
  time: String,                    // Time in HH:mm format
  timestamp: String,               // ISO timestamp
  partySizeAvailability: Object,   // Availability levels by party size range
  exactAvailability: Object        // Exact table counts by party size range
}
```

### Availability Levels
- `NONE` (0): No tables available
- `LOW` (1): 1-2 tables available
- `MEDIUM` (2): 3-5 tables available
- `HIGH` (3): 6+ tables available

## Detailed Process Flow

The `Product.availabilityTables()` method follows a complex execution sequence with multiple database queries and data processing steps. The following diagrams illustrate the process flow and highlight potential optimization opportunities.

### High-Level Process Sequence

```mermaid
sequenceDiagram
    participant Client
    participant Product as Product.availabilityTables()
    participant Place as Place Model
    participant Resource as Resource Model
    participant Booking as Booking Model
    participant Utils as @perkd/utils

    Client->>Product: availabilityTables(placeId)

    Note over Product: Phase 1: Validation & Setup
    Product->>Product: Validate settings (enabled)
    Product->>Place: findById(placeId)
    Place-->>Product: place data + dinein config
    Product->>Product: Validate dinein.available

    Note over Product: Phase 2: Resource Discovery
    Product->>Resource: find({ placeId, kind: TABLE })
    Resource-->>Product: table resources[]
    Product->>Utils: aggregateHours(resources.hours)
    Utils-->>Product: businessHours

    Note over Product: Phase 3: Time Slot Generation
    Product->>Product: createHelperFunctions()
    Product->>Product: generateTimeSlots() for each day
    Product->>Product: filter & deduplicate slots

    Note over Product: Phase 4: Occupancy Analysis
    Product->>Booking: occupied(tableIds, startDate, endDate)
    Booking-->>Product: occupancy data
    Product->>Product: organize tables by capacity

    Note over Product: Phase 5: Availability Calculation
    loop For each time slot
        loop For each party size range
            Product->>Product: findAvailableTables()
            Product->>Product: calculate availability level
        end
    end

    Note over Product: Phase 6: Response Construction
    Product->>Product: buildAvailabilityByCalendarDate()
    Product-->>Client: availability matrix
```

### Detailed Data Flow with Optimization Opportunities

```mermaid
flowchart TD
    A[Start: placeId] --> B{Settings Enabled?}
    B -->|No| B1[❌ Throw NOT_ENABLED]
    B -->|Yes| C[🔍 Query Place by ID]

    C --> D{Place Exists?}
    D -->|No| D1[❌ Throw PLACE_NOT_FOUND]
    D -->|Yes| E{Dinein Available?}
    E -->|No| E1[❌ Throw DINEIN_NOT_ENABLED]

    E -->|Yes| F[🔍 Query All Table Resources]
    F --> G{Tables Found?}
    G -->|No| G1[❌ Throw No Tables Found]

    G -->|Yes| H[⚡ Aggregate Business Hours]
    H --> I[🏗️ Create Helper Functions]
    I --> J[📅 Generate Business Days Array]

    J --> K[🕐 Generate Time Slots]
    K --> L[🔄 Filter & Deduplicate Slots]
    L --> M[🔍 Query Booking Occupancy]

    M --> N[📊 Organize Tables by Capacity]
    N --> O[🔄 Process Each Time Slot]

    O --> P[🔄 Process Each Party Size]
    P --> Q[🎯 Find Available Tables]
    Q --> R[📈 Calculate Availability Level]
    R --> S{More Party Sizes?}
    S -->|Yes| P
    S -->|No| T{More Time Slots?}
    T -->|Yes| O
    T -->|No| U[🏗️ Build Response Matrix]
    U --> V[✅ Return Availability Data]

    style B fill:#f57f17,color:#fff
    style C fill:#e65100,color:#fff
    style F fill:#e65100,color:#fff
    style M fill:#e65100,color:#fff
    style O fill:#c62828,color:#fff
    style P fill:#c62828,color:#fff
    style Q fill:#ad1457,color:#fff
```

### Performance Bottlenecks and Optimization Opportunities

```mermaid
graph LR
    subgraph "🔴 High Impact Bottlenecks"
        A1[Database Queries<br/>3 separate queries]
        A2[Nested Loops<br/>Slots × Party Sizes]
        A3[Table Availability<br/>Complex algorithm]
    end

    subgraph "🟡 Medium Impact Areas"
        B1[Business Hours<br/>Aggregation]
        B2[Time Slot Generation<br/>Multiple days]
        B3[Data Transformation<br/>Response building]
    end

    subgraph "🟢 Optimization Strategies"
        C1[Query Batching<br/>Combine DB calls]
        C2[Caching Layer<br/>Business hours & resources]
        C3[Parallel Processing<br/>Independent calculations]
        C4[Lazy Evaluation<br/>On-demand slot generation]
        C5[Response Streaming<br/>Progressive data delivery]
    end

    A1 --> C1
    A1 --> C2
    A2 --> C3
    A2 --> C4
    A3 --> C2
    B1 --> C2
    B2 --> C4
    B3 --> C5
```

### 1. Initial Validation and Setup
```javascript
// Validate table booking is enabled
if (!enabled) throw new Error('NOT_ENABLED', { statusCode: 403 })

// Validate place exists and dinein is enabled
const store = await Place.findById(placeId)
if (!dinein.available) throw new Error('DINEIN_NOT_ENABLED', { statusCode: 403 })
```

### 2. Resource Discovery and Business Hours Aggregation
```javascript
// Get all table resources for the place
const resources = await Resource.find({
  where: { placeId, kind: TABLE }
})

// Aggregate operating hours from all table resources
const businessHours = aggregateHours(hoursArray)
```

### 3. Time Slot Generation
The method generates time slots by:
- Creating business days array based on `maxAdvanceBooking` setting
- Considering overnight operations (slots from previous day)
- Filtering slots based on lead time requirements
- Removing duplicates and sorting chronologically

### 4. Table Occupancy Analysis
```javascript
const { tablesByCapacity, tableOccupancy } = await getTablesData({
  Resource, Booking, placeId, dates: occupancyCheckDates, now
})
```

### 5. Availability Matrix Construction
For each time slot and party size range:
- Find available tables using `findAvailableTables()`
- Calculate availability level based on table count
- Build structured response with exact counts and availability levels

## Business Logic

### Party Size Categorization
The method groups party sizes into predefined ranges:
- **1-2 people**: Small parties
- **3-4 people**: Medium parties
- **5-8 people**: Large parties
- **9+ people**: Extra large parties

### Table Allocation Algorithm

```mermaid
flowchart TD
    A[Party Size Request] --> B[Apply maxCapacity Constraint]
    B --> C[Calculate Max Allowed Capacity]
    C --> D[Search Single Tables]

    D --> E{Single Tables<br/>Available?}
    E -->|Yes| F[✅ Return Single Tables<br/>Optimal Solution]

    E -->|No| G{allowCombined<br/>Enabled?}
    G -->|No| H[❌ No Tables Available]

    G -->|Yes| I[Find Smaller Available Tables]
    I --> J{Smaller Tables<br/>Found?}
    J -->|No| H

    J -->|Yes| K[Sort by Capacity<br/>Largest First]
    K --> L[Generate Combinations<br/>Up to maxCombined]

    L --> M[Check Table Adjacency]
    M --> N{Adjacent Tables<br/>Required?}
    N -->|Yes| O[Filter Adjacent Only]
    N -->|No| P[All Combinations Valid]

    O --> Q{Valid Adjacent<br/>Combinations?}
    Q -->|No| H
    Q -->|Yes| R[Sort by Optimal Fit]

    P --> R
    R --> S[✅ Return Best Combination]

    style F fill:#2e7d32,color:#fff
    style S fill:#2e7d32,color:#fff
    style H fill:#c62828,color:#fff
```

### Table Allocation Rules
1. **Single Table Priority**: Always prefer single tables over combinations
2. **Capacity Constraints**: Respect `maxCapacity` settings for optimal utilization
3. **Combined Tables**: Allow table combinations up to `maxCombined` limit
4. **Adjacency Requirements**: Ensure combined tables are adjacent when required

### Time Slot Generation Process

```mermaid
sequenceDiagram
    participant TG as Time Slot Generator
    participant BH as Business Hours
    participant TS as Time Slots
    participant Filter as Slot Filter

    Note over TG: For each business day
    TG->>BH: Get operating periods for day
    BH-->>TG: Regular + specific date periods

    loop For each operating period
        TG->>TG: Parse open/close times
        TG->>TG: Handle overnight periods
        TG->>TS: Generate slots at intervals
        TS->>TS: Apply minimum duration check
        TS->>TS: Apply lead time constraint
        TS-->>TG: Valid slots for period
    end

    TG->>Filter: All generated slots
    Filter->>Filter: Remove duplicates
    Filter->>Filter: Sort chronologically
    Filter->>Filter: Filter by date range
    Filter->>Filter: Apply lead time from now
    Filter-->>TG: Final slot list
```

### Time Slot Filtering
- **Lead Time**: Exclude slots within the required advance notice period
- **Business Hours**: Only include slots during operating hours
- **Advance Booking**: Limit slots to maximum advance booking window
- **Minimum Duration**: Ensure bookings can complete before closing time

## Data Flow

### Data Structure Relationships

```mermaid
erDiagram
    PLACE ||--o{ RESOURCE : contains
    PLACE ||--|| SETTINGS : has
    RESOURCE ||--o{ BOOKING : "booked for"
    RESOURCE ||--|| BUSINESS_HOURS : "operates during"

    PLACE {
        string id PK
        object dinein
        object settingList
        string name
        object addressList
    }

    RESOURCE {
        string id PK
        string placeId FK
        string kind "TABLE"
        number capacity
        object hours
        array adjacentIds
        object position
    }

    BOOKING {
        string id PK
        string resourceId FK
        string placeId FK
        datetime startTime
        datetime endTime
        string status
        number quantity
    }

    SETTINGS {
        boolean enabled
        number minPartySize
        number maxPartySize
        number minDuration
        number leadTime
        number maxAdvanceBooking
        number timeSlotInterval
        object maxCapacity
    }

    BUSINESS_HOURS {
        array periods
        array specific
        object open
        object close
    }
```

### Data Transformation Flow

```mermaid
flowchart LR
    subgraph "📥 Input Data"
        A1[Place Config]
        A2[Table Resources]
        A3[Booking Records]
        A4[System Settings]
    end

    subgraph "🔄 Processing Stages"
        B1[Validation Layer]
        B2[Resource Aggregation]
        B3[Time Slot Generation]
        B4[Occupancy Analysis]
        B5[Availability Calculation]
    end

    subgraph "📤 Output Structure"
        C1[Availability Matrix]
        C2[Configuration Data]
        C3[Date Range Info]
    end

    A1 --> B1
    A2 --> B2
    A3 --> B4
    A4 --> B1

    B1 --> B2
    B2 --> B3
    B3 --> B4
    B4 --> B5

    B5 --> C1
    B2 --> C2
    B3 --> C3

    style B1 fill:#f57f17,color:#fff
    style B4 fill:#e65100,color:#fff
    style B5 fill:#c62828,color:#fff
```

### Input Dependencies
1. **Place Configuration**: Restaurant settings and dinein availability
2. **Resource Data**: Table configurations, capacities, and operating hours
3. **Booking Data**: Existing reservations and occupancy information
4. **System Settings**: Booking rules, time zones, and business constraints

### Processing Pipeline
```
Place Validation → Resource Discovery → Hours Aggregation →
Time Slot Generation → Occupancy Analysis → Availability Calculation →
Response Construction
```

### External Dependencies
- **Place Model**: Restaurant configuration and settings
- **Resource Model**: Table definitions and business hours
- **Booking Model**: Existing reservations and occupancy data
- **Settings System**: Global and place-specific booking rules
- **Timezone Utilities**: Date/time calculations and formatting

## Edge Cases

### Invalid Place ID
```javascript
// Throws PLACE_NOT_FOUND error with 404 status
const store = await Place.findById(placeId).catch(error => {
  throw new Error(`PLACE_NOT_FOUND: ${error.message}`, { statusCode: 404 })
})
```

### No Tables Available
```javascript
if (!resources.length) {
  throw new Error(`No active tables found for place: ${placeId}`)
}
```

### Dinein Disabled
```javascript
if (!dinein.available) {
  throw new Error('DINEIN_NOT_ENABLED', { statusCode: 403 })
}
```

### Empty Business Hours
When no operating hours are configured, the method returns an empty availability matrix with proper structure but no time slots.

### Timezone Edge Cases
- **Overnight Operations**: Handles restaurants open past midnight
- **DST Transitions**: Properly manages daylight saving time changes
- **Cross-Date Slots**: Manages time slots that span multiple calendar dates

## Integration Points

### Table Booking System
- **Widget API**: Direct integration for member bookings
- **Website API**: Integration for non-member bookings with additional validation
- **Booking Validation**: Used to verify availability before creating reservations

### Resource Management
- **Table Configuration**: Reads table capacities, positions, and adjacency data
- **Business Hours**: Integrates with operating hours from multiple resources
- **Occupancy Tracking**: Coordinates with booking system for real-time availability

### Settings System
- **Global Settings**: Booking rules, timezone, and system constraints
- **Place Settings**: Restaurant-specific configurations and overrides
- **Dynamic Configuration**: Supports runtime configuration changes

### Queue System Integration
- **Availability Monitoring**: Provides data for queue processing decisions
- **Priority Calculation**: Availability levels influence queue priority
- **Resource Allocation**: Coordinates with queue system for table assignments

## Error Handling

### System Errors
- **Database Connectivity**: Graceful handling of database connection issues
- **Model Validation**: Proper error propagation from model operations
- **Resource Conflicts**: Detection and handling of concurrent booking attempts

### Business Rule Violations
- **Configuration Errors**: Clear error messages for misconfigured settings
- **Validation Failures**: Detailed feedback for invalid requests
- **Constraint Violations**: Specific errors for business rule violations

### Recovery Mechanisms
- **Fallback Responses**: Minimal valid responses when partial data is available
- **Error Logging**: Comprehensive logging for debugging and monitoring
- **Graceful Degradation**: Continued operation with reduced functionality when possible

## Code Examples

### Basic Usage
```javascript
// Get availability for a restaurant
const availability = await Product.availabilityTables('507f1f77bcf86cd799439011');

console.log('Available dates:', availability.dateRange);
console.log('Configuration:', availability.config);
console.log('Today\'s slots:', availability.availability.find(day => day.isToday)?.timeSlots);
```

### Processing Availability Data
```javascript
// Find high availability slots for large parties
const highAvailabilitySlots = availability.availability
  .flatMap(day => day.timeSlots)
  .filter(slot => slot.partySizeAvailability['5-8'] === 'HIGH');

// Check exact table counts
const exactCounts = availability.availability[0].timeSlots[0].exactAvailability;
console.log('Tables available for 3-4 people:', exactCounts['3-4']);
```

### Error Handling Example
```javascript
try {
  const availability = await Product.availabilityTables(placeId);
  return availability;
} catch (error) {
  switch (error.message) {
    case 'NOT_ENABLED':
      return { error: 'Table booking is currently disabled' };
    case 'DINEIN_NOT_ENABLED':
      return { error: 'Dine-in service is not available' };
    default:
      console.error('Availability check failed:', error);
      return { error: 'Unable to check availability' };
  }
}
```

### Integration with Booking Flow
```javascript
// Check availability before attempting booking
const availability = await Product.availabilityTables(placeId);
const targetSlot = availability.availability
  .find(day => day.date === requestedDate)
  ?.timeSlots.find(slot => slot.time === requestedTime);

if (targetSlot?.partySizeAvailability[partySizeRange] !== 'NONE') {
  // Proceed with booking attempt
  const booking = await Product.requestTableBookings(customer, placeId, partySize, from, preferences, options);
} else {
  // No availability - suggest alternatives or queue
  const alternatives = findAlternativeSlots(availability, partySize);
}
```

## Performance Considerations

### Performance Analysis and Optimization Opportunities

```mermaid
graph TB
    subgraph "🔍 Performance Analysis"
        A1[Method Execution Time]
        A2[Database Query Count]
        A3[Memory Usage Pattern]
        A4[CPU Intensive Operations]
    end

    subgraph "⚠️ Current Bottlenecks"
        B1["🔴 3 Sequential DB Queries<br/>Place → Resources → Bookings"]
        B2["🔴 Nested Loops<br/>Days × Slots × Party Sizes"]
        B3["🟡 Business Hours Aggregation<br/>JSON parsing & merging"]
        B4["🟡 Table Combination Algorithm<br/>Recursive generation"]
    end

    subgraph "🚀 Optimization Strategies"
        C1["📊 Query Optimization<br/>• Batch queries<br/>• Use indexes<br/>• Parallel execution"]
        C2["💾 Caching Layer<br/>• Resource data (1hr TTL)<br/>• Business hours (24hr TTL)<br/>• Availability matrix (5min TTL)"]
        C3["⚡ Algorithm Optimization<br/>• Early termination<br/>• Lazy evaluation<br/>• Memoization"]
        C4["🔄 Async Processing<br/>• Promise.all for parallel ops<br/>• Worker threads for CPU tasks<br/>• Streaming responses"]
    end

    subgraph "📈 Expected Improvements"
        D1["Response Time: 60-80% reduction"]
        D2["Database Load: 50% reduction"]
        D3["Memory Usage: 30% reduction"]
        D4["Throughput: 3x increase"]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B3
    A4 --> B2

    B1 --> C1
    B1 --> C4
    B2 --> C3
    B2 --> C4
    B3 --> C2
    B4 --> C3

    C1 --> D1
    C1 --> D2
    C2 --> D1
    C2 --> D3
    C3 --> D1
    C4 --> D4

    style B1 fill:#c62828,color:#fff
    style B2 fill:#c62828,color:#fff
    style B3 fill:#ef6c00,color:#fff
    style B4 fill:#ef6c00,color:#fff
    style C1 fill:#2e7d32,color:#fff
    style C2 fill:#2e7d32,color:#fff
    style C3 fill:#2e7d32,color:#fff
    style C4 fill:#2e7d32,color:#fff
```

### Specific Optimization Recommendations

#### 1. Database Query Optimization
```javascript
// Current: 3 sequential queries
const place = await Place.findById(placeId);
const resources = await Resource.find({ where: { placeId, kind: TABLE }});
const occupancy = await Booking.occupied(tableIds, startDate, endDate);

// Optimized: Parallel execution with Promise.all
const [place, resources, occupancy] = await Promise.all([
  Place.findById(placeId),
  Resource.find({ where: { placeId, kind: TABLE }}),
  // Defer occupancy query until we have tableIds
]);
```

#### 2. Caching Strategy Implementation
```javascript
// Cache business hours aggregation (expensive operation)
const cacheKey = `business-hours:${placeId}`;
let businessHours = await cache.get(cacheKey);
if (!businessHours) {
  businessHours = aggregateHours(hoursArray);
  await cache.set(cacheKey, businessHours, 3600); // 1 hour TTL
}
```

#### 3. Algorithm Optimization
```javascript
// Early termination for availability calculation
for (const slot of timeSlots) {
  for (const range of partySizeRanges) {
    const availableTables = findAvailableTables(representativeSize, slot);

    // Early termination: if no tables available, skip detailed calculation
    if (availableTables.length === 0) {
      partySizeAvailability[range.label] = NONE;
      continue;
    }

    // Only calculate detailed availability if tables exist
    const level = calculateAvailabilityLevel(availableTables.length);
    partySizeAvailability[range.label] = level;
  }
}
```

### Optimization Strategies
1. **Caching**: Consider implementing response caching for frequently requested places
2. **Database Queries**: Optimized queries for resource and booking data retrieval
3. **Memory Usage**: Efficient data structures for large availability matrices
4. **Concurrent Requests**: Thread-safe operations for high-traffic scenarios

### Scalability Factors
- **Resource Count**: Performance scales with number of tables per place
- **Date Range**: Larger advance booking windows increase computation time
- **Time Slot Density**: Smaller intervals create more slots to process
- **Occupancy Data**: Historical booking data affects query performance

### Monitoring Metrics
- **Response Time**: Track method execution duration
- **Memory Usage**: Monitor memory consumption for large datasets
- **Database Load**: Monitor query performance and connection usage
- **Error Rates**: Track validation failures and system errors

## Implementation Details

### Helper Functions
The method utilizes several helper functions created by `createHelperFunctions()`:

#### generateTimeSlots(date)
Generates available time slots for a specific business day, considering:
- Regular business hours and specific date overrides
- Time slot intervals and minimum duration requirements
- Lead time constraints and booking windows

#### findAvailableTables(partySize, timeSlot, tablesByCapacity, tableOccupancy, maxPartySize, maxCombined)
Finds suitable tables for a party size at a specific time:
- Applies capacity constraints based on `maxCapacity` settings
- Prioritizes single tables over combinations
- Validates table adjacency for combined bookings
- Returns optimal table combinations

#### getPartySizeRanges(minPartySize, maxPartySize)
Defines party size categories for availability reporting:
- Creates standardized size ranges (1-2, 3-4, 5-8, 9+)
- Supports dynamic range adjustment based on restaurant capacity
- Enables consistent availability reporting across different venues

### Data Structures

#### tablesByCapacity
```javascript
// Map of table capacity to available tables
{
  2: [table1, table2],    // Tables with capacity 2
  4: [table3, table4],    // Tables with capacity 4
  6: [table5]             // Tables with capacity 6
}
```

#### tableOccupancy
```javascript
// Map of table ID to occupied periods
{
  "tableId1": [
    { start: dayjs, end: dayjs, quantity: 1 },
    { start: dayjs, end: dayjs, quantity: 1 }
  ]
}
```

### Business Hours Processing
The method aggregates operating hours from multiple table resources:
- **Regular Periods**: Standard weekly operating hours
- **Specific Dates**: Holiday hours and special event overrides
- **Overnight Operations**: Handles venues open past midnight
- **Multiple Periods**: Supports split shifts and break periods

### Time Zone Handling
All date/time operations are timezone-aware:
- Uses restaurant's configured timezone for all calculations
- Maintains consistency across daylight saving transitions
- Converts between timezone-aware dayjs objects and UTC timestamps
- Ensures accurate availability windows regardless of client timezone

## Related Documentation

- [Table Booking Overview](./table-booking.md) - Complete table booking system documentation
- [Resource Management](./resources.md) - Table resource configuration and management
- [Table Queuing](./table-queuing.md) - Queue system integration
- [API Documentation](./API.md) - Complete API reference

## Changelog

### Version History
- **v1.0**: Initial implementation with basic availability matrix
- **v1.1**: Added party size categorization and availability levels
- **v1.2**: Enhanced timezone support and overnight operations
- **v1.3**: Improved performance with optimized queries and caching
- **v1.4**: Added comprehensive error handling and validation

### Recent Updates
- Enhanced business hours aggregation for multiple resources
- Improved table combination logic with adjacency validation
- Added support for tiered capacity constraints
- Optimized memory usage for large availability matrices
