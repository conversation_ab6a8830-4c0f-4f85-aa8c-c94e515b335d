# Product Ordering Documentation

## Table of Contents
- [Overview](#overview)
- [Order Types](#order-types)
- [Order Processing System](#order-processing-system)
- [Order Workflow](#order-workflow)
- [Validation Rules](#validation-rules)
- [Fulfillment Types](#fulfillment-types)
- [Integration Points](#integration-points)
- [API Endpoints](#api-endpoints)
- [Event System](#event-system)
- [Error Handling](#error-handling)
- [Best Practices](#best-practices)

## Overview
This document details the product ordering process within the Product Service, covering different order types, workflows, integration points, and internal processing mechanisms.

### System Architecture
```mermaid
flowchart TB
    Customer[Customer Order] --> ProductService[Product Service]
    
    subgraph "Product Service"
        ProductService --> OrderManager[Order Management]
        ProductService --> BundleManager[Bundle Management]
        ProductService --> QueueManager[Queue Management]
        ProductService --> ResourceManager[Resource Management]
        
        OrderManager --> Validation[Order Validation]
        OrderManager --> Processing[Order Processing]
        OrderManager --> Fulfillment[Order Fulfillment]
        
        ResourceManager --> Calendar[Calendar Integration]
        ResourceManager --> Availability[Availability Management]
        ResourceManager --> Booking[Booking Management]
    end
    
    subgraph "Internal Services"
        ProductService --> SalesService[Sales Service]
        ProductService --> Payment[Payment Service]
    end
    
    subgraph "Fulfillment Types"
        SalesService --> Digital[Digital Products]
        SalesService --> Physical[Physical Products]
    end
    
    subgraph "External Services"
        Physical --> Shopify[Shopify]
        Physical --> Delivery[Food Delivery Platforms]
        Calendar --> GoogleCal[Google Calendar]
    end
```

## Order Types

- **Single Product Orders**
  - Individual physical products with inventory tracking
  - Digital products (memberships, stored value cards, vouchers)
  - Supports quantity and variant options
  - Supports custom variant options per product
  - Vending machine dispensed products

- **Bundle Orders**
  - Fixed bundles with predefined components
  - Dynamic bundles with configurable items
  - Supports mixed physical/digital products
  - Automatic price calculations and adjustments
  - Individual fulfillment tracking per component
  - Bundle restrictions for vending machines

- **Booking Orders**
  - Booking time slots with resource allocation
  - Capacity-managed sessions
  - Calendar-integrated reservations
  - Recurring booking support

- **Provider-Specific Orders**
  - Shopify synchronized orders
  - Food delivery platform orders (GrabFood, UberEats)
  - Custom provider integrations
  - Provider-specific fulfillment rules

### Order Processing Modes
- **Standard Processing**
  - Sequential validation and fulfillment
  - Inventory checks and reservations
  - Payment processing integration
  - Supports both sync and async processing
  
- **Priority Processing**
  - Expedited order handling
  - Queue priority management
  - Real-time processing options

- **Batch Processing**
  - Bulk order handling
  - Optimized inventory updates
  - Consolidated fulfillment options


## Order Processing System

### Queue Management
```mermaid
flowchart TB
    Start[Order Request] --> Queue[Queue System]
    Queue --> Processing[Order Processing]
    Processing --> Concurrent[Concurrent Handling]
    
    subgraph "Queue System"
        Queue --> QV[Queue Validation]
        Queue --> QP[Priority Processing]
        Queue --> QR[Rate Limiting]
    end
    
    subgraph "Concurrent Processing"
        Concurrent --> UserQueue[User-Specific Queue]
        Concurrent --> GlobalQueue[Global Queue]
        Concurrent --> ProviderQueue[Provider Queue]
    end
```

### Queue Configuration
- User-specific queues (`Q_ORDER:userId`)
- Rate limiting and throttling
- Priority processing for different order types
- Concurrent order processing
- Queue error handling and recovery

## Order Workflow

```mermaid
flowchart TB
    Start[Order Initiated] --> Validation[Order Validation]
    Validation --> Payment[Payment Processing]
    Payment --> Qualified[Order Qualification]
    Qualified --> Create[Order Creation]
    Create --> Fulfillment[Fulfillment]
    
    Fulfillment --> Digital[Digital Fulfillment]
    Fulfillment --> Physical[Physical Fulfillment]
    Fulfillment --> Booking[Booking Fulfillment]
    
    Digital --> DigitalComplete[Complete]
    Physical --> PhysicalComplete[Complete]
    Booking --> BookingComplete[Complete]
    
    subgraph "Validation Process"
        Validation --> IV[Inventory Check]
        Validation --> PV[Price Validation]
        Validation --> MV[Membership Check]
        Validation --> BV[Bundle Validation]
    end
    
    subgraph "Product Service Fulfillment"
        Digital --> StoredValue[Process Stored Value]
        StoredValue --> Balance[Balance Update]
        StoredValue --> Currency[Currency Conversion]
        
        Digital --> DigitalVoucher[Process Digital Voucher]
        DigitalVoucher --> Code[Code Generation]
        DigitalVoucher --> Validity[Set Validity Period]
    end

    subgraph "Sales Service Fulfillment"
        Physical --> PhysicalProcess[Process Physical Order]
        PhysicalProcess --> Stock[Stock Update]
        PhysicalProcess --> Pickup[Pickup/Delivery]
        PhysicalProcess --> Track[Tracking Update]
        
        Booking --> BookingProcess[Process Booking]
        BookingProcess --> Resource[Resource Allocation]
        BookingProcess --> Schedule[Schedule Update]
    end

    subgraph "Fulfillment Types"
        DigitalComplete --> Sync[Synchronous]
        PhysicalComplete --> Async[Asynchronous]
        BookingComplete --> Async
    end
```

### Order Workflow Sequence
```mermaid
sequenceDiagram
    participant C as Customer
    participant PS as Product Service
    participant Q as Queue System
    participant PM as Payment Service
    participant SS as Sales Service
    participant VM as Vending Machine
    
    C->>PS: Place Order
    PS->>PS: Validate Order
    PS->>Q: Queue Order Processing
    Q->>PS: Process Order
    PS->>PM: Process Payment
    
    alt Payment Successful
        PM-->>PS: Payment Confirmed
        PS->>PS: Order Qualification
        PS->>SS: Create Order
        SS-->>PS: Order Created
        
        par Digital Fulfillment
            PS->>PS: Handle Digital Fulfillment
        and Async Fulfillment
            SS->>SS: Handle Physical/Booking Fulfillment
        and Vending Fulfillment
            SS->>VM: Send Dispense Command
            VM-->>SS: Dispense Status
            alt Dispense Success
                SS-->>PS: Fulfillment Complete
            else Dispense Failed
                SS->>PM: Initiate Refund
                SS-->>PS: Fulfillment Failed
            end
        end
        
        PS-->>C: Order Status
    else Payment Failed
        PM-->>PS: Payment Failed
        PS-->>C: Order Failed
    end
    
    Note over PS: Digital fulfillment handled synchronously
    Note over SS: Physical/Booking fulfillment handled asynchronously
    Note over VM: Vending fulfillment requires real-time status
```

### Fulfillment Process
1. Order must be fully validated, paid and created before fulfillment begins
2. All orders are created and tracked in the Sales Service
3. Fulfillment is handled based on the product type:
   - **Digital**: Processed through Sales Service with immediate confirmation
   - **Physical**: Async processing through Sales Service (inventory updates, delivery scheduling, tracking)
   - **Booking**: Async processing through Sales Service (resource allocation, calendar updates, confirmations)
4. Product Service maintains order status synchronization with Sales Service

### Bundle Structure
```json
{
    "bundleId": "string",
    "items": [
        {
            "variantId": "string",
            "quantity": "number",
            "unitPrice": "number",
            "adjustedPrice": "number",
            "variantOptions": {
                "customFields": "object",
                "addons": "array"
            }
        }
    ],
    "pricing": {
        "total": "number",
        "subtotal": "number", 
        "adjustments": [
            {
                "type": "string",
                "amount": "number",
                "reason": "string"
            }
        ]
    },
    "fulfillment": {
        "type": "string",
        "status": "string",
        "details": "object"
    }
}
```

### Bundle Price Calculation
- Automatic price adjustments for bundled items
- Individual item price allocation
- Bundle-specific discounts
- Nested bundle price calculation


## Validation Rules

### Inventory Validation
- Check current stock levels
- Reserve inventory during payment processing
- Handle backorder scenarios
- Validate location-specific availability
- Bundle inventory validation

### Price Validation
- Verify current pricing
- Apply membership tier pricing
- Handle multi-currency scenarios
- Validate promotional pricing
- Bundle price validation
- Automatic price adjustments

### Membership Validation
- Check membership requirements
- Validate tier-specific access
- Handle membership-only products
- Process membership upgrades

### Bundle Validation
- Validate bundle composition
- Check bundle availability
- Verify bundle pricing
- Validate nested bundles

### Vending Machine Validation
- Verify machine operational status
- Check real-time inventory
- Validate product compatibility
- Check machine capacity
- Verify payment methods
- Validate operating hours
- Check maintenance schedule


## Fulfillment Types

### Digital Fulfillment

#### Stored Value Products
  - Balance initialization
  - Currency conversion handling
  - Top-up processing
  - Balance reservation during payment
  - Balance release on payment failure
  - Multi-currency balance tracking
  - Balance expiry handling
  - Balance transfer rules
  - Transaction history tracking

#### Booking
- **Resource Allocation**
  - Timeslot reservation
  - Capacity checking
  - Resource assignment
  
- **Schedule Management**
  - Calendar updates
  - Reminder setup
  - Conflict resolution

#### Digital Vouchers
  - Code lifecycle: generation, validation, reservation, and release
  - Status flow: RESERVED → ISSUED → REDEEMED (or RELEASED on failure)
  - Features:
    - Single/multi-use codes
    - Time-limited validity
    - Value-specific options
    - Batch management
    - Usage tracking

### Physical Fulfillment
- **Inventory Management**
  - Stock deduction
  - Location assignment
  - Backorder handling
  - Bundle inventory management
  - Vending machine inventory sync
  
- **Delivery Options**
  - Pickup scheduling
  - Delivery tracking
  - Provider assignment
  - Vending machine dispensing
    - Real-time inventory checks
    - Machine status validation
    - Dispensing confirmation
    - Error handling and refunds

### Vending Machine Fulfillment
- **Machine Types**
  - Standard vending machines
  - Smart vending machines with real-time monitoring
  - Multi-product dispensers
  
- **Purposes**
  - Direct order and dispense
  - Pickup point for pre-orders
  - Redemption point for digital vouchers
  
- **Operational Requirements**
  - Real-time inventory sync
  - Payment processing integration
  - Error detection and reporting
  - Remote monitoring capabilities
  
- **Order Processing**
  - Pre-dispense validation
  - Payment confirmation
  - Dispensing command
  - Success/failure confirmation
  - Automatic refund on failure


## Integration Points

### Internal Services
- Payment Service: Transaction processing
- Sales Service: Order tracking
- Membership Service: Associate membership with order

### External Providers
- Shopify: E-commerce orders
  - Product sync
  - Inventory sync
  - Order sync
  - Fulfillment sync
- GrabFood/UberEats: Food delivery orders
- Payment Gateways: Transaction processing


## API Endpoints

### Order Creation
```http
POST /variants/order
Content-Type: application/json

{
    "items": [
        {
            "variantId": "string",
            "quantity": number,
            "variantOptions": {
                "customFields": object,
                "addons": array
            },
            "fulfillment": {
                "type": "digital|physical|booking",
                "details": object
            },
            "bundleInfo": {
                "bundleId": "string",
                "parentItemId": "string"
            }
        }
    ],
    "customer": {
        "id": "string",
        "membershipTier": "string",
        "membershipId": "string"
    },
    "queueOptions": {
        "priority": "number",
        "processImmediately": "boolean",
        "retryAttempts": "number"
    }
}
```

### Order with Payment
```http
POST /variants/order/pay
Content-Type: application/json

{
    "orderId": "string",
    "payment": {
        "method": "string",
        "amount": number,
        "currency": "string",
        "provider": {
            "name": "string",
            "options": object
        }
    },
    "queueOptions": {
        "priority": "number",
        "processImmediately": "boolean"
    }
}
```

### Order Commitment
```http
POST /variants/order/commit
Content-Type: application/json

{
    "orderId": "string",
    "paymentId": "string",
    "queueOptions": {
        "priority": "number",
        "processImmediately": "boolean"
    }
}
```

### Receipt Processing
```http
POST /variants/bag/receipt
Content-Type: application/json

{
    "receipt": [
        {
            "id": "string",
            "kind": "string",
            "product": object,
            "variant": object,
            "unitPrice": number,
            "quantity": number,
            "bundled": ["string"],
            "bundleId": "string",
            "variantOptions": object
        }
    ],
    "fulfillments": object
}
```

### Vending Machine Operations
```http
POST /vending/dispense
Content-Type: application/json

{
    "machineId": "string",
    "orderId": "string",
    "items": [
        {
            "slotId": "string",
            "productId": "string",
            "variantId": "string",
            "quantity": "number"
        }
    ]
}
```

```http
GET /vending/status/{machineId}
```

```http
POST /vending/refund
Content-Type: application/json

{
    "orderId": "string",
    "reason": "string",
    "items": [
        {
            "variantId": "string",
            "quantity": "number"
        }
    ]
}
```


## Event System

### Order Events
```json
{
    "order.created": {
        "orderId": "string",
        "items": [],
        "customer": {},
        "status": "pending"
    },
    "order.paid": {
        "orderId": "string",
        "payment": {},
        "status": "paid"
    },
    "order.fulfilled": {
        "orderId": "string",
        "fulfillment": {},
        "status": "fulfilled"
    },
    "stored_value.reserved": {
        "orderId": "string",
        "balanceId": "string",
        "amount": "number",
        "currency": "string"
    },
    "stored_value.released": {
        "orderId": "string",
        "balanceId": "string",
        "amount": "number",
        "currency": "string",
        "reason": "string"
    },
    "stored_value.credited": {
        "orderId": "string",
        "balanceId": "string",
        "amount": "number",
        "currency": "string",
        "expiresAt": "string"
    },
    "voucher.reserved": {
        "orderId": "string",
        "voucherId": "string",
        "code": "string",
        "type": "string",
        "validFrom": "string",
        "validTo": "string"
    },
    "voucher.released": {
        "orderId": "string",
        "voucherId": "string",
        "code": "string",
        "reason": "string"
    },
    "voucher.issued": {
        "orderId": "string",
        "voucherId": "string",
        "code": "string",
        "type": "string",
        "validFrom": "string",
        "validTo": "string",
        "maxUses": "number",
        "value": {
            "amount": "number",
            "currency": "string"
        }
    }
}
```

### Fulfillment Events
```json
{
    "fulfillment.digital.completed": {
        "orderId": "string",
        "variantId": "string",
        "details": {
            "balanceId": "string",
            "amount": "number",
            "currency": "string",
            "type": "string",
            "voucher": {
                "code": "string",
                "validFrom": "string",
                "validTo": "string",
                "maxUses": "number",
                "value": {
                    "amount": "number",
                    "currency": "string"
                }
            }
        }
    },
    "fulfillment.physical.shipped": {
        "orderId": "string",
        "tracking": {},
        "status": "shipped"
    },
    "fulfillment.booking.confirmed": {
        "orderId": "string",
        "booking": {},
        "status": "confirmed"
    },
    "fulfillment.bundle.completed": {
        "orderId": "string",
        "bundleId": "string",
        "items": [],
        "status": "completed"
    }
}
```

## Error Handling

### Common Error Scenarios
1. Insufficient inventory
2. Invalid pricing
3. Membership restrictions
4. Payment failures
5. Fulfillment errors
6. Queue processing errors
7. Bundle validation errors
8. Provider integration errors

### Queue-Related Errors
1. Queue timeout
2. Concurrent processing conflicts
3. Rate limit exceeded
4. Priority processing failures
5. Queue system unavailability

### Error Response Format
```json
{
    "error": {
        "code": "string",
        "message": "string",
        "details": {},
        "queueInfo": {
            "queueId": "string",
            "attempt": "number",
            "lastError": "string",
            "retryAfter": "number"
        },
        "context": {
            "orderId": "string",
            "variantId": "string",
            "customerId": "string"
        }
    }
}
```

### Vending Machine Errors
1. **Machine Unavailable**
   - Machine offline
   - Machine in maintenance
   - Outside operating hours
   - Network connectivity issues

2. **Inventory Issues**
   - Product out of stock
   - Slot empty
   - Slot jammed
   - Product mismatch

3. **Dispensing Failures**
   - Mechanical failure
   - Power interruption
   - Sensor malfunction
   - Product stuck

4. **Recovery Procedures**
   - Automatic refund initiation
   - Error reporting to maintenance
   - Customer notification
   - Inventory adjustment
   - Order status update


## Best Practices

### Order Processing
1. Always validate before payment
2. Use atomic transactions
3. Implement idempotency
4. Handle partial fulfillment
5. Maintain audit logs
6. Implement proper queue management
7. Handle bundl

### Queue Management
1. Implement proper rate limiting
2. Handle priority processing
3. Implement dead letter queues
4. Monitor queue health
5. Implement queue scaling
6. Handle queue failures gracefully

### Error Recovery
1. Implement rollback mechanisms
2. Use retry policies
3. Maintain transaction consistency
4. Log detailed error states
5. Handle timeout scenarios
6. Implement queue recovery mechanisms
7. Handle partial bundle fulfillment

### Vending Machine Workflow
```mermaid
flowchart TB
    Start[Order Initiated] --> VMCheck[Check Machine Status]
    VMCheck --> Validation[Order Validation]
    Validation --> Payment[Payment Processing]
    Payment --> Qualified[Order Qualification]
    Qualified --> Create[Order Creation]
    Create --> VMDispense[Vending Dispense]
    
    VMDispense --> Success[Dispense Success]
    VMDispense --> Failure[Dispense Failure]
    
    Success --> Complete[Order Complete]
    Failure --> Refund[Process Refund]
    Refund --> Failed[Order Failed]
    
    subgraph "Vending Pre-checks"
        VMCheck --> Status[Machine Online]
        VMCheck --> Stock[Stock Available]
        VMCheck --> Compatible[Product Compatible]
        VMCheck --> Hours[Operating Hours]
    end
    
    subgraph "Dispense Process"
        VMDispense --> Command[Send Command]
        Command --> Monitor[Monitor Status]
        Monitor --> Confirm[Confirm Dispense]
    end
```
