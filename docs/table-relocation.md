# Table Relocation

Table Relocation is part of the Product Service in the CRM platform that enables staff to relocate customers from their current table(s) to other available table(s).

## Table of Contents
1. [Overview](#overview)
2. [Design](#design)
3. [Relocation Process Flow](#relocation-process-flow)
4. [API Endpoints](#api-endpoints)
5. [Integration Points](#integration-points)
6. [Error Handling](#error-handling)
7. [Atomic Operations](#atomic-operations)

**Related Documentation:**
- [Table Booking](./table-booking.md) - Table booking and allocation strategies
- [Table Queuing](./table-queuing.md) - Queue management and priority settings

## Overview

The Table Relocation system enables restaurant staff to move customers from their current table(s) to other available table(s) while maintaining order history and service continuity. The system operates differently depending on whether the Booking feature is enabled or disabled.

### Key Features

- **NFC-Initiated Relocation**: Staff can initiate relocation by tapping an NFC tag on a customer's table
- **Order Continuity**: Automatically relocates all outstanding (not fully fulfilled) orders to new table(s)
- **Atomic Operations**: Ensures all updates are performed as a single atomic operation
- **Kitchen Notification**: Notifies kitchen staff about table changes with clear origin information

### Additional Features When Booking is Enabled
- **Reservation-Centric**: Uses reservation as the primary entity for relocation operations
- **Partial Relocation Support**: Allows relocating only a portion of a large party
- **Automatic Availability Validation**: System validates table availability

### Core Components

The feature is designed as a two-step process:

1. **Intent Phase** (Information Gathering)
   - Identifies source table(s) and validates relocation feasibility
   - Finds suitable destination tables that are available
   - Returns options for staff selection

2. **Commit Phase** (Execution)
   - Updates bookings (when Booking feature is enabled)
   - Relocates all outstanding orders to new tables
   - Notifies kitchen about relocated orders with source information

## Design

The Table Relocation feature is designed with a clear separation of concerns:

- **Intent Phase**: Gathers information and validates options without making changes
- **Commit Phase**: Executes the relocation as an atomic operation

This separation allows staff to make informed decisions before committing to changes, reducing errors and improving the user experience.

### System Architecture

```mermaid
graph TD
    subgraph "Staff Mobile App"
        NFC[NFC Tap]
        UI[Selection UI]
        Confirm[Confirmation]
    end

    subgraph "Product Service"
        Intent[Relocation Intent]
        Commit[Relocation Commit]
        RM[Resource Manager]
        BM[Booking Manager]
    end

    subgraph "Sales Service"
        Order[Order Manager]
        Fulfill[Fulfillment]
        RO[Relocate Table Fulfillments API]
    end

    subgraph "Kitchen System"
        KDS[Kitchen Display]
        Notify[Notification]
    end

    NFC --> Intent
    Intent --> UI
    UI --> Confirm
    Confirm --> Commit

    Intent <--> RM
    Intent <--> BM
    Commit <--> BM
    Commit <--> RO
    RO <--> Fulfill
    Fulfill --> Notify
    Notify --> KDS
```

### Component Interactions

1. **Staff Mobile App**
   - Initiates relocation via NFC tap on any table in the reservation
   - Displays source tables (all tables in the reservation) and available destination options
   - Handles staff selection and confirmation with clear preview of changes

2. **Product Service**
   - Manages the relocation intent and commit operations
   - Handles resource availability and booking updates
   - Coordinates transaction management across services
   - Calls Sales Service's Relocate Table Fulfillments API

3. **Sales Service**
   - Provides Relocation Table Fulfillments API (from tables, to tables)
   - Manages fulfillment updates
   - Preserves order history and maintains continuity
   - Emits detailed events for kitchen notification with source table information

4. **Kitchen System**
   - Receives enhanced notifications about relocated orders
   - Displays clear "relocated from" information on tickets
   - Updates kitchen display to reflect table changes with visual indicators

### Data Flow

The following sequence diagram illustrates the data flow between components during the table relocation process. Note that the Transaction Manager (TM) shown below represents the transaction management logic within the Product Service, not a separate component.

```mermaid
sequenceDiagram
    actor Staff as Staff
    participant App as Perkd App
    participant PS as Product Service
    participant TM as Transaction Manager
    participant SS as Sales Service
    participant KS as Kitchen System

    %% Intent Phase
    rect rgb(31, 62, 110)
    Note over Staff,KS: Intent Phase
    Staff->>App: Tap NFC on table
    App->>PS: Call staffRelocateTableIntent

    alt Booking Enabled
        PS->>PS: Find reservation & all associated tables
        PS->>PS: Validate relocation feasibility
        PS->>PS: Find available destination tables
        PS-->>App: Return source & destination options
    else Booking Disabled
        PS->>PS: Identify single source table
        Note over PS: No reservation context
        PS->>PS: Find orders associated with table
        PS->>PS: List tables with no active orders
        Note over PS: Cannot validate actual availability
        PS-->>App: Return source table and potential destinations
        Note over PS,App: Include warnings about manual management
    end
    end

    %% Staff Decision
    alt Booking Enabled
        Staff->>App: Select tables to relocate
    else Booking Disabled
        Staff->>App: Visually verify and select destination table
        Note over Staff,App: Staff must manually verify table is available
    end

    %% Commit Phase
    rect rgb(74, 48, 31)
    Note over Staff,KS: Commit Phase

    alt Booking Enabled
        App->>PS: Call staffRelocateTableCommit with reservationId
        PS->>TM: Begin transaction
        TM->>PS: Create new bookings & set to SUCCESS
        Note over TM,PS: Preserve all booking metadata
        TM->>PS: End original bookings
        TM->>SS: Call relocateTableFulfillments API
        SS->>SS: Find all orders by reservation
    else Booking Disabled
        App->>PS: Call staffRelocateTableCommit
        PS->>SS: Call relocateTableFulfillments API with single table
        Note over PS,SS: Simple table-to-table relocation
    end

    SS->>SS: Update order fulfillments
    SS->>SS: Add source table information
    SS->>KS: Emit relocation events
    KS->>KS: Update kitchen displays with origin info

    alt Success
        alt Booking Enabled
            TM->>TM: Commit transaction
        end
        PS-->>App: Return success with details
    else Failure
        alt Booking Enabled
            TM->>TM: Rollback transaction
        end
        PS-->>App: Return error with details
    end

    App-->>Staff: Show confirmation or error
    end
```

## Relocation Process Flow

```mermaid
graph TD
    %% Intent Phase
    subgraph "Intent Phase"
        A[Staff Taps NFC] --> B[Identify Source Table]
        B --> C{Booking Enabled?}

        C -->|Yes| D1[Find Reservation]
        D1 --> E1[Get All Tables in Reservation]
        E1 --> F1[Validate Reservation Status]
        F1 --> G1[Find Available Destination Tables]

        C -->|No| D2[Direct Table Identification]
        D2 --> E2[Find Orders Associated with Table]
        E2 --> F2[List Tables with No Active Orders]

        G1 --> H[Staff Selects Source and Destination Tables]
        F2 --> H
    end

    %% Commit Phase
    subgraph "Commit Phase"
        H --> I{Booking Enabled?}

        I -->|Yes| M1[Create New Bookings for Destinations & set to SUCCESS]
        M1 --> O1[Relocate Orders]
        O1 --> L2[End Original Bookings of selected Source Tables]

        I -->|No| V1[Validate Single Table Restriction]
        V1 --> V2[Validate Active Orders Exist]
        V2 --> J2[Simple Table-to-Table Relocation]

        L2 --> P{Success?}
        J2 --> P

        P -->|Yes| Q[Update UI with Success]
        P -->|No| R[Rollback & Show Error]
    end
```

### Key Process Details

#### Intent Phase

The Intent phase gathers information and validates options without making changes to the system.

##### Source Table Identification
- Takes a source table object with a resourceId
- Checks if the Booking feature is enabled in settings
- **With Booking Enabled**:
  - Finds the active booking for the source table
  - Finds all tables under the same reservation
  - Validates the reservation is active (status `success` or `open`)
- **With Booking Disabled**:
  - Uses the provided table as the single source table
  - Finds all orders associated with the table
  - Validates there are active orders for the table

##### Destination Table Selection
- **With Booking Enabled**:
  - Finds available tables based on party size and remaining reservation time
  - Excludes tables that are already part of the reservation
  - Provides table information including capacity and availability
- **With Booking Disabled**:
  - Finds all tables in the same place
  - Provides basic table information

#### Commit Phase

The Commit phase executes the relocation operations in a coordinated sequence.

##### Operation Sequence
The operations are executed in the following order:
1. Create new bookings for destination tables (when booking is enabled) & set status to `success`
2. Relocate orders from source tables to destination tables via `Order.relocateTableFulfillments`
3. End original bookings for source tables (when booking is enabled) - executed asynchronously

##### Booking Management (When Enabled)
- Creates new bookings for destination tables with preserved metadata
- Maintains reservation relationships between tables
- Uses `Booking.requestAndConfirm` for creating new bookings
- Sets new bookings to `success` status with `arrivedAt` timestamp
- After order relocation, transitions booking statuses (`success` → `ended` for source tables) asynchronously

##### Order Relocation
- **With Booking Enabled**:
  - Relocates orders from multiple source tables to multiple destination tables
  - Uses the `relocateTableFulfillments` API of Sales Service for order relocation
- **With Booking Disabled**:
  - Limited to single table-to-table relocation (enforced by validation)
  - Validates that there are active orders for the source table
  - Requires explicit validation that destination tables have been manually verified as available
  - Uses the same `relocateTableFulfillments` API of Sales Service for consistency

###### Order.relocateTableFulfillments API
The Sales Service provides the `relocateTableFulfillments` API that handles the actual order relocation:

```javascript
/**
 * Relocate (partial) fulfillments of DINEIN orders from source tables to destination tables
 * @param {Spot[]} from - List of source tables { type, name, placeId, resourceId, position[] }
 * @param {Spot[]} to - List of destination tables { type, name, placeId, resourceId, position[] }
 * @returns {Object} { relocatedOrders }
 */
Order.relocateTableFulfillments = async function(from, to) {
  // 1. Find all open fulfillments for the source tables
  // 2. Update fulfillment destinations to the new tables
  // 3. Preserve source table information in the destination
  // 4. Emit events for kitchen notification
  // 5. Return details of relocated orders
}
```

This API is called by both booking-enabled and booking-disabled flows, providing a consistent approach to order relocation.

##### Kitchen Notification
- In call to `relocateTableFulfillments`, Sales Service emits events with source and destination information
  - `sales.fulfillment.kitchen.relocated` for kitchen fulfillments
  - `sales.fulfillment.relocated` for other fulfillment types
- Preserves source table information by adding it to the destination position
- Includes detailed context about the original table for kitchen staff

##### Error Handling
- Provides error messages for validation failures
- Logs errors for debugging purposes
- Returns error details to the client
- Has limited rollback capabilities in the current implementation

#### Special Cases

- **Partial Relocation** (Booking Enabled Only):
  - Relocates only selected tables while maintaining reservation integrity
  - Preserves relationships between all tables in the same reservation

- **Single Table Relocation** (Booking Disabled):
  - Only supports one-to-one table relocation
  - Requires staff to manually manage multiple tables if needed

## API Endpoints

The Table Relocation feature exposes two main API endpoints:

### 1. Relocation Intent API
`POST /Products/staffRelocateTableIntent`

Gathers information and validates relocation feasibility without making changes.

**Key Parameters:**
- `from` (Spot object, required): Source table information
- `type` (string, required): Type of the source table (e.g., `table`)
- `name` (string, required): Name of the source table
- `placeId` (string, required): ID of the place where the table is located
- `resourceId` (string, required): ID of the source table
- `position` (array, required): Position of the source table

**Response Includes:**
- `sourceTables` (array): Source tables with order information
- `availableTables` (array): Available destination tables with capacity information
- `reservation` (object, when booking is enabled): Reservation details

**Error Handling:**
- Returns appropriate error codes for invalid tables, missing reservations, or permission issues
- Provides detailed error messages with recovery suggestions

### 2. Relocation Commit API
`POST /Products/staffRelocateTableCommit`

Executes the relocation operation.

**Key Parameters:**
- `sourceIds` (array, required): IDs of tables to relocate from
- `destinationIds` (array, required): IDs of tables to relocate to
- `reservationId` (string, required when booking is enabled): ID of the reservation

**Response Includes:**
- `originalTables` (array): Details of source tables
- `newTables` (array): Details of destination tables
- `relocatedOrders` (array): List of relocated orders

**Validation Rules:**
- When booking is disabled, only single table-to-table relocation is supported
- Source and destination tables must exist and be in the same place
- Source tables must have active orders
- When booking is enabled, a valid reservation ID must be provided
- When booking is disabled, manual verification is required

**Error Handling:**
- Validates inputs before execution
- Returns error information with context
- Limited rollback capabilities in the current implementation

## Integration Points

### Event Integration

The Table Relocation feature integrates with the existing event system. The following events are emitted by the Sales Service during the relocation process:

**Key Events from Sales Service:**
- `sales.fulfillment.relocated`: Emitted by Sales Service when orders are relocated
- `sales.fulfillment.kitchen.relocated`: Emitted by Sales Service specifically for kitchen notifications

**Note:** While the Product Service orchestrates the table relocation process, the actual order relocation and event emission is handled by the Sales Service through the `relocateTableFulfillments` API. The Product Service does not directly emit events related to order relocation.

### System Integration

```mermaid
graph TD
    subgraph "Table Relocation System"
        Intent[Relocation Intent]
        Commit[Relocation Commit]
    end

    subgraph "Core Services"
        RM[Resource Management]
        BM[Booking Management]
        OM[Order Management]
        FM[Fulfillment Management]
    end

    subgraph "APIs"
        RO[RelocateTableFulfillments API]
    end

    Intent --> RM
    Commit --> RO
    RO --> OM
    OM --> FM

    %% Booking-specific connections
    Intent -.->|"When Booking Enabled"| BM
    Commit -.->|"When Booking Enabled"| BM
```

**Integration Components:**

1. **Resource Management**
   - Provides table information and availability checking
   - Used by both booking-enabled and booking-disabled flows

2. **Order Management**
   - Handles order relocation through the `relocateTableFulfillments` API:
     - With multiple tables when booking is enabled
     - With single source and destination tables when booking is disabled
   - Ensures order continuity during relocation

3. **Booking Management**
   - Only used when booking feature is enabled
   - Manages creation and ending of bookings
   - Preserves metadata during relocation

## Error Handling

The Table Relocation system implements error handling with the following characteristics:

### Validation Strategy

- **Input Validation**: Validates required parameters before processing
  - Checks for source and destination table IDs
  - Validates reservation ID when booking is enabled
  - Requires manual verification acknowledgment when booking is disabled
- **Business Logic Validation**:
  - Verifies active bookings exist (when booking is enabled)
  - Checks for active orders on source tables
  - Enforces single table-to-table restriction when booking is disabled

### Error Response Format

API endpoints return error responses with:
- HTTP status code (typically 400 for validation errors, 500 for system errors)
- Error code for programmatic handling (e.g., 'NO_ACTIVE_ORDERS', 'TRANSACTION_FAILED')
- Human-readable message
- Additional error details in some cases

### Common Error Scenarios and Recovery Strategies

| Error Code | Phase | Description | Recovery Strategy |
|------------|-------|-------------|-------------------|
| `invalid_table_ids` | Commit | Source or destination table IDs missing | Ensure all required table IDs are provided |
| `NO_ACTIVE_ORDERS` | Intent/Commit | No active orders found for source table | Verify table has active orders before attempting relocation |
| `tables_unavailable` | Commit | One or more destination tables are not available | Select different destination tables or try again later |
| `TRANSACTION_FAILED` | Commit | General transaction failure during relocation | Check error details and retry with valid parameters |
| `ROLLBACK_FAILED` | Commit | Transaction failed and rollback was unsuccessful | Contact system administrator for manual recovery |
| `invalid_reservation` | Intent/Commit | Invalid or missing reservation ID (booking enabled) | Provide valid reservation ID or verify booking exists |
| `single_table_restriction` | Commit | Multiple tables specified when booking is disabled | Use only one source and one destination table |
| `manual_verification_required` | Commit | Missing manual verification flag (booking disabled) | Set acknowledgeMissingValidation to true |

## Atomic Operations

Table relocation operations aim to maintain data consistency through a series of coordinated operations:

### Current Implementation Scope

- Creation of new bookings for destination tables (when booking is enabled)
- Relocation of all orders associated with source tables
- Ending of original bookings (when booking is enabled)
- Emission of events for kitchen notification

### Error Handling and Transaction Management

In the current implementation:

#### Error Logging and Reporting
- Comprehensive error logging is provided for debugging purposes
- Detailed error information is returned to the client with appropriate status codes
- Error codes are standardized for programmatic handling (e.g., 'NO_ACTIVE_ORDERS', 'TRANSACTION_FAILED')

#### Transaction Management
- The Product Service manages the transaction logic internally with a structured approach
- When booking is enabled, the implementation executes in this sequence:
  1. Create new bookings for destination tables & set status to `success`
  2. Relocate orders to new tables via Sales Service API
  3. End original bookings for source tables
- Operations are coordinated to maintain data consistency
- Detailed error logging captures the context of any failures

#### Rollback Capabilities
- Error recovery mechanisms for the booking-enabled flow include:
  - Standardized error responses with specific error codes
  - Detailed error context for debugging and client feedback
  - Structured error handling for each operation phase
- The system provides comprehensive error details including:
  - Original error information
  - Operation context (tables, reservation, etc.)
  - Specific error codes for programmatic handling
- The booking-disabled flow uses simplified error handling focused on validation

### Future Enhancements

Planned improvements to transaction management:
- Implement database-level transactions for atomic operations
- Add comprehensive concurrency control for high-traffic scenarios
- Enhance audit trail with detailed operation logging
- Implement automated recovery for common failure scenarios

## Implementation Status

The Table Relocation feature has been implemented with the following status:

1. **Core Functionality**: ✅ Implemented
   - Intent and Commit endpoints
   - Support for both booking-enabled and booking-disabled flows
   - Complete order relocation functionality via Sales Service API
   - Partial relocation support (booking-enabled only)

2. **Transaction Management**: ✅ Implemented
   - Comprehensive error handling with specific error codes
   - Error recovery mechanisms for common failure scenarios
   - Coordinated transaction management for booking-enabled flow
   - Structured error responses with context information

3. **Kitchen Notification**: ✅ Implemented
   - Event emission for kitchen notifications via Sales Service
   - Source table tracking in fulfillment destinations
   - Detailed context information for kitchen staff

4. **API Endpoints**: ✅ Implemented
   - `POST /Products/staff/tables/relocate/intent` - Intent endpoint
   - `POST /Products/staff/tables/relocate/commit` - Commit endpoint
   - Full parameter validation and error handling

5. **Testing**: ⚠️ Needs Implementation
   - Unit tests for each component
   - Integration tests for the complete flow
   - Edge case testing
