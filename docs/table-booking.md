# Table Booking
Table Booking is part of the Product Service in the CRM platform that manages table reservations for restaurants.

# Table of Contents
1. [Overview](#overview)
2. [Table Booking Flow](#table-booking-flow)
3. [Table Allocation Logic](#table-allocation-logic)
    - [Table Selection Process](#table-selection-process)
    - [Tiered maxCapacity Configuration](#tiered-maxcapacity-configuration)
4. [Booking States](#booking-states)
5. [Atomic Booking Process](#atomic-booking-process)
6. [Smart Release and Reassign](#smart-release-and-reassign)
7. [Integrations](#integrations)
    - [Table Queuing Integration](#table-queuing-integration)
    - [Table Relocation Integration](#table-relocation-integration)
    - [Calendar Integration](#calendar-integration)
8. [Date Handling Best Practices](#date-handling-best-practices)
9. [API Endpoints](#api-endpoints)
10. [Table-Specific Settings](#table-specific-settings)

For queue management and priority settings, see [Resource Queuing](./resource-queuing.md).

## Overview
The table booking system provides a comprehensive solution for restaurant reservations, designed to maximize table occupancy, enhance customer experiences, and streamline booking operations for restaurants.

The system operates through three main layers:

```mermaid
graph LR
    subgraph Customer Touchpoints
        A[Member Booking]
        B[Non-member Booking]
    end

    subgraph Core Components
        C[Booking Management]
        D[Resource Management]
        E[Configuration Settings]
    end

    F[Sales Service]
    G[Calendar Service]

    A --> C
    B --> C
    C <--> D
    D <--> G
    C <--> F
    C <--> E
```

1. **Customer Touchpoints**: Members via Perkd App, Non-members via Website
2. **Core Components**: manages bookings and resource allocations according to configured settings
3. **External Services**: Sales Service to create & retrieve bookings, and Calendar Service

### Key Features
- Smart table allocation with support for single and combined tables. A single reservation request might result in multiple tables being assigned (and thus multiple booking records created) if `allowCombined` is enabled and necessary to accommodate the party size.
- Flexible booking durations with configurable time slots
- Real-time availability monitoring and dynamic resource management
- Automated handling of no-shows with queue system integration
- Calendar integration for synchronized booking management
- Timezone-aware operations with business hours support
- Comprehensive validation and error handling

### Core Components

1. **Booking Management**
   - Manages the complete booking lifecycle (pending → confirmed → completed/cancelled)
   - Implements atomic booking process for reliable table allocation
   - Provides comprehensive validation and automatic rollback mechanisms

2. **Resource Management**
   - Handles table configurations and combinations with adjacency rules
   - Optimizes capacity utilization through dynamic allocation and thresholds
   - Controls real-time availability with business hours integration
   - Implements key methods:
     * `Resource.available()`: Finds available tables for a given time and party size
     * `Resource.candidates()`: Generates and scores table combinations
     * `Resource.assign()`: Allocates tables and creates booking records

3. **Configuration Settings**
   - Provides global and per-resource settings
   - Customizable booking rules and thresholds
   - Manages business hours and timezone settings


## Table Booking Flow

```mermaid
graph TD
    A[Booking Request] --> B[Validate Request]
    B -->|Validate| C[Time Window Check]
    B -->|Validate| D[Party Size Check]

    C --> E[Find Available Tables]
    D --> E

    E -->|1\. Find Candidates| F[Match Capacity]
    F -->|2\. Check Availability| G[Check Occupancy]
    G -->|3\. Validate Hours| H[Business Hours]

    H --> I[Generate Table Candidates]
    I -->|Score & Prioritize| J[Prioritize Tables]

    J --> K[Resource Assignment]
    K --> L[Create Booking]
    L --> M[Confirm Booking]
```

1. **Initial Validation**
   - Time Window: Validates lead time, advance booking window, and business hours
   - Party Size: Checks against min/max party size limits

2. **Resource Search**
   - Find Candidates: Identifies tables matching capacity requirements
   - Check Occupancy: Verifies table availability for requested time slot
   - Business Hours: Validates against operating hours

3. **Table Selection**
   - Generate Candidates: Creates possible table combinations if needed
   - Prioritize Tables: Scores and ranks available options based on optimal fit using weighted scoring:
     - **For Immediate Seating**: Capacity Fit (75%) + Adjacency (25%)
     - **For Queue Assignment**: Utilization (70%) + Adjacency (20%) - Multiple Resource Penalty (10%)
   - **Enhanced Isolation Preference**: Among available tables, applies dual-factor isolation scoring to optimize both customer experience and operational efficiency:

#### Dual-Factor Isolation Scoring

The system uses an enhanced isolation scoring formula that considers two factors:

```
Isolation Score = 1 - (Occupied Penalty) - (Adjacency Penalty)
where:
  Occupied Penalty = (Occupied Adjacent Tables / Total Adjacent Tables)
  Adjacency Penalty = (Total Adjacent Tables × 0.1)
```

**Primary Factor - Customer Privacy (Occupied Penalty):**
- Heavily penalizes tables adjacent to currently occupied or soon-to-be-occupied tables
- Ensures customers are not seated next to noisy or crowded areas
- Penalty ranges from 0% (no occupied neighbors) to 100% (all neighbors occupied)

**Secondary Factor - Combination Preservation (Adjacency Penalty):**
- Lightly penalizes tables with many adjacent tables (10% per adjacent table)
- Preserves adjacent table pairs/groups for larger parties that may need combinations
- Encourages use of truly isolated tables when available

#### Practical Examples

| Table Configuration | Occupied Penalty | Adjacency Penalty | Final Score | Priority |
|---------------------|------------------|-------------------|-------------|----------|
| **Table A**: No adjacent tables | 0.0 | 0.0 | **1.0** | Highest (completely isolated) |
| **Table B**: 1 adjacent table (available) | 0.0 | 0.1 | **0.9** | High |
| **Table C**: 2 adjacent tables (both available) | 0.0 | 0.2 | **0.8** | Medium-High |
| **Table D**: 1 adjacent table (occupied) | 1.0 | 0.1 | **0.0*** | Lowest |
| **Table E**: 2 adjacent tables (1 occupied) | 0.5 | 0.2 | **0.3** | Low |

*Score is capped at 0.0 minimum

#### Business Benefits

- **Enhanced Customer Privacy**: Primary focus on avoiding occupied neighbors reduces noise and crowding
- **Strategic Table Management**: Secondary focus on preserving adjacent pairs for larger parties
- **Improved Venue Distribution**: Encourages balanced seating across the restaurant
- **Operational Flexibility**: Maintains table combination options for varying party sizes

4. **Booking Creation**
   - Resource Assignment: Locks selected tables (could be multiple if combined).
   - Create Booking: Initializes booking record(s) in the Sales Service. If multiple tables are assigned, multiple `Booking` records are created, each linked by a common `reservationId`.
   - Confirm Booking: Finalizes the reservation details and handles deposit if required.


## Table Allocation Logic

The table allocation system uses a sophisticated algorithm to find the best tables for a given party size and time slot:

```mermaid
graph TD
    A[Party Size & Time Slot] --> B[Apply maxCapacity Constraint]
    B --> C[Search for Single Tables]
    C --> D{Single Tables Available?}
    D -->|Yes| E[Apply Isolation Preference]
    E --> F[Sort by Isolation Score]
    F --> G[Return Single Tables]

    D -->|No| H[Search for Table Combinations]
    H --> I[Filter for Available Tables]
    I --> J[Sort by Capacity]
    J --> K[Find Valid Combinations]
    K --> L[Check Table Adjacency]
    L --> M[Sort by Optimal Fit]
    M --> N[Return Best Combination]
```

### Table Selection Process

1. **Table Capacity Range**:
   - Tables in the system have capacities between `minSize` and `maxSize` (default: 2-12)
   - These settings define the range of table sizes available in the restaurant

2. **Apply maxCapacity Constraint**:
   - For each party size, calculate the maximum allowed table capacity using the `maxCapacity` setting
   - For example, with party size 2 and maxCapacity 2.0, only tables with capacity ≤ 4 are considered

3. **Single Table Search**:
   - First try to find single tables with appropriate capacity
   - Tables must have capacity ≥ party size and ≤ maxAllowedCapacity
   - Filter for availability during the requested time slot
   - **Apply isolation preference**: Prioritize tables using dual-factor scoring (avoid occupied neighbors, prefer isolated tables)

4. **Table Combination Search** (if no single tables available and `allowCombined` is enabled):
   - Consider combinations of smaller available tables
   - Only applies to parties with size ≥ `minSize` setting
   - Respect the `maxCombined` setting (default: 3)
   - Ensure tables are adjacent using adjacency information if `adjacentOnly` is enabled
   - Sort combinations by number of tables (fewer is better) and optimal capacity fit

### Tiered maxCapacity Configuration

The `maxCapacity` setting can be configured as a tiered object to provide different capacity constraints for different party sizes:

```json
"maxCapacity": {
    "1": 2.0,
    "2": 2.0,
    "3": 1.5,
    "4": 1.5,
    "5": 1.3,
    "6": 1.3,
    "7": 1.3,
    "8": 1.3,
    "9": 1.2,
    "10": 1.2
}
```

With this configuration:
- Party size 1-2: Tables with capacity up to 2× party size are considered
- Party size 3-4: Tables with capacity up to 1.5× party size are considered
- Party size 5-8: Tables with capacity up to 1.3× party size are considered
- Party size 9+: Tables with capacity up to 1.2× party size are considered

For party sizes not explicitly defined, the system uses the closest smaller size in the configuration.


## Booking States
Bookings in the system follow a well-defined lifecycle with the following states:

```mermaid
stateDiagram-v2
    [*] --> pending: Booking Request
    pending --> open: Validation Success
    pending --> cancelled: Manual/Timeout
    pending --> error: System Error
    open --> success: Customer Check-In
    open --> noshow: Grace Period Expired
    open --> cancelled: Manual Cancellation
    open --> error: System Error
    [*] --> reserved: Staff Table Closure
    reserved --> ended: Staff Table Opening
    success --> ended: Check-Out / Dining Complete
    success --> error: System Error
    cancelled --> [*]
    ended --> [*]
    noshow --> [*]
    error --> [*]
```

1. **Pending**
   - The booking request is initiated and resources are temporarily held.
   - Transitions to: **open** (if validated), **cancelled** (if timed out or manually cancelled), or **error** (on failure).

2. **Open**
   - The booking has been validated and is active, awaiting customer check-in.
   - Transitions to: **success** (upon check-in), **noshow** (if no check-in within the grace period), **cancelled**, or **error**.

3. **Success**
   - The customer has checked in and the booking is in use.
   - Transitions to: **ended** (after completion) or **error**.

4. **Reserved**
   - The table is reserved by staff using the `staffCloseTable` function.
   - Used for maintenance, special events, or other staff-initiated closures.
   - Transitions to: **ended** (when staff opens the table).

5. **Ended**
   - The booking is completed successfully; the dining experience is finished.

6. **Cancelled**
   - The booking was cancelled either manually or due to a timeout.

7. **No-show**
   - The customer did not check in within the grace period (default: 40 minutes after booking start time).
   - Triggers queue integration if enabled.
   - Controlled by the `gracePeriod` setting.

7. **Error**
   - An unexpected error occurred during the booking process.


### State Transition Rules
- Only confirmed bookings can transition to completed or no-show
- Cancellations can occur at any point before completion
- No-shows are automatically detected when customers don't check in within the grace period (default: 40 minutes)
- Tables with `reserved` status (created via `staffCloseTable`) can only be released by staff using `staffOpenTable`
- System maintains audit trail of all state transitions

### Impact on Resource Management
- Resources are temporarily locked in pending state
- Confirmed bookings block resources for the scheduled duration
- Tables with `reserved` status block resources for staff-defined periods (e.g., maintenance)
- Final states (completed, cancelled, no-show) release resources
- Queue system is notified of resource availability changes
- Timezone-aware operations ensure accurate resource allocation across time zones
- Error handling includes automatic rollback of resource allocations on failure

### Resource Management Implementation

The Resource model implements several key methods for table management:

```javascript
/**
 * Find available resources (in ascending order of capacity)
 * @param {String} placeId - Place ID
 * @param {String} kind - Kind of resource (e.g., 'TABLE')
 * @param {Number} capacity - Party size
 * @param {Date} at - Start time
 * @param {Number} duration - Duration in minutes
 * @param {Boolean} combined - Whether to allow combined tables
 * @param {Boolean} includeOverstay - Whether to include overstaying bookings check
 * @return {Resource[]} List of available resources
 */
Resource.available = async function(placeId, kind, capacity, at, duration, combined, includeOverstay) {
  // 1. Find candidate resources that match capacity
  // 2. Shortlist resources that are available at the specified time for the duration
  // 3. Return available resources in ascending order of capacity
}

/**
 * Generate and score table candidates
 * @param {Resource[]} available - List of available resources
 * @param {Number} partySize - Number of people
 * @param {Object} preferences - Customer preferences
 * @return {Object[]} Scored candidates in descending order of score
 */
Resource.candidates = function(available, partySize, preferences) {
  // 1. Generate single table candidates
  // 2. Generate combined table candidates if needed
  // 3. Score candidates based on capacity fit, adjacency, etc.
  // 4. Return candidates in descending order of score
}

/**
 * Assign resources and create booking records
 * @param {Object[]} candidates - Scored resource candidates
 * @param {Date} from - Start time
 * @param {Date} to - End time
 * @param {Number} partySize - Number of people
 * @param {Object} customer - Customer information
 * @param {Object} preferences - Customer preferences
 * @param {Object} options - Additional options
 * @return {Object[]} Assigned resources with booking details
 */
Resource.assign = async function(candidates, from, to, partySize, customer, preferences, options) {
  // 1. Select the best candidate(s)
  // 2. Create booking records
  // 3. Return assigned resources with booking details
}
```

These methods work together to provide a robust resource management system for table bookings.


## Atomic Booking Process
The table booking system implements an atomic booking process to ensure reliable resource allocation and booking confirmation.

Conceptually, this process can be thought of as involving resource selection/validation and subsequent confirmation; however, in practice, all these steps occur within a single, unified operation.

```mermaid
sequenceDiagram
    participant C as Client
    box Product Service
        participant P as Table Booking
        participant R as Resource
    end
    box Sales Service
        participant B as Booking
    end

    Note over C,B: Single Request: Resource Selection, Validation & Confirmation
    C->>P: requestTableBookings()
    activate P
    Note over P: Validate Request
    P->>P: validateTimeWindow() & validatePartySize()
    P->>R: available()
    R->>B: getExistingBookings()
    B-->>R: booked time slots
    R-->>P: available tables

    Note over P: Resource Selection
    P->>R: candidates()
    R-->>P: prioritized candidates

    Note over P: Resource Assignment & Internal Confirmation
    P->>R: assign()
    R->>B: createBooking()
    B-->>R: booking record
    R-->>P: assigned tables & booking details
    P-->>C: booking response (Booking Confirmed)
    deactivate P
```

The process consists of the following internal steps within the same request:

1. **Validation**:
   - The request is validated for time window, party size, and business hours.

2. **Resource Selection**:
   - Available tables are retrieved, and candidate tables are identified, scored, and prioritized using capacity optimization and isolation preference.

3. **Assignment & Confirmation**:
   - Tables are assigned and the booking is internally confirmed; the booking response is then returned to the client.

This design ensures atomic booking operations while maintaining consistent state management and reliable resource allocation, without requiring a separate client-side confirmation call.

**Note on `reservationId`**: If the resource assignment step results in multiple tables being booked (e.g., combining tables), the system generates multiple `Booking` records. Each of these records will share the same `reservationId`, effectively linking all parts of the single, original reservation request together.


## Smart Release and Reassign
The system implements a Smart Release and Reassign approach for handling customers with reservations at check-in time:

```mermaid
graph TD
    A[Customer with Reservation Checks In] --> B{Original Tables Available?}
    B -->|Yes| C[Seat at Original Tables]
    B -->|No| D[Release Original Tables]
    D --> E[Search for Alternative Tables]
    E --> F{Alternatives Available?}
    F -->|Yes| G[Seat at Alternative Tables]
    F -->|No| H[Queue with High Priority]
```

### Process Flow
1. **Check Original Tables**: When a customer with a reservation checks in, the system first checks if their originally reserved tables are currently available.
2. **If Original Tables Available**: The customer is seated at their originally reserved tables, and the booking status is updated to reflect check-in.
3. **If Original Tables Unavailable**:
   - The original booking is canceled to release those tables back to the available pool
   - The system immediately searches for alternative tables that can accommodate the party
4. **If Alternative Tables Available**: The customer is seated at these alternative tables with new booking records created
5. **If No Tables Available**: The customer is placed in the queue with ASAP priority status

### Benefits
- Eliminates "zombie resources" where tables remain blocked by bookings but aren't being used
- Customers with reservations maintain priority status
- Overall table utilization is maximized
- Provides a clear, deterministic system behavior

For detailed implementation, see [Smart Release and Reassign](./table-queuing.md#smart-release-and-reassign-process).


## Integrations

### Table Queuing Integration
The Queue Integration provides seamless handling of table requests when immediate seating is not available:

```mermaid
graph TD
    A[Table Booking System] -->|No Table Available| B[Queue System]
    B -->|Join Queue| C[Queue Entry Creation]
    C -->|Calculate Priority| D[Priority Assignment]
    D -->|Schedule Time| E[Time Allocation]

    subgraph Queue Processing
        F[Process Queue]
        G[Resource Monitor]
        H[Assignment Check]
    end

    E --> F
    G -->|Available Tables| H
    F -->|Next Entry| H
    H -->|Success| I[Table Assignment]
    H -->|Retry| F
```

1. **Queue Entry Creation**
   - Automatic queue entry creation when tables unavailable
   - Configurable validity period for queue entries
   - Support for walk-ins and booking conversions
   - Customer preferences tracking (combined tables, adjacent only)

2. **Priority Management**
   - Dynamic priority calculation based on:
     - Customer tier
     - Wait time
     - Party size
   - Periodic priority updates for waiting entries
   - Fair scheduling based on priority and creation time

3. **Resource Processing**
   - Continuous monitoring of table availability
   - Sequential processing to prevent race conditions
   - Configurable processing intervals
   - Automatic retry on failed assignments

4. **Time Management**
   - Scheduled time calculation for each entry
   - Consideration of existing queue entries
   - Resource timing simulation for accurate scheduling
   - Minimum duration enforcement from booking settings

5. **Integration Features**
   - Real-time resource state synchronization
   - Automatic processing on booking completion/cancellation
   - Event-driven architecture for status updates
   - Error handling with graceful degradation

The system ensures efficient table utilization while maintaining fair queue processing based on customer priority and wait time.

For detailed queue management implementation, see [Table Queuing](./table-queuing.md).

### Table Relocation Integration
The Table Relocation feature integrates with the Table Booking system to enable staff to move customers between tables while maintaining order history:

```mermaid
graph TD
    A[Table Booking System] -->|Active Reservation| B[Table Relocation]
    B -->|Create New Bookings| C[New Table Bookings]
    B -->|Relocate Orders| D[Order Relocation]
    B -->|End Original Bookings| E[Release Original Tables]
    C -->|Preserve Metadata| F[Maintain Reservation Context]
    E -->|Update Status| G[Mark as ENDED]
```

1. **Reservation-Centric Approach**
   - Uses the reservation as the primary entity for relocation operations
   - Maintains relationships between all tables in the same reservation
   - Preserves booking metadata (party size, notes, etc.) during relocation

2. **Partial Relocation Support**
   - Allows relocating only selected tables from a multi-table reservation
   - Maintains reservation integrity across all related tables
   - Supports different party configurations while preserving the overall reservation

3. **Booking Status Management**
   - Creates new bookings with `SUCCESS` status for destination tables
   - Updates original bookings to `ENDED` status after successful relocation
   - Preserves reservation ID across all related bookings

For detailed implementation, see [Table Relocation](./table-relocation.md).

### Calendar Integration
Table bookings are automatically synchronized with Google Calendar:

- Each table resource has a dedicated calendar
- Bookings create calendar events with customer details
- Events include party size and contact information
- Rate-limited to prevent API throttling (3 requests per 3 seconds)
- Multiple bookings for the same customer at the same time are aggregated

For more details, see [Calendar Integration](./resources.md#calendar-integration).

## Date Handling Best Practices

The table booking system uses dayjs for timezone-aware date handling. Follow these best practices for consistent date operations:

```javascript
// Create timezone-aware dates
const { timeZone } = app.getSettings(LOCALE);
const nowInTz = dayjs().tz(timeZone);
const fromInTz = dayjs(from).tz(timeZone);

// Perform date calculations in the timezone context
const toInTz = fromInTz.add(minDuration, 'minutes');

// Convert to native Date objects only when needed for database operations
const fromDate = fromInTz.toDate();
const toDate = toInTz.toDate();
```

Key principles:
- Always create timezone-aware dates with `dayjs().tz(timeZone)`
- Perform all date calculations using dayjs methods
- Convert to native Date objects only when needed for database operations
- Maintain timezone context throughout the booking process
- Avoid redundant conversions between dayjs and native Date objects

## API Endpoints
See [API Documentation](./API.md) for details.

#### Table Booking Widget APIs
- `GET /Products/app/booking/tables/availability` - Check table availability
- `POST /Products/app/booking/tables` - Book tables for member

#### Table Booking Website APIs
- `GET /Products/web/booking/tables/availability` - Get table availability matrix for place
- `POST /Products/web/booking/tables` - Book tables for non-member

#### Deposit Handling
Booking APIs support deposit handling with the following parameters:
- `deposit` - Whether deposit is required
- `deposit.value` - Deposit amount
- `deposit.currency` - Currency code

Deposit requirements are determined based on:
- Customer tier
- Party size
- Time of booking (peak hours may require deposits)
- Place-specific settings

#### Error Handling
The booking system implements comprehensive error handling:

| Error Code | Description | Cause |
|------------|-------------|-------|
| `NOT_ENABLED` | Table booking is disabled | `enabled` setting is false |
| `INVALID_TIME_WINDOW` | Invalid booking time | Outside lead time or advance booking window |
| `INVALID_PARTY_SIZE` | Invalid party size | Outside min/max party size limits |
| `BOOKING_UNAVAILABLE` | No tables available | No suitable tables found for the requested time |
| `CAPACITY_EXCEEDED` | Party size too large | Exceeds maximum party size |

All errors include appropriate HTTP status codes and detailed error messages.


## Table-Specific Settings

Configured under the `table` key in the `booking` settings:

| Setting | Description | Type | Default |
|---------|-------------|------|---------|
| `enabled` | Whether table booking is enabled | `boolean` | `true` |
| `minSize` | Smallest table size (capacity) | `number` | `2` |
| `maxSize` | Largest table size (capacity) | `number` | `12` |
| `minPartySize` | Minimum party size for table combinations | `number` | `1` |
| `maxPartySize` | Maximum party size allowed | `number` | `20` |
| `minDuration` | Minimum dining duration in minutes | `number` | `90` |
| `maxDuration` | Maximum dining duration in minutes | `number` | `180` |
| `maxCombined` | Maximum number of tables that can be combined | `number` | `3` |
| `minCapacity` | Minimum capacity ratio (party size multiplier) | `number` | `0.7` |
| `maxCapacity` | Maximum capacity ratio (party size multiplier) or tiered configuration | `number` or `object` | `1.5` |
| `leadTime` | Minimum advance notice required for bookings | `number` | - |
| `maxAdvanceBooking` | Maximum days in advance for bookings | `number` | - |
| `holdTime` | Time in minutes to hold pending bookings before timeout | `number` | `10` |
| `gracePeriod` | Time in minutes after booking start time before marking as no-show | `number` | `40` |
| `timeSlotInterval` | Interval between available time slots | `number` | - |
| `earlyCheckinTime` | Time in minutes before booking start time to allow early check-in | `number` | `15` |
| `allowCombined` | Whether to allow combining multiple tables for larger parties | `boolean` | `true` |
| `adjacentOnly` | Whether combined tables must be physically adjacent | `boolean` | `true` |
