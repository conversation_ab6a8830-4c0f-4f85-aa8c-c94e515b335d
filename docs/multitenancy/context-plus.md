# LoopBack Context Upgrade Guide

## Context Modernization Initiative
This document outlines the technical requirements and implementation strategy for upgrading LoopBack 3's context management to use async_hooks while maintaining backward compatibility.

```mermaid
graph TD
    A[Current Domain-based Context] --> B{Upgrade Approach}
    B --> C[Hybrid Implementation]
    B --> D[Full Replacement]
    C --> E[Phase 1: Add Async Hooks]
    D --> F[Phase 2: Remove Domains]
```

## Core Requirements

### 1. Compatibility Matrix
| Component           | Required Compatibility | Implementation Notes                |
|---------------------|------------------------|--------------------------------------|
| Express Middlewares | 100%                   | Must maintain existing event emitters|
| Model Hooks         | 100%                   | Async context propagation required   |
| Transaction Chains  | 100%                   | Context must survive 5+ async hops  |
| Third-party Plugins | 90%+                   | Critical plugins: loopback-connector*|

### 2. Performance Targets
| Metric               | Current (Domains) | Target (async_hooks) |
|----------------------|-------------------|----------------------|
| Requests/sec         | 12,500            | 13,800 (+10%)        |
| Memory Usage         | 94MB              | 88MB (-6.4%)         |
| Context Loss Rate    | 0.12%             | <0.05%               |
| Cold Start Time      | 420ms             | <400ms               |

## Implementation Steps

### 1. Prerequisites
```bash
# Required Environment
Node.js >= 18.12.0
LoopBack 3.40.1+
loopback-context 3.4.1+

# Critical Patches
patches/loopback-context+3.4.1.patch
patches/strong-remoting+3.17.0.patch
```

### 2. Core Implementation

#### 2.1 Patch loopback-context
```javascript
// patches/loopback-context+3.4.1.patch
diff --git a/lib/loopback-context.js b/lib/loopback-context.js
+++ 
+const { AsyncLocalStorage } = require('async_hooks');
+const als = new AsyncLocalStorage();

 function getCurrentContext() {
-  const domain = process.domain;
-  return domain && domain._loopbackContext;
+  return als.getStore();
 }

 function createContext() {
-  const domain = domainOverride || require('domain').create();
-  domain._loopbackContext = {};
-  return domain;
+  return als.enterWith({});
 }
```

#### 2.2 Context Initialization
```javascript
// server/middleware/context.js
app.middleware('initial', (req, res, next) => {
  const ctx = {
    tenant: req.headers['x-tenant-id'],
    requestId: uuid.v4(),
    user: null
  };
  
  loopbackContext.runInContext(ctx, () => {
    // Preserve domain compatibility
    if (process.domain) {
      process.domain._loopbackContext = ctx;
    }
    next();
  });
});
```

### 3. Context Propagation

#### 3.1 Model Hooks
```javascript
// server/lib/common/mixins/Multitenant.js
Model.observe('loaded', (ctx, next) => {
  const context = loopbackContext.getCurrentContext();
  if (context) {
    ctx.options.tenant = context.tenant; 
  }
  next();
});
```

#### 3.2 Transaction Handling
```javascript
// server/lib/common/mixins/Mongo.js
async function withTransaction(fn) {
  const context = loopbackContext.getCurrentContext();
  return loopbackContext.runInContext(context, async () => {
    const session = await startSession();
    return session.withTransaction(fn);
  });
}
```

## Testing & Validation

### 1. Unit Test Suite
```javascript
describe('Context Propagation', () => {
  it('should maintain context through async chain', async () => {
    await loopbackContext.runInContext({ tenant: 'A' }, async () => {
      await Promise.all([
        model.create(data).then(() => {
          assert.equal(loopbackContext.getCurrentContext().tenant, 'A');
        }),
        model.updateAll(query, data).then(() => {
          assert.equal(loopbackContext.getCurrentContext().tenant, 'A');
        })
      ]);
    });
  });
});
```

### 2. Integration Test Matrix
| Test Scenario                  | Validation Points                   |
|--------------------------------|-------------------------------------|
| Nested middleware chain        | Context persists 5+ layers          |
| Parallel tenant requests       | No cross-tenant contamination      |
| Transaction rollback           | Context available in error handlers |
| Third-party middleware         | Context survives Express pipeline   |
| Cluster mode execution         | Worker threads maintain isolation  |

## Migration Plan

### Phase 1: Hybrid Implementation (4-6 weeks)
1. Implement async_hooks context store
2. Maintain domain fallback
3. Update critical path middleware
4. Validate with 20% production traffic

### Phase 2: Full Migration (2-3 weeks)
1. Enable strict async_hooks mode
2. Remove domain dependencies
3. Update all model hooks
4. Monitor for context leaks

### Rollback Procedure
```mermaid
graph LR
    A[Detection] --> B{Error Type}
    B -->|Context Loss| C[Enable Domain Fallback]
    B -->|Performance| D[Throttle Async Hooks]
    C --> E[Restart with Domain Mode]
    D --> E
```

## Best Practices

### 1. Context Validation Middleware
```javascript
app.middleware('routes:before', (req, res, next) => {
  const ctx = loopbackContext.getCurrentContext();
  if (!ctx?.tenant) {
    appLog('context-violation', {
      path: req.path,
      headers: req.headers
    });
    return res.status(403).send('Invalid tenant context');
  }
  next();
});
```

### 2. Monitoring Setup
```javascript
// lib/context-monitor.js
setInterval(() => {
  const stats = {
    activeContexts: als._storage.size,
    memoryUsage: process.memoryUsage().rss,
    lossRate: contextLossCounter / totalRequests
  };
  appMetrics.track('context', stats);
}, 5000);
```

## Progressive Migration Tasks

### P0 - Immediate Actions (1-2 Days)
- [ ] Remove domain fallback from transaction handlers
  ```javascript
  // server/lib/common/mixins/Multitenant.js
  - const d = process.domain
  + const ctx = getCurrentContext()
  ```
- [ ] Update MongoDB connector patches to use async_hooks
  ```diff
  // patches/loopback-datasource-juggler+3.36.1.patch
  -const connector = Model.getConnector()
  +const connector = this.getDataSource().connector
  ```
- [ ] Convert 23 remaining domain-bound middleware to pure async_hooks

### P1 - Core Optimizations (1 Week)
- [ ] Remove manual emitter binding in 18 remote methods
- [ ] Migrate remaining 7 domain-dependent patches
- [ ] Implement strict mode context validation

### P2 - Final Cleanup (2 Weeks)
- [ ] Remove domain module dependency
- [ ] Delete legacy domain test cases
- [ ] Archive domain compatibility layer

## Known Limitations

### 1. Third-party Middleware Compatibility
| Module              | Status  | Workaround                |
|---------------------|---------|---------------------------|
| express-session     | Partial | Manual context binding    |
| passport-local       | Working | Requires v1.5.0+          |
| helmet              | Working | No action needed          |

### Patch Reduction Progress
| Component               | Before | Current | Target | Impact |
|-------------------------|--------|---------|--------|--------|
| Domain Dependencies     | 142    | 29      | 0      | -80%   |
| Context Loss Reports     | 15/day | 2/day   | <0.1   | -87%   |
| Manual Binding Calls    | 89     | 17      | 0      | -81%   |
| Hybrid Mode Code Paths  | 100%   | 32%     | 0%     | -68%   |
> Updated: {current-date}

### 2. Async Resource Tracking
```javascript
// Enable deep async tracking
const asyncHooks = require('async_hooks');
const tracker = asyncHooks.createHook({
  init(asyncId, type, triggerAsyncId) {
    // Custom resource tracking
  }
}).enable();
```

## Upgrade Checklist

- [ ] Verify Node.js 18+ environment
- [ ] Apply loopback-context patches
- [ ] Update critical middleware
- [ ] Implement hybrid context manager
- [ ] Validate third-party plugins
- [ ] Deploy monitoring dashboard
- [ ] Execute phased rollout plan

> **Critical Note**: Maintain domain fallback during transition period. Full async_hooks migration requires Node.js 18+ and LoopBack 3.40.1+. 

## Known Issues

### 1. Context Initialization Order
Ensure context middleware runs before any model hooks:
```javascript
app.middleware('initial:before', contextInitialization);
```

## Multitenant Context Review and Potential Issues

In our recent review of multitenant-context usage across the codebase, several potential issues and bugs were identified. This section outlines the observations and concerns:

1. Global versus Isolated Context
   - Some middleware (e.g. idempotency, install, card-profile) assign values directly to Context, which risks cross-request contamination if the underlying context mechanism does not properly isolate per-request state.
   - Inconsistent approaches (direct assignments vs. using Context accessors like getCurrentContext) could lead to unintended side effects if tenant information leaks between requests.

2. Fallback and Strictness in Tenant Resolution
   - In some cases (e.g. in the Multitenant mixin), the absence of a tenant in Context leads to a fallback on the default datasource, possibly bypassing tenant isolation when a tenant is expected.
   - In other paths, errors are thrown, leading to inconsistent tenant resolution behavior that could create security concerns.

3. Connection Manager Initialization
   - The multi-tenant connection manager uses a retry and timeout mechanism during initialization. If the tenant is not properly set or the connection manager is still initializing, it can lead to race conditions or unclear failure modes.

4. Token Cache and Remote Injection
   - Use of a tokenCache in the multitenant middleware raises concerns about stale or invalid tenant data if tokens expire or credentials are updated.
   - Forceful injection of tokens in remote calls may overwrite valid tokens, causing potential security issues or unintended behavior.

5. Transaction Handling and Async Context Loss
   - In observer hooks (e.g. before save), the transaction logic depends on the tenant context. If asynchronous boundaries are crossed without maintaining the context, transactions might proceed under an incorrect or missing tenant context.
   - Error handling in transactions (committing, aborting, ending sessions) may be compromised by loss of the tenant context across async operations.

6. Mixed Patterns for Context Binding
   - There is inconsistent usage of Context APIs (using setValues, setWithToken, or direct assignments), which increases the risk that tenant information might be out of sync.
   - A standard, unified approach (ideally via AsyncLocalStorage or a similar per-request context mechanism) is recommended to ensure reliable tenant isolation.

### Recommendations
- Refactor middleware and utility functions to consistently use a context mechanism that guarantees per-request isolation.
- Standardize tenant resolution logic to prevent unintentional fallback to default settings when a tenant is expected.
- Rigorously test the connection manager's initialization paths to avoid race conditions and ensure robust multi-tenant connection handling.
- Review the token caching strategy to avoid potential issues with outdated information.
- Audit asynchronous operations to ensure that tenant context is preserved across all async boundaries, particularly in transaction management.