# Multi-tenancy Architecture

## Implementation Overview
Our CRM platform implements strict tenant isolation through a layered approach combining database-level separation and context-aware connection management. The system supports 150+ concurrent tenants with plans to scale to 500+.

```mermaid
graph TD
    subgraph Tenant Isolation
        DB[(MongoDB Databases)] -->|1:1 Mapping| T1[Tenant 1]
        DB -->|Database per Tenant| T2[Tenant 2]
        DB --> T3[Tenant N]
    end
    
    subgraph Context Propagation
        CTX[Request Context] -->|Async Hooks| S1[Service 1]
        CTX --> S2[Service 2]
        CTX --> SN[Service N]
    end
    
    subgraph Connection Management
        CM[Connection Manager] -->|Pool per Tenant| P1[Pool 1]
        CM --> P2[Pool 2]
        CM --> PN[Pool N]
    end
    
    classDef tenant fill:#1a365d,stroke:#333,stroke-width:2px,color:#fff
    classDef context fill:#b7791f,stroke:#333,stroke-width:2px,color:#fff
    classDef connection fill:#1e40af,stroke:#333,stroke-width:2px,color:#fff
    
    class T1,T2,T3 tenant
    class CTX context
    class CM,P1,P2,PN connection
```

## Core Components

### 1. Context Management
```javascript
// Current Implementation (Legacy)
const LoopBackContext = require('loopback-context');
const ctx = LoopBackContext.getCurrentContext();
const tenant = ctx.get('tenant-code');

// Target Implementation
const { AsyncLocalStorage } = require('async_hooks');
const contextStorage = new AsyncLocalStorage();

// In middleware
app.use((req, res, next) => {
  contextStorage.run(new Map(), () => {
    const store = contextStorage.getStore();
    store.set('tenant', detectTenant(req));
    next();
  });
});
```

**Key Features:**
- Tenant context propagation through async operations
- Automatic injection in model hooks
- Request-level isolation

**Risk Mitigation:**
- P0: Migrate from domains to AsyncLocalStorage
- Temporary workaround: Limit async hops to 3 levels

### 2. Connection Management
```javascript
// Connection pool management
const tenantPools = new Map();

function getPool(tenant) {
  if (!tenantPools.has(tenant)) {
    tenantPools.set(tenant, new ConnectionPool({
      min: 2,
      max: 10,
      idleTimeout: 30000
    }));
  }
  return tenantPools.get(tenant);
}

// Transaction handling with retries
async function withRetry(fn, maxAttempts = 3) {
  let attempt = 0;
  while (attempt++ < maxAttempts) {
    try {
      return await fn();
    } catch(err) {
      if (err.errorLabels?.includes('TransientTransactionError')) {
        await sleep(50 * attempt);
        continue;
      }
      throw err;
    }
  }
}
```

**Performance Metrics:**
| Tenant Count | Avg Connection Setup Time | Max Pool Size | Error Rate |
|--------------|----------------------------|---------------|------------|
| 50           | 120ms                      | 8             | 0.2%       |
| 150          | 250ms                      | 10            | 1.8%       |
| 200          | 420ms                      | 12            | 4.5%       |

### 3. Data Isolation
```javascript
// Query validation hook
Model.observe('access', (ctx) => {
  const tenant = ctx.Model.getTenant();
  if (!ctx.query.where.tenant) {
    ctx.query.where.tenant = tenant;
  } else if (ctx.query.where.tenant !== tenant) {
    throw new Error('Cross-tenant query attempt');
  }
});

// Document structure with tenant code
{
  _id: ObjectId,
  tenant: 'TENANT-CODE',
  // ... other fields
}
```

**Isolation Layers:**
1. Database-level separation
2. Collection-level tenant codes
3. Query pre-processor hooks
4. Session-bound transactions

## Security Implementation

### Authentication Flow
```mermaid
sequenceDiagram
    participant Client
    participant Gateway
    participant AuthService
    participant TenantDB
    
    Client->>Gateway: Request (JWT/Code)
    Gateway->>AuthService: Validate credentials
    AuthService->>TenantDB: Verify tenant status
    TenantDB-->>AuthService: Tenant config
    AuthService->>Gateway: Issue signed token
    Gateway->>Client: Return access token
    Note over Client,Gateway: Token contains tenant context
```

### Security Controls
```javascript
// HMAC-signed internal tokens
const crypto = require('crypto');
const SECRET = process.env.API_SECRET;

function generateAccessToken() {
  return crypto.createHmac('sha256', SECRET)
    .update(Date.now().toString())
    .digest('hex');
}

// Usage in middleware
app.use((req, res, next) => {
  const token = req.headers['x-access-token'];
  if (!validateToken(token)) {
    return res.status(401).send('Invalid token');
  }
  next();
});
```

**Audit Controls:**
1. Monthly token rotation
2. Session activity monitoring
3. Cross-tenant access alerts

## Monitoring & Observability

### Key Metrics
| Metric                  | Alert Threshold | Measurement Method |
|-------------------------|-----------------|---------------------|
| Context Leaks           | >0.1%          | Async hook tracing |
| Pool Exhaustion         | >85% capacity  | MongoDB driver metrics |
| Cross-tenant Queries    | Any occurrence | Query pre-processor |
| Token Validation Errors | >5%/min        | API Gateway logs |

### Health Checks
```javascript
// Custom health check endpoint
app.get('/tenant-health', (req, res) => {
  const health = {
    status: 'OK',
    connections: connectionManager.stats(),
    context: contextStorage.getStore()?.size || 0,
    lastUpdated: Date.now()
  };
  res.json(health);
});
```

## Risk Mitigation Roadmap

### Critical Improvements
```mermaid
gantt
    title Multitenancy Improvement Timeline
    dateFormat  YYYY-MM-DD
    section Context
    Migrate to AsyncLocalStorage   :active, ctx1, 2024-01-01, 14d
    Update Middleware              :         ctx2, after ctx1, 7d
    section Security
    Implement HMAC Tokens          :active, sec1, 2024-01-10, 7d
    Rotate Existing Secrets        :         sec2, after sec1, 3d
    section Performance
    Connection Pool Optimization   :         perf1, 2024-01-15, 10d
    Query Validation Layer          :         perf2, after perf1, 5d
```

### Priority Matrix
| Priority | Component           | Action Items                              | Owner       |
|----------|---------------------|-------------------------------------------|-------------|
| P0       | Context Management  | 1. AsyncLocalStorage migration<br>2. Context validation tests | Core Team   |
| P1       | Security            | 1. HMAC implementation<br>2. Token rotation automation | Security Team |
| P1       | Transactions        | 1. Retry wrapper<br>2. Transient error monitoring | DB Team      |
| P2       | Performance         | 1. Pool sizing algorithm<br>2. Connection warmup    | DevOps       |

## Best Practices

1. **Context Propagation**
```javascript
// Proper async context handling
async function tenantAwareOperation() {
  return contextStorage.getStore()?.get('tenant') || 'default';
}

// Avoid
function riskyOperation() {
  setTimeout(() => {
    // Loses context!
  }, 100);
}
```

2. **Connection Management**
```javascript
// Recommended pool settings
const optimalPoolConfig = {
  min: Math.floor(MAX_CONNECTIONS * 0.2),
  max: Math.floor(MAX_CONNECTIONS * 0.7),
  acquireTimeoutMillis: 30000,
  idleTimeoutMillis: 60000
};
```

3. **Error Handling**
```javascript
// Tenant-aware error logging
function logError(error) {
  const tenant = contextStorage.getStore()?.get('tenant') || 'unknown';
  logger.error(`[${tenant}] ${error.message}`, {
    stack: error.stack,
    code: error.code
  });
}
```

## Migration Strategy

### Phase 1: Context Management
1. Implement AsyncLocalStorage prototype
2. Update critical middleware
3. Parallel run with legacy system
4. Full cutover after 2-week observation

### Phase 2: Security Enhancements
1. Rotate all existing tokens
2. Deploy HMAC validation
3. Monitor failed auth attempts

### Phase 3: Performance Optimization
1. Implement tiered connection pools
2. Add warmup mechanism
3. Fine-tune based on load testing

> **Critical Note:** Always maintain backward compatibility during migration phases. Use feature flags to control new implementations. 