# Resource Management
The Resource Management system is part of the Product Service in the CRM platform.

## Table of Contents
1. [Overview](#overview)
2. [Resource Types](#resource-types)
3. [Resource Properties](#resource-properties)
4. [Resource Allocation](#resource-allocation)
5. [External Integration](#external-integration)
6. [Data Models](#data-models)
7. [API Endpoints](#api-endpoints)
8. [Events](#events)

## Overview
The system provides a flexible and extensible way to manage various types of business resources, from physical locations to people. It implements a sophisticated scheduling engine that handles complex resource allocation, real-time availability tracking, and dynamic capacity management.

```mermaid
graph TD
    subgraph "Resources"
        E[Event]
        T[Table]
        P[Person]
        V[Venue]
    end

    subgraph "Core Services"
        S[Scheduling Engine]
        Q[Queue Management]
        A[Resource Allocation]
    end

    GC[Google Calendar]
    SL[Shopify Locations]

    Resources --> S

    S <--> GC
    V <--> SL
    A --> SL
```

Key features include:
- **Multi-resource Support**: Manages diverse resource types through a consistent interface
- **Centralized Scheduling**: Unified scheduling engine for consistent handling and optimization
- **Queue Management**: Handles waiting lists and priority allocation
- **Real-time Operations**: Immediate availability updates and dynamic resource management
- **Time Zone Handling**: Proper handling of operating hours across different time zones

## Resource Types

### Event
- Time-bound activities with capacity limits
- Scheduling constraints:
  - Fixed start and end times
  - Recurring event patterns
  - Capacity-based availability
  - Lead time requirements
  - Cleanup time between events

### Venue
- Physical locations with operating hours
- Service capabilities and place association
- Capacity management:
  - Total venue capacity
  - Section-based capacity tracking
  - Multiple service area support
- Operating hours synchronization with Shopify
- Location mapping with external systems

### Table
- Seating capacity and position tracking
- Combination capabilities:
  - Adjacent table detection
  - Dynamic capacity calculation
  - Maximum combined tables limit
- Real-time availability tracking
- Position-based allocation optimization
- For details, see:
  - [Table Queuing](./table-queuing.md)
  - [Table Booking](./table-booking.md)

### Person
- Staff resources with availability schedules
- Service assignments:
  - Skill-based allocation
  - Service area restrictions
  - Maximum concurrent assignments
- Break management:
  - Required break durations
  - Break scheduling rules
  - Shift pattern support
- Calendar integration for availability

## Resource Properties
All resource properties are defined in the Data Models section under Resource entity. Key property groups include:

1. **Core Properties**
   - Basic identification (`kind`, `name`, `description`)
   - References (`productId`, `placeId`)
   - Position tracking (`position`, `adjacentIds`)

2. **Time Configuration**
   - Timezone settings (`timeZone`)
   - Operating hours (`hours`)
   - Availability period (`startTime`, `endTime`)
   - Buffer periods (`interval`, `leadTime`, `cleanupTime`)

3. **Capacity Management**
   - Maximum limits (`capacity`)
   - Resource sharing (`shared`)
   - Combination limits (`maxCombined`)

4. **Integration Properties**
   - Calendar integration (`calendarId`)
   - External system mappings

For detailed property specifications, see [Data Models > Resource](#data-models).

## Resource Allocation
The system implements a sophisticated allocation strategy handling both immediate and queued requests.

### Adjacency Management
The system validates resource combinations using a Union-Find algorithm with path compression:

- **Adjacency Definition**: Resources maintain an `adjacentIds` property defining neighbors
- **Connection Validation**: Valid combinations must form a single connected component
- **Efficient Processing**: Uses path compression optimization for improved find operations
- **Timezone Consistency**: Combined resources must share the same timezone

### Capacity Optimization
Resource combinations must meet configured capacity ratios:

- **Minimum Capacity**: `Requested Amount × minCapacity` (default: 0.7)
- **Maximum Capacity**: `Requested Amount × maxCapacity` (default: 1.5)
- **Validation Process**:
  1. Calculate total capacity of candidate resources
  2. Reject combinations outside thresholds
  3. Prioritize combinations nearest to requested amount
  4. Only consider combinations if party size >= minSize

### Scoring System

The system scores resource combinations using weighted formulas optimized for different scenarios:

#### Immediate Seating
```typescript
Score = (Capacity Fit × 0.75) + (Adjacency × 0.25)
```

| Component | Weight | Description |
|-----------|--------|-------------|
| Capacity Fit | 75% | Strict capacity matching with thresholds. Rejects if too small, heavily penalizes if >150% of party size. Perfect match = 1.0, linear penalty for deviation. |
| Adjacency | 25% | Bonus for physically adjacent resources |

#### Queued Assignment
```typescript
Score = (Utilization × 0.70) + (Adjacency × 0.20) - (Multiple Resource Penalty × 0.10)
```

| Component | Weight | Description |
|-----------|--------|-------------|
| Utilization | 70% | Flexible capacity matching. Score decreases symmetrically with capacity difference (both under/over). No hard thresholds. |
| Adjacency | 20% | Bonus for physically adjacent resources |
| Multiple Resource Penalty | 10% | Penalty per additional resource |

#### Isolation Preference

For available single tables, the system applies an isolation preference as a secondary sorting criteria using a dual-factor scoring system:

```typescript
Isolation Score = 1 - (Occupied Penalty) - (Adjacency Penalty)
where:
  Occupied Penalty = (Occupied Adjacent Tables / Total Adjacent Tables)
  Adjacency Penalty = (Total Adjacent Tables × 0.1)
```

| Scenario | Occupied Penalty | Adjacency Penalty | Final Score | Priority |
|----------|------------------|-------------------|-------------|----------|
| No adjacent tables | 0.0 | 0.0 | 1.0 | Highest (completely isolated) |
| 1 adjacent table (available) | 0.0 | 0.1 | 0.9 | High |
| 2 adjacent tables (available) | 0.0 | 0.2 | 0.8 | Medium-High |
| 1 adjacent table (occupied) | 1.0 | 0.1 | 0.0* | Lowest |
| 2 adjacent tables (1 occupied) | 0.5 | 0.2 | 0.3 | Low |

*Score is capped at 0.0 minimum

**Dual-Factor Benefits:**
- **Primary Goal - Customer Privacy**: Heavily penalizes tables adjacent to occupied ones (up to 100% penalty)
- **Secondary Goal - Combination Preservation**: Lightly penalizes tables with many adjacent tables (10% per adjacent table)
- **Strategic Flexibility**: Reserves adjacent table pairs/groups for larger parties that need combinations
- **Venue Distribution**: Encourages use of truly isolated tables when available

### Validation Rules
Determines resource availability and allocation based on configurable settings:

1. **Party Size**
   - Rejects combinations with party sizes outside configured limits:
     - Minimum party size: `minPartySize`
     - Maximum party size: `maxPartySize`

2. **Capacity Utilization**
   - Rejects combinations with utilization outside configured limits:
     - Minimum capacity utilization: `minCapacity`
     - Maximum capacity utilization: `maxCapacity`

3. **Resource Combination**
   - Maximum number of combinable resources: `maxCombined`
   - Resources define their adjacent resources through `adjacentIds` property
   - Adjacent resources must share same timezone (enforced)
   - When `adjacentOnly` is true:
     * Multi-resource combinations must form a connected group
     * Validated using Union-Find algorithm

## External Integration

### Calendar Integration (Google Calendar)
- One-way synchronization from system to calendar
- Dedicated calendars per resource
- Rate limiting controls:
  - 3 requests per 3 seconds per calendar
  - Batch processing for multiple updates
  - Event aggregation for identical bookings
- Calendar event management:
  - Resource name as calendar name
  - Resource description as event description
  - Availability status synchronization
  - Operating hours reflection
- Events factor into availability calculations

### Location Integration (Shopify)
- Venue and location mapping
- Availability status synchronization
- Operating hours synchronization
- Place association management
- Service area definitions

## Data Models
The key entities and their relationships:

```mermaid
erDiagram
    Resource ||--o{ Variant : "has"
    Resource ||--o{ Resource : "adjacent"
    Resource ||--|| Product : "belongs_to"
    Resource ||--|| Place : "belongs_to"
    Resource ||--o{ Queue : "has"
    Resource ||--o{ Order : "has"
    Resource ||--o{ Booking : "booked_by"
    Product ||--o{ Variant : "has"
    Variant ||--|| Place : "sold_at"
    Queue ||--|| Person : "belongs_to"
    Queue ||--|| Membership : "belongs_to"
    Queue ||--|| Place : "belongs_to"
    Queue ||--|| Booking : "belongs_to"
    Order ||--|| Resource : "uses"
    Order ||--|| Place : "belongs_to"
    Booking ||--|| Place : "belongs_to"
    Booking ||--|| Person : "belongs_to"
    Booking ||--|| Membership : "belongs_to"

    Resource {
        string kind
        string name
        string description
        array position
        string timeZone
        object hours
        date startTime
        date endTime
        number capacity
        boolean shared
        number interval
        number leadTime
        number maxCombined
        number cleanupTime
        string calendarId
        string productId
        string placeId
        array adjacentIds
    }

    Queue {
        string kind
        number capacity
        string tier
        number priority
        string status
        object timestamps
    }

    Order {
        string status
        date startTime
        date endTime
        number quantity
        string resourceId
    }

    Booking {
        string status
        date startTime
        date endTime
        number quantity
        string resourceId
        string placeId
        string personId
        string membershipId
        object preferences
        date confirmedAt
        date cancelledAt
    }
```

### Key Entities

#### Resource
The core entity representing any bookable asset in the system.

| Property | Description | Type | Unit |
|----------|-------------|------|------|
| `kind` | Type of resource (table, event, venue, person) | string | - |
| `name` | Display name of the resource | string | - |
| `description` | Detailed description of the resource | string | - |
| `position` | Location identifiers as key-value pairs for positioning | array | - |
| `timeZone` | IANA timezone identifier for the resource (e.g. 'Asia/Singapore') | string | - |
| `hours` | Operating hours configuration for each day of the week | object | - |
| `startTime` | Start of resource availability period | date | ISO 8601 |
| `endTime` | End of resource availability period | date | ISO 8601 |
| `capacity` | Maximum number of people/units the resource can accommodate | number | people/units |
| `shared` | Whether the resource can be shared between multiple bookings | boolean | - |
| `interval` | Minimum booking interval | number | minutes |
| `leadTime` | Minimum advance notice required for bookings | number | minutes |
| `maxCombined` | Maximum number of resources that can be combined | number | resources |
| `cleanupTime` | Required cleanup duration between bookings | number | minutes |
| `calendarId` | External calendar system reference ID | string | - |
| `adjacentIds` | Array of IDs for adjacent combinable resources | array | - |
| `productId` | Reference to associated Product | string | - |
| `placeId` | Reference to associated Place | string | - |

#### Queue
Represents a waiting list entry for a resource when immediate allocation isn't possible.

| Property | Description | Type | Unit |
|----------|-------------|------|------|
| `kind` | Type of queue entry (follow Resource kind) | string | - |
| `capacity` | Number of people/units requested for the resource | number | people/units |
| `tier` | Customer's membership tier affecting priority (e.g. VIP, REGULAR) | string | - |
| `priority` | Calculated queue priority score based on tier and wait time | number | score |
| `status` | Current queue entry status (PENDING, WAITING, ASSIGNED, NOTIFIED, etc.) | string | - |
| `preferences` | Customer preferences for resource allocation | object | - |
| `source` | Source of the queue entry (`walkin`, `noshow`, `overflow`) | string | - |
| `scheduledAt` | Time when the resource was scheduled | date | ISO 8601 |
| `timestamps` | Timing metadata including created, updated, notified times | object | ISO 8601 |

#### Booking
Represents a confirmed reservation of one or more resources.

| Property | Description | Type | Unit |
|----------|-------------|------|------|
| `status` | Booking status (CONFIRMED, CANCELLED, etc.) | string | - |
| `startTime` | Start time of the booking | date | ISO 8601 |
| `endTime` | End time of the booking | date | ISO 8601 |
| `quantity` | Number of units/people booked | number | units/people |
| `resourceId` | ID of the booked resource | string | - |
| `placeId` | ID of the place where booking is made | string | - |
| `personId` | ID of the person making the booking | string | - |
| `membershipId` | ID of the membership associated with booking | string | - |
| `preferences` | Booking preferences (e.g. seating preferences) | object | - |
| `confirmedAt` | Time when booking was confirmed | date | ISO 8601 |
| `cancelledAt` | Time when booking was cancelled (if applicable) | date | ISO 8601 |

### Entity Relationships

- **Resource Relationships**
  - Has many variants (service configurations)
  - Can be adjacent to other resources
  - Belongs to one product and place
  - Can have multiple queues

- **Queue Relationships**
  - Belongs to one person (customer)
  - Associated with one membership, place, and booking

## API Endpoints
For details, see [API Documentation](./API.md).

- `POST /Resources` – Create a new resource
- `GET /Resources/{id}` – Retrieve a resource by ID
- `PUT /Resources/{id}` – Update a resource
- `DELETE /Resources/{id}` – Delete a resource
- `GET /Resources` – Search for resources using filter queries

- `POST /Resources/availableby` – Find available resources by IDs and time period
- `POST /Resources/{id}/balance` – Get resource capacity balance for time period

- `GET /Resources/timings` – Retrieve resources of a given kind at a given place with their status (available, reserved-soon, occupied) and timing information
- `GET /Products/staff/tables/timings` – Staff API to retrieve table resources at a given place with their status and timing information


## Events
The Resource Management system integrates with Sales Service through the following events:

### Subscribed Events
   - `sales.booking.confirmed.reservation`: Update calendar for confirmed reservation (1 or more bookings)
   - `sales.booking.cancelled.reservation`: Update calendar for cancelled reservation (1 or more bookings)
