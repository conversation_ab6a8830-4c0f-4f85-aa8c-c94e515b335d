// orderPay
// file: crm/mixins/Buy.js
// function: Model.orderPay()

const stripe_card_deliver = {
	userId: '633d513be622cc001ba216f6',
	payments: [
		{
			provider: 'stripe',
			method: 'card',
			amount: 21.9,
			intent: {
				paymentMethodId: 'pm_1M0ScWLFbSgjvNHi8fhPGCDE',
				customerId: 'cus_MjwVqeiPQOIocQ'
			}
		}
	],
	order: {
		id: '6369ebac4b808400005ad86a',
		channel: 'shopify',
		items: [
			{
				variantId: '39731385892982',
				productId: '6741018607734',
				sku: 'HOMU-Adult-DarkBlue-BT',
				title: 'Epitex VIOGUARD HOMU Bath Towel - Dark Blue',
				quantity: 1,
				unitPrice: 16.9,
				price: 16.9,
				taxes: [],
				images: [
					'https://cdn.shopify.com/s/files/1/0271/8688/8822/products/Grey1_b27d489d-726b-4fc4-91b9-c6c4b6c96e9c.jpg?v=**********'
				],
				tags: [
					'11.11',
					'accessories',
					'addons',
					'best',
					'cny',
					'towel',
					'vio+',
					'all-products',
					'epitex'
				],
				properties: [],
				options: {},
				discount: {
					amount: 0,
					allocations: []
				},
				status: {}
			}
		],
		discounts: [],
		fulfillment: {
			quantity: 1,
			unitPrice: 5,
			price: 5,
			taxes: [],
			images: [],
			tags: [],
			properties: [],
			options: [],
			discount: {
				value: 0
			},
			type: 'deliver',
			recipient: {
				fullName: 'Zhang Li',
				phone: '6590490009',
				email: ''
			},
			destination: {
				id: 'l8wgd1mw',
				type: 'others',
				label: 'Ponggol Seventeenth Avenue',
				level: null,
				unit: null,
				street: '247 Ponggol Seventeenth Ave',
				city: 'Singapore',
				state: null,
				postCode: '829704',
				country: 'SG',
				formatted: '247 Ponggol Seventeenth Ave, Singapore 829704',
				short: '247 Ponggol Seventeenth Ave',
				geo: {
					type: 'Point',
					coordinates: [
						103.9039658,
						1.4162215
					]
				},
				optIn: null,
				valid: null
			},
			origin: {},
			scheduled: {
				minTime: '2022-11-13T05:39:45.000Z',
				maxTime: '2022-11-17T05:39:45.000Z'
			}
		},
		totalDiscounts: 0,
		subtotalPrice: 16.9,
		taxIncluded: true,
		taxes: [
			{
				title: 'GST',
				rate: 0.07,
				price: 1.44
			}
		],
		totalTax: 1.44,
		totalPrice: 21.9,
		currency: 'SGD',
		masterId: '62c8634a243ccd001f2fef71',
		options: {
			through: {
				type: 'perkd',
				format: 'shop',
				attributedTo: {
					type: 'shop'
				},
				touchedAt: '2022-11-08T05:39:56.079Z'
			},
			cardId: '63660c19b0fd053437324646'
		},
		cardId: '63660c19b0fd053437324646',
		idempotencyKey: '3a63c4c8e022'
	},
	pricings: [],
	options: {
		masterId: '62c8634a243ccd001f2fef71',
		cardId: '63660c19b0fd053437324646',
		description: 'Friend Card: product',
		through: {
			type: 'perkd',
			format: 'shop',
			context: {},
			touchedAt: '2022-11-08T05:40:05.874Z'
		},
		authorizedRoles: {}
	}
}

const stripe_applepay_pickup = {
	userId: '633d513be622cc001ba216f6',
	payments: [
		{
			provider: 'stripe',
			method: 'applepay',
			amount: 39.2,
			intent: {
				paymentMethodId: 'pm_1M1kZMPp2ttMM5qjfQAGdZYz'
			}
		}
	],
	order: {
		id: '6369ec104b808400005ad87d',
		channel: 'shopify',
		items: [
			{
				variantId: '**************',
				productId: '6902934896780',
				sku: '887167381872',
				title: 'Estee Lauder Nutritious Super Pomegranate 2-in-1 Foam Cleanser - 125ml',
				quantity: 1,
				unitPrice: 39.2,
				price: 39.2,
				taxes: [

				],
				images: [
					'https://cdn.shopify.com/s/files/1/0374/9691/4060/products/PRODUCT-ESTEE_LAUDER-887167381872_3da89e59-3f0a-45e9-a533-7fa41661b84d.jpg?v=**********'
				],
				tags: [
					'all',
					'all skin types',
					'brightening',
					'cleanser',
					'cleansing',
					'dull skin',
					'foam',
					'moisturising',
					'pomegranate',
					'skincare',
					'home'
				],
				properties: [

				],
				options: {

				},
				discount: {
					amount: 0,
					allocations: [

					]
				},
				status: {

				}
			}
		],
		discounts: [

		],
		fulfillment: {
			quantity: 1,
			unitPrice: 0,
			price: 0,
			taxes: [

			],
			images: [

			],
			tags: [

			],
			properties: [

			],
			options: [

			],
			discount: {
				value: 0
			},
			type: 'pickup',
			recipient: {
				fullName: 'Zhang Li',
				phone: '6590490009',
				email: '<EMAIL>'
			},
			destination: {
				id: '632d2fccf77e05001e45bf67',
				type: 'store',
				house: '83',
				level: null,
				unit: null,
				street: 'Punggol Central',
				city: 'Singapore',
				state: 'Punggol',
				postCode: '828761',
				country: 'SG',
				formatted: '83 Punggol Central, #01-50 Singapore 828761',
				short: '83 Punggol Central',
				geo: {
					type: 'Point',
					coordinates: [
						103.90176722359922,
						1.4068736340127657
					]
				},
				optIn: null,
				valid: null,
				name: 'Waterway Point',
				brand: '',
				unitLevel: '',
				external: {

				},
				pickup: {
					available: false,
					timeslot: 60
				},
				deliver: {

				}
			},
			origin: {

			},
			scheduled: {
				minTime: '2022-11-08T08:41:27.710Z'
			},
			note: '5pm'
		},
		totalDiscounts: 0,
		subtotalPrice: 39.2,
		taxIncluded: true,
		taxes: [
			{
				title: 'GST',
				rate: 0.07,
				price: 2.56
			}
		],
		totalTax: 2.56,
		totalPrice: 39.2,
		currency: 'SGD',
		masterId: '5aafe0ea91bf0c4bf986e5cc',
		options: {
			through: {
				type: 'perkd',
				format: 'shopify',
				attributedTo: {
					type: 'shopify'
				},
				touchedAt: '2022-11-08T05:41:36.995Z'
			},
			cardId: '633d51ba3dad42872695bdfc'
		},
		cardId: '633d51ba3dad42872695bdfc',
		idempotencyKey: '8b45c8e0036e'
	},
	pricings: [

	],
	options: {
		masterId: '5aafe0ea91bf0c4bf986e5cc',
		cardId: '633d51ba3dad42872695bdfc',
		description: 'Friends of NOVELA: product',
		through: {
			type: 'perkd',
			format: 'shop',
			context: {

			},
			touchedAt: '2022-11-08T05:41:56.821Z'
		},
		authorizedRoles: {

		}
	}
}

const stripe_alipay_deliver = {
	userId: '633d513be622cc001ba216f6',
	payments: [
		{
			provider: 'stripe',
			method: 'alipay',
			amount: 118.9,
			intent: {

			}
		}
	],
	order: {
		id: '6369ec684b808400005ad88c',
		channel: 'shopify',
		items: [
			{
				variantId: '**************',
				productId: '6902934274188',
				sku: '887167412965',
				title: 'Estee Lauder Micro Essence Fresh with Sakura Ferment - 200ml',
				quantity: 1,
				unitPrice: 118.9,
				price: 118.9,
				taxes: [

				],
				images: [
					'https://cdn.shopify.com/s/files/1/0374/9691/4060/products/PRODUCT-ESTEE_LAUDER-887167412965_cc05e9ac-ee21-47d3-a179-6637c3bf2adf.jpg?v=**********'
				],
				tags: [
					'all',
					'anti-aging',
					'dryness',
					'earth day 2022',
					'hydrating',
					'lotion',
					'oil control',
					'oily skin',
					'sakura ferment filtrate',
					'skincare',
					'home'
				],
				properties: [

				],
				options: {

				},
				discount: {
					amount: 0,
					allocations: [

					]
				},
				status: {

				}
			}
		],
		discounts: [
			{
				offerMasterId: '5f3a95167c3b4d670f439716',
				offerId: '5f3a95167c3b4d670f439716',
				name: 'Free Shipping above $100',
				kind: 'percentage',
				value: 1,
				currency: 'SGD',
				amount: 5,
				use: 'always',
				priority: 5,
				excludeOffers: [

				],
				targetType: 'shipping',
				targetSelection: 'all',
				entitled: {

				},
				allocation: {
					method: 'across',
					limit: 1
				}
			}
		],
		fulfillment: {
			quantity: 1,
			unitPrice: 5,
			price: 0,
			taxes: [

			],
			images: [

			],
			tags: [

			],
			properties: [

			],
			options: [

			],
			discount: {
				value: 5
			},
			type: 'deliver',
			recipient: {
				fullName: 'Zhang Li',
				phone: '6590490009',
				email: '<EMAIL>'
			},
			destination: {
				id: 'l8wgd1mw',
				type: 'others',
				label: 'Ponggol Seventeenth Avenue',
				level: null,
				unit: null,
				street: '247 Ponggol Seventeenth Ave',
				city: 'Singapore',
				state: null,
				postCode: '829704',
				country: 'SG',
				formatted: '247 Ponggol Seventeenth Ave, Singapore 829704',
				short: '247 Ponggol Seventeenth Ave',
				geo: {
					type: 'Point',
					coordinates: [
						103.9039658,
						1.4162215
					]
				},
				optIn: null,
				valid: null
			},
			origin: {

			},
			scheduled: {
				minTime: '2022-11-12T05:42:59.793Z',
				maxTime: '2022-11-13T05:42:59.793Z'
			}
		},
		totalDiscounts: 5,
		subtotalPrice: 118.9,
		taxIncluded: true,
		taxes: [
			{
				title: 'GST',
				rate: 0.07,
				price: 7.78
			}
		],
		totalTax: 7.78,
		totalPrice: 118.9,
		currency: 'SGD',
		masterId: '5aafe0ea91bf0c4bf986e5cc',
		options: {
			through: {
				type: 'perkd',
				format: 'shopify',
				attributedTo: {
					type: 'shopify'
				},
				touchedAt: '2022-11-08T05:43:04.183Z'
			},
			cardId: '633d51ba3dad42872695bdfc'
		},
		cardId: '633d51ba3dad42872695bdfc',
		idempotencyKey: '0fe3b08d7f2a'
	},
	pricings: [

	],
	options: {
		masterId: '5aafe0ea91bf0c4bf986e5cc',
		cardId: '633d51ba3dad42872695bdfc',
		description: 'Friends of NOVELA: product',
		through: {
			type: 'perkd',
			format: 'shop',
			context: {

			},
			touchedAt: '2022-11-08T05:43:10.409Z'
		},
		authorizedRoles: {

		}
	}
}

const mypay_applepay_cstore = {
	userId: '633d513be622cc001ba216f6',
	payments: [
		{
			provider: 'mypay',
			method: 'applepay',
			amount: 239,
			intent: {
				paymentMethod: {
					displayName: 'mypay',
					type: 'credit'
				},
				paymentData: {
					version: 'EC_v1',
					data: 'Vo2d34AM5rHCsHL/QjwYVX0VssP7N+ylBQ/xNdDGiPVz6X48xDq41o1W+ujvWPeLI3wqg+5fU0dm9WdIMl9CDBUq21olAY/667bxV8PICk9eaWz83IbV05z/txDIgBbOEJNcj9IZ9cEkYLHjli0uv/hNj4Hf8LQqGJS4ryu2mKzH5YEgQwWZ+lr5XRRPMxZCe8LFt8uJdAfx9Rkba1E/A6bkhz7z6hPoNGDbSXdRSN/lxz4prYFhHCbd3xAFKEkOi/mfzwkonm+aXJuSqIQ2TX6syinwAg2nyszMGNfE4IYUoJEn7mSeZKlR3wFdo28rz8BR6I4IAxX41if+mnEAr70oARDb46s7VzYtFkDaBqSTt/oilBnvVonQOmoZ3qQmvDga4gVahkE+rNDCKg6gsLd6bDN2cXPHrzl/zNn/wbEq',
					signature: '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',
					header: {
						ephemeralPublicKey: 'MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEIhq86NFTwl5IZ8c6k7xuLK8K06b60Rv1PGfDrzA1a1Eu3p7VPnQ20XQh8p4jkSm52dmn8E4pjgcdSqe+7Y4O0g==',
						publicKeyHash: 'QiDSYwsQdPOYhyhWbwzauKtFeNsgXt46cEnWjCT3XEg=',
						transactionId: 'cf53d3029a1de157396202496895bc9a0bb8b5d7c28eb127d4a64ff2c8f8f634'
					}
				},
				transactionIdentifier: 'CF53D3029A1DE157396202496895BC9A0BB8B5D7C28EB127D4A64FF2C8F8F634'
			}
		}
	],
	order: {
		id: '6369ecaf4b808400005ad899',
		channel: 'shopify',
		items: [
			{
				variantId: '42430312972486',
				productId: '7694789411014',
				sku: 'M052021010401041-1',
				title: 'Bisco百思可 乳酸菌夾心餅 (香草奶油&牛奶&可可) - 3入組',
				quantity: 1,
				unitPrice: 159,
				price: 159,
				taxes: [

				],
				images: [
					'https://cdn.shopify.com/s/files/1/0525/2045/2294/products/bisco-combo-M052021010401041-1_1.png?v=1666082605'
				],
				tags: [
					'奶油',
					'巧克力',
					'牛奶'
				],
				properties: [

				],
				options: {

				},
				discount: {
					amount: 0,
					allocations: [

					]
				},
				status: {

				}
			}
		],
		discounts: [

		],
		fulfillment: {
			quantity: 1,
			unitPrice: 80,
			price: 80,
			taxes: [

			],
			images: [

			],
			tags: [

			],
			properties: [

			],
			options: [

			],
			discount: {
				value: 0
			},
			type: 'pickup',
			recipient: {
				fullName: 'Zhang Li',
				phone: '6590490009',
				email: '<EMAIL>'
			},
			destination: {
				type: 'cstore',
				house: '',
				level: '',
				unit: '',
				street: '市府路',
				district: '信義區',
				city: '台北市',
				state: '',
				postCode: '110',
				country: 'TW',
				formatted: '台北市信義區市府路45號B1(商場編號53)',
				short: '信義區市府路45號B1(商場編號53)',
				geo: {
					type: 'Point',
					coordinates: [
						121.563859,
						25.034089
					]
				},
				id: '5f87ae23f81df224150001ab',
				name: '全家台北101店',
				brand: 'familymart',
				external: {
					serviceCode: '019399',
					storeCode: '019399',
					storeId: '019399'
				},
				pickup: {
					instruction: '取貨時，需出示與取件人信息相同的身份證明文件方可取件'
				}
			},
			origin: {

			},
			scheduled: {
				minTime: '2022-11-15T05:44:12.945Z',
				maxTime: '2022-11-17T05:44:12.945Z'
			}
		},
		totalDiscounts: 0,
		subtotalPrice: 159,
		taxIncluded: true,
		taxes: [
			{
				title: '營業稅',
				rate: 0.05,
				price: 12
			}
		],
		totalTax: 12,
		totalPrice: 239,
		currency: 'TWD',
		masterId: '5f74411a55474214ad9c6350',
		options: {
			through: {
				type: 'perkd',
				format: 'shopify',
				attributedTo: {
					type: 'shopify'
				},
				touchedAt: '2022-11-08T05:44:15.702Z'
			},
			cardId: '633d5c3b3dad42872695bdfe'
		},
		cardId: '633d5c3b3dad42872695bdfe',
		idempotencyKey: 'bdccc6a6ca0d'
	},
	pricings: [

	],
	options: {
		masterId: '5f74411a55474214ad9c6350',
		cardId: '633d5c3b3dad42872695bdfe',
		description: '好友卡: product',
		through: {
			type: 'perkd',
			format: 'shop',
			context: {

			},
			touchedAt: '2022-11-08T05:44:24.988Z'
		},
		authorizedRoles: {

		}
	}
}

const mypay_linepay_deliver = {
	userId: '633d513be622cc001ba216f6',
	payments: [
		{
			provider: 'mypay',
			method: 'linepay',
			amount: 137,
			intent: {

			}
		}
	],
	order: {
		id: '6369ed124b808400005ad8a4',
		channel: 'shopify',
		items: [
			{
				variantId: '**************',
				productId: '7694789345478',
				sku: 'J050000000000798-1',
				title: 'Bisco百思可 香草奶油乳酸菌夾心餅 58g - 單品',
				quantity: 1,
				unitPrice: 57,
				price: 57,
				taxes: [

				],
				images: [
					'https://cdn.shopify.com/s/files/1/0525/2045/2294/products/bisco-butter-J050000000000798-2.png?v=**********'
				],
				tags: [
					'奶油'
				],
				properties: [

				],
				options: {

				},
				discount: {
					amount: 0,
					allocations: [

					]
				},
				status: {

				}
			}
		],
		discounts: [

		],
		fulfillment: {
			quantity: 1,
			unitPrice: 80,
			price: 80,
			taxes: [

			],
			images: [

			],
			tags: [

			],
			properties: [

			],
			options: [

			],
			discount: {
				value: 0
			},
			type: 'deliver',
			recipient: {
				fullName: 'Zhang Li',
				phone: '6590490009',
				email: '<EMAIL>'
			},
			destination: {
				street: '台北市忠孝東路五段',
				state: '台北市',
				country: 'TW',
				formatted: '103台灣台北市忠孝東路五段01樓之03',
				short: '台北市忠孝東路五段01樓之03',
				geo: {
					coordinates: [
						121.5740496,
						25.0409638
					]
				},
				level: '01',
				unit: '03',
				postCode: '103',
				type: 'others',
				label: '忠孝東路五段',
				id: 'la7sgtal'
			},
			origin: {

			},
			scheduled: {
				minTime: '2022-11-11T05:45:43.023Z',
				maxTime: '2022-11-15T05:45:43.023Z'
			},
			note: '測試'
		},
		totalDiscounts: 0,
		subtotalPrice: 57,
		taxIncluded: true,
		taxes: [
			{
				title: '營業稅',
				rate: 0.05,
				price: 7
			}
		],
		totalTax: 7,
		totalPrice: 137,
		currency: 'TWD',
		masterId: '5f74411a55474214ad9c6350',
		options: {
			through: {
				type: 'perkd',
				format: 'shopify',
				attributedTo: {
					type: 'shopify'
				},
				touchedAt: '2022-11-08T05:45:54.458Z'
			},
			cardId: '633d5c3b3dad42872695bdfe'
		},
		cardId: '633d5c3b3dad42872695bdfe',
		idempotencyKey: '63b42a163bde'
	},
	pricings: [

	],
	options: {
		masterId: '5f74411a55474214ad9c6350',
		cardId: '633d5c3b3dad42872695bdfe',
		description: '好友卡: product',
		through: {
			type: 'perkd',
			format: 'shop',
			context: {

			},
			touchedAt: '2022-11-08T05:45:59.440Z'
		},
		authorizedRoles: {

		}
	}
}
