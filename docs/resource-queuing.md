# Resource Queuing
Queuing is part of the Resource Management system of Product Service in the CRM platform.

## Table of Contents
1. [Overview](#overview)
2. [Queue Entry States](#queue-entry-states)
3. [Queue Entry Properties](#queue-entry-properties)
4. [Queue Entry Source Types](#queue-entry-source-types)
5. [Queue Entry Lifecycle](#queue-entry-lifecycle)
6. [Wait Time Estimation](#wait-time-estimation)
7. [Priority System](#priority-system)
8. [Abandoned Entries](#abandoned-entries)
9. [Settings](#settings)

## Overview
The queuing system is designed to manage and schedule resource bookings (such as tables) in the application. It handles customer requests through a systematic process of evaluation, queuing, and resource allocation, ensuring fair and efficient distribution of available resources.

Key Features:
- Request evaluation and resource availability checking
- Dynamic queue management with priority-based scheduling
- Automated resource allocation based on configurable strategies
- Continuous monitoring and reprocessing of queued requests
- Integration with resource management system for real-time availability updates
- Multi-tier priority system (ASAP > VIP > Regular)
- Flexible resource preferences (combined seating, adjacent tables)
- Comprehensive timestamp tracking throughout entry lifecycle
- Event-driven processing for real-time resource availability
- Integration with customer management (Person, Membership)

```mermaid
graph TD
    A[Resource Request] --> B{Resources Available?}

    B -->|Yes| C[Apply Allocation Strategies]
    B -->|No| D[Create Queue Entry]:::queueing

    C --> E{Allocation Successful?}
    E -->|Yes| F[Assign Resources]
    E -->|No| D

    D --> G[Calculate Initial Priority]:::queueing
    G --> H[Add to Queue]:::queueing

    H --> I[Monitor Queue]:::queueing
    I --> J{Resources Available?}:::queueing
    J -->|No| K[Update Priority]:::queueing
    K --> I

    J -->|Yes| L[Process Queue Entry]:::queueing
    L --> C

    F --> M[Notify Customer]
    M --> N[Update Resource Status]

    classDef queueing fill:#00008B,color:#fff;
```

The queuing system integrates with resource availability modules to:
- Process incoming resource booking requests
- Evaluate resource availability in real-time
- Manage queue entries with dynamic priority calculations
- Handle resource allocation and customer notifications
- Monitor and update resource status throughout the booking lifecycle


### Queue Entry States

```mermaid
graph TD
    A[New Request] --> B[WAITING]
    B --> C[ASSIGNED]
    C --> D[NOTIFIED]
    D --> |Customer Confirms| E[CONFIRMED]
    E --> F[COMPLETED]
    B --> |Customer Leaves| G[LEFT]
    B --> |Entry Skipped| H[SKIPPED]
```

| State | Description | Next States | Triggers |
|-------|-------------|-------------|----------|
| `waiting` | Initial state when entry is created | `assigned`, `left`, `skipped` | Resource available, Customer leaves, Entry skipped |
| `assigned` | Resource allocated, pending notification | `notified` | Notification sent |
| `notified` | Customer notified of available resource | `confirmed` | Customer confirms |
| `confirmed` | Customer accepted the resource | `completed` | Service completed |
| `completed` | Queue entry fulfilled | - | - |
| `left` | Customer left the queue | - | - |
| `skipped` | Entry was skipped in queue | - | - |

### Queue Entry Properties
Each queue entry contains the following key properties:

| Property | Description |
|----------|-------------|
| `kind` | Resource type (e.g., 'table') |
| `capacity` | Number of people (party size) |
| `tier` | Customer tier: 'regular', 'vip', or 'asap' |
| `priority` | Calculated priority score, higher is more important |
| `status` | Current state in the lifecycle (see above) |
| `scheduledAt` | Expected service time (adjusted for overstayed resources) |
| `endTime` | Time after which the entry is considered abandoned |
| `hadReservation` | Whether this entry had a reservation that couldn't be fulfilled immediately |
| `originalReservationId` | The original reservation ID if this entry had a reservation |
| `source` | Source of the queue entry (see below) |

### Queue Entry Source Types
Queue entries can be created from different sources:

| Source | Description |
|--------|-------------|
| `walkin` | Walk-in customers joining the queue |
| `noshow` | Re-queued from no-show bookings (customers who had reservations but didn't show up) |
| `overflow` | Overflow from other queues/systems |

## Queue Entry Lifecycle

```mermaid
stateDiagram-v2
    [*] --> Waiting: join()

    state "Entry Creation" as Waiting {
        [*] --> CalculatingPriority
        CalculatingPriority --> ReadyToQueue
    }

    Waiting --> ResourceAssignment: tryAssignResources()

    state "Resource Assignment" as ResourceAssignment {
        [*] --> Validating
        Validating --> Reserving: compatible
        Reserving --> Assigned: success
    }

    ResourceAssignment --> Waiting: assignment failed
    ResourceAssignment --> Notified: assignment success

    Notified --> Confirmed: customer accepts
    Confirmed --> Completed: service done

    Waiting --> Left: customer leaves
    Waiting --> Skipped: entry skipped

    note right of Waiting
        Initial priority based on:
        - Customer tier (regular/vip/asap)
        - Group size
        - Time requested
        - Special requirements
    end note

    note right of ResourceAssignment
        Assignment considers:
        - Resource availability
        - Customer preferences
        - Combined seating
        - Adjacent tables
    end note
```

Each queue entry maintains a comprehensive set of timestamps in its lifecycle:

| Timestamp | Description |
|-----------|-------------|
| waited | Time when customer joined the queue |
| notified | Time when customer was notified of resource availability |
| confirmed | Time when customer confirmed acceptance |
| completed | Time when service was completed |
| skipped | Time when entry was skipped in queue |
| left | Time when customer left the queue |

All timestamps are stored in the place's local timezone and are used for:
- Queue analytics and reporting
- Wait time calculations
- Priority adjustments
- Service level monitoring
- Queue performance optimization

## Wait Time Estimation
The wait time estimation system calculates expected waiting times based on current queue state and resource availability:

```mermaid
graph TD
    %% Resource States
    RS[Resource State]
    OH[Operating Hours]
    CO[Current Orders]
    CB[Current Bookings]
    FB[Future Bookings]

    %% Calculations
    IA[Initial Available Time]
    VA[Validate Slots]
    MD[Min Duration Check]
    RC[Resource Combinations]
    AT[Available Time]
    NA[No Availability]

    %% Resource Analysis
    subgraph Analysis
        RS
        OH
        CO
        CB
        FB
    end

    %% Availability Check
    subgraph Calculation
        IA
        VA
        MD
    end

    %% Assignment
    subgraph Assignment
        RC
        AT
    end

    %% Flow
    RS --> IA
    OH --> IA
    CO --> IA
    CB --> IA
    FB --> VA
    IA --> VA
    VA --> MD
    MD -->|Valid| RC
    MD -->|Invalid| NA
    RC -->|Compatible| AT
    RC -->|Incompatible| NA
```

1. **Resource Timing Analysis**
   - **Resource State**
     - Basic resource information (capacity, position)
     - Operating hours and time zone settings
     - Current availability status

   - **Active Bookings**
     - `currentOrders`: Active orders with `estimatedEndsAt` times
     - `currentBookings`: Ongoing bookings (started but not ended)
     - `futureBookings`: Upcoming bookings not yet started

2. **Availability Calculation**
   - **Initial Availability**
     - Calculates `initialAvailableAt` as the latest of:
       - Current time in resource's timezone
       - End times of all current orders
       - End times of all current bookings

   - **Future Slot Finding**
     - Processes `futureBookings` sequentially to find valid slots
     - Checks if time until next booking meets minimum duration
     - Updates `availableAt` and `nextUnavailableAt` times
     - Considers booking transitions and preparation time

3. **Queue Processing**
   - Retrieves waiting entries in priority order
   - For each entry:
     1. Gets resource timings via `Resource.getTimings()`
     2. Finds earliest available time using `Resource.earliestAvailableTime()`
        - Handles overstayed resources by applying a buffer time
        - Prioritizes non-overstayed resources when times are equal
     3. Considers resource combinations if allowed
     4. Updates scheduled times based on availability

4. **Dynamic Updates**
   System recalculates when:
   - New entries join the queue
   - Orders or bookings end
   - Resource status changes
   - Queue entries are processed

The estimation process:
- Uses actual booking data from multiple sources
- Maintains accurate timeline of resource utilization
- Handles resource combinations and adjacent seating
- Considers minimum duration requirements
- Accounts for timezone-specific calculations
- Handles overstayed resources with buffer times

### Handling Overstayed Resources

The system has special handling for overstayed resources (customers staying beyond their booked end time):

1. **Detection**
   - Resources are considered overstayed when their `availableAt` time is in the past
   - This happens when bookings have ended but customers haven't departed

2. **Buffer Time Application**
   - When overstayed resources are detected, a buffer time is applied
   - Buffer is calculated as a percentage (20%) of the minimum duration
   - This provides more realistic wait time estimates

3. **Prioritization**
   - When multiple resource combinations have the same adjusted available time:
     - Combinations with fewer overstayed resources are preferred
     - This reduces dependency on unpredictable overstay durations

## Priority System

```mermaid
graph LR
    A[Queue Entry] --> B[Base Priority]

    T1[ASAP] --> |Highest| P[Priority Score]
    T2[VIP] --> |Medium| P
    T3[Regular] --> |Lowest| P

    B --> |Tier Lookup| P

    W[Wait Time] --> |Factor & Cap| WB[Wait Bonus]
    R[Reservation History] --> |Bonus Points| RB[Reservation Bonus]

    P & WB & RB --> FP[Final Priority]

    style T1 fill:#cc0000
    style T2 fill:#cc6600
    style T3 fill:#999900
```

The priority system implements a sophisticated scoring mechanism that combines:

1. **Base Tier Priority**
   - ASAP: Highest base priority (immediate seating needed)
   - VIP: Medium base priority (privileged customers)
   - Regular: Lowest base priority (default tier)

2. **Wait Time Bonus**
   - Increases linearly with waiting duration
   - Bonus = min(waitTime × factor, maxBonus)
   - Prevents starvation of lower-tier entries
   - Configurable bonus factor and maximum

3. **Reservation History Bonus**
   - Additional points for customers who had reservations
   - Applied when `hadReservation` is true
   - Ensures customers with reservations maintain priority
   - Configurable bonus amount

4. **Final Priority**
   - Combined score of tier priority, wait bonus, and reservation bonus
   - Higher scores get processed first
   - Used for queue ordering and processing
   - Dynamically updated during queue monitoring

The system ensures:
- Immediate handling of ASAP requests when possible
- Preferential treatment of VIP customers
- Fair progression of Regular customers based on wait time
- No permanent starvation of any tier
- Configurable balance between tier priority and wait time

### Examples

Given these settings:
```json
{
  "priority": {
    "asap": 100,
    "vip": 50,
    "regular": 10,
    "waitingTime": {
      "factor": 0.5,
      "maxBonus": 30
    },
    "reservationBonus": 10
  }
}
```

Example calculations:

| Customer | Tier | Wait Time | Had Reservation | Calculation | Final Priority |
|----------|------|-----------|----------------|-------------|----------------|
| A | ASAP | 5 min | No | 100 + min(5 × 0.5, 30) + 0 = 102.5 | 102.5 |
| B | VIP | 20 min | No | 50 + min(20 × 0.5, 30) + 0 = 60 | 60 |
| C | Regular | 60 min | No | 10 + min(60 × 0.5, 30) + 0 = 40 | 40 |
| D | Regular | 120 min | No | 10 + min(120 × 0.5, 30) + 0 = 40 | 40 |
| E | Regular | 10 min | Yes | 10 + min(10 × 0.5, 30) + 10 = 25 | 25 |
| F | VIP | 5 min | Yes | 50 + min(5 × 0.5, 30) + 10 = 62.5 | 62.5 |

Notes:
- Customer A (ASAP): Gets highest base priority (100) plus small wait bonus
- Customer B (VIP): Medium base priority (50) with moderate wait bonus
- Customer C (Regular): Low base priority (10) with significant wait bonus
- Customer D (Regular): Shows how wait bonus is capped at maximum (30)
- Customer E (Regular with Reservation): Gets additional 10 points for reservation history
- Customer F (VIP with Reservation): Shows how reservation bonus stacks with tier and wait time


## Abandoned Entries

The system automatically handles abandoned queue entries to maintain an accurate and efficient queue. Abandoned entries are those where the customer is presumed to have left without notification.

```mermaid
graph TD
    A[Queue Entry] --> B[scheduledAt Time]
    B --> C[endTime = scheduledAt + maxWaitTime]

    J[Resource Availability Changes] --> K[Update scheduledAt]
    K --> L[endTime Remains Fixed]

    M[Booking Event Occurs] --> N[Process Abandoned Entries]
    N --> O{Current Time > endTime?}
    O -->|Yes| P[Mark as LEFT]
    O -->|No| Q[Keep in Queue]
    P --> R[Update Metrics]
    Q --> S[Process Queue]
```

### Abandonment Detection

The system uses the following approach to detect and handle abandoned entries:

1. **Maximum Wait Time**
   - Each queue entry has an `endTime` calculated as `scheduledAt + maxWaitTime` when created
   - The `maxWaitTime` is configurable (default: 60 minutes)
   - After this time, the entry is considered abandoned
   - The `endTime` remains fixed even when `scheduledAt` is updated due to changing priorities or resource availability
   - This ensures a consistent abandonment policy regardless of dynamic rescheduling

2. **Event-Driven Processing**
   - Abandoned entries are processed when booking events occur
   - This happens when bookings end, are marked as no-show, or are deleted
   - Entries with `endTime` in the past are marked as `left`
   - The `when.left` timestamp is set to the current time
   - Metrics are updated to reflect the abandoned entry

3. **Integration Points**
   - Abandoned entries are processed before regular queue processing
   - This ensures resources aren't allocated to customers who have likely left
   - Triggered by booking status changes and deletions

### Benefits

- Maintains accurate queue state
- Prevents allocating resources to absent customers
- Improves wait time estimates for active customers
- Provides data for queue analytics and optimization
- Reduces staff workload by automating abandonment handling

## Settings
The queuing system's behavior is controlled through configuration settings that can be adjusted per resource kind (e.g., TABLE) to accommodate different business requirements.

### QUEUING Settings
Core settings that control the queuing system behavior:

| Setting | Description | Type | Unit |
|---------|-------------|------|------|
| `enabled` | Whether queuing is enabled for the resource | boolean | - |
| `holdTime` | Time for customer confirmation before releasing table | number | milliseconds |
| `validity` | How long queue entries remain valid | number | minutes |
| `maxWaitTime` | Maximum time a customer should wait after scheduledAt | number | minutes |
| `duration` | Dining duration settings | object | - |
| `priority` | Priority calculation settings | object | - |
| `minDuration` | Minimum duration required for resource allocation | number | minutes |

#### Duration Settings
Controls the expected dining duration based on time of day:

| Setting | Description | Type | Unit |
|---------|-------------|------|------|
| `default` | Default dining duration | number | minutes |
| `peak` | Peak dining duration | number | minutes |
| `offPeak` | Off-peak dining duration | number | minutes |

#### Priority Settings
Configures how queue entry priorities are calculated:

| Setting | Description | Type | Unit |
|---------|-------------|------|------|
| `asap` | Base score for immediate seating | number | score |
| `vip` | Base score for VIP | number | score |
| `regular` | Base score for regular | number | score |
| `waitingTime` | Priority settings for waiting time | object | - |
| `factor` | Multiplier on waiting time | number | multiplier |
| `maxBonus` | Capped bonus score | number | score |
| `reservationBonus` | Bonus points for customers who had reservations | number | score |

### LOCALE Settings
Settings that control regional and timezone behavior:

| Setting | Description | Type | Unit |
|---------|-------------|------|------|
| `timeZone` | Default business timezone for scheduling | string | IANA timezone |

The timezone setting affects all timestamp fields in queue entries:
- Entry creation time
- Wait start time
- Notification time
- Confirmation time
- Completion time
- Skip/Left timestamps

### Resource Preferences
Optional settings that can be specified per queue entry:

| Setting | Description | Type | Unit |
|---------|-------------|------|------|
| `allowCombined` | Allow combining multiple resources | boolean | - |
| `adjacentOnly` | Require adjacent resources when combined | boolean | - |

### Queue Processing
System-level settings that control queue processing behavior:

| Setting | Description | Type | Unit |
|---------|-------------|------|------|
| `immediateInterval` | Processing interval for immediate retries | number | milliseconds |
| `periodicInterval` | Processing interval for periodic checks | number | milliseconds |
